package com.czur.starry.device.noticelib.floating

import android.widget.TextView
import com.czur.starry.device.baselib.utils.view.findView
import com.czur.starry.device.baselib.widget.CommonButton
import com.czur.starry.device.noticelib.NotifyMsgHandler
import com.czur.starry.device.noticelib.R

/**
 * Created by 陈丰尧 on 2021/9/6
 * 带按钮的通知悬浮窗
 */
class BtnFloatNotice(
    private val notifyMsg: NotifyMsg,
    private val onActionClick: (notifyMsg: NotifyMsg) -> Unit,
) : FloatNotice() {
    override fun getLayoutId(): Int = R.layout.float_notice_btn

    private val floatNoticeMsgTv: TextView by findView(R.id.floatNoticeMsgTv)
    private val floatNoticeBtn: CommonButton by findView(R.id.floatNoticeBtn)


    override fun initView() {
        super.initView()

        floatNoticeMsgTv.text = notifyMsg.title
        floatNoticeBtn.text = NotifyMsgHandler.getNotifyMsgBtnText(resources, notifyMsg.type)
        floatNoticeBtn.setOnClickListener {
            onActionClick(notifyMsg)
            dismiss()
        }
    }
}