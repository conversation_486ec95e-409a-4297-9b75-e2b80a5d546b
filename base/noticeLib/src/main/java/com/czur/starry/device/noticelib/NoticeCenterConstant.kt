package com.czur.starry.device.noticelib

import com.czur.starry.device.baselib.utils.PathWrapper

/**
 * Created by 陈丰尧 on 2021/7/3
 */

const val NOTIFY_MSG_AUTHORITY = "com.czur.starry.device.noticecenter.provider.NotifyMsgProvider"

val PATH_HAS_UNREAD_MSG = PathWrapper("unReadMsg", 0, "未读消息")

const val QUERY_KEY_HAS_UN_READ_MSG = "hasUnReadMsg"

const val NOTICE_TYPE_UPDATE = 1            // 版本升级
const val NOTICE_TYPE_INVITED_COMPANY = 2   // 邀请加入企业
const val NOTICE_TYPE_EXIT_COMPANY = 3      // 踢出企业
const val NOTICE_TYPE_MEMBERS_EXIT = 4      // 成员退出
const val NOTICE_TYPE_SCHEDULE = 5          // 日程
const val NOTICE_TYPE_EXPIRE = 6            // 会员到期
const val NOTICE_TYPE_OTHER = 7             // 其他
const val NOTICE_TYPE_USER_REGISTER = 8     // 新用户注册
const val NOTICE_TYPE_OFFICIAL_NOTICE = 9   // 官方通知

const val NOTICE_STATUS_UNREAD = 0          // 未读
const val NOTICE_STATUS_READ = 1            // 已读
const val NOTICE_STATUS_PROCESS = 2         // 已处理

const val NOTICE_MSG_ID = "id"
const val NOTICE_MSG_TITLE = "title"
const val NOTICE_MSG_TYPE = "type"
const val NOTICE_MSG_DATA = "data"
const val NOTICE_MSG_RECEIVE_TIME = "receiveTime"