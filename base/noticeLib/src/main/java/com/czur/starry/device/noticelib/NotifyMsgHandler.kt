package com.czur.starry.device.noticelib

import com.czur.czurutils.log.logTagD
import android.content.UriMatcher
import android.database.ContentObserver
import android.net.Uri
import android.os.Handler
import android.os.Looper
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.liveData
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.utils.getBoolean
import com.czur.starry.device.baselib.utils.makeUri
import com.czur.starry.device.baselib.utils.query
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2021/7/3
 * Launcher 页面上的消息通知
 */
object NotifyMsgHandler : NotifyMsgProcess by NotifyMsgProcessImpl() {
    private const val TAG = "NotifyMsgHandler"

    private val resolver by lazy {
        val context = CZURAtyManager.appContext
        context.contentResolver
    }

    val uriMatcher = UriMatcher(UriMatcher.NO_MATCH).apply {
        addURI(NOTIFY_MSG_AUTHORITY, PATH_HAS_UNREAD_MSG.path, PATH_HAS_UNREAD_MSG.code)
    }

    // 未读消息
    val hasUnReadMsgLive: LiveData<Boolean> by lazy {
        liveData(Dispatchers.IO) {
            emit(getHasUnRead())
            registerObserver()
        }
    }

    /**
     * 手动刷新是否有未读消息
     */
    suspend fun refreshHasUnRead() {
        val hasUnRead = getHasUnRead()
        (hasUnReadMsgLive as MutableLiveData).value = hasUnRead
    }

    private suspend fun getHasUnRead(): Boolean {
        val cursor = withContext(Dispatchers.IO) {
            resolver.query(NOTIFY_MSG_AUTHORITY, PATH_HAS_UNREAD_MSG)
        }
        val result = cursor?.use {
            it.getBoolean()
        } ?: false
        logTagD(TAG, "HasUnRead:${result}")
        return result
    }

    private fun registerObserver() {
        logTagD(TAG, "注册未读消息监听")
        resolver.registerContentObserver(
            makeUri(NOTIFY_MSG_AUTHORITY, PATH_HAS_UNREAD_MSG),
            true,
            object : ContentObserver(Handler(Looper.getMainLooper())) {
                override fun onChange(selfChange: Boolean, uri: Uri?) {
                    super.onChange(selfChange, uri)
                    logTagD(TAG, "收到了回调:${uri}")
                    val hasUnReadMsg =
                        uri?.getBooleanQueryParameter(
                            QUERY_KEY_HAS_UN_READ_MSG, false
                        ) ?: false
                    (hasUnReadMsgLive as MutableLiveData).value = hasUnReadMsg
                }
            })
    }
}