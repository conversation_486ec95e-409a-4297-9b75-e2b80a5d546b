package com.czur.starry.device.noticelib

import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagD
import android.content.Context
import android.content.Intent
import android.content.res.Resources
import android.util.Log
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_NOTICE_SETTING
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_PERSONAL
import com.czur.starry.device.baselib.common.BootParam.BOOT_KEY_SETTING_PAGE_MENU_KEY
import com.czur.starry.device.baselib.network.HttpManager


import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2021/7/6
 */
private const val TAG = "NotifyMsgProcess"

const val ACTION_TYPE_POSITIVE = 1
const val ACTION_TYPE_NEGATIVE = 0

interface NotifyMsgProcess {
    /**
     * 通知消息是否有按钮
     */
    fun notifyMsgHasAction(type: Int): Boolean

    /**
     * 获取通知上按钮的对应文字
     */
    fun getNotifyMsgBtnText(res: Resources, type: Int): String

    /**
     * 处理对应的消息
     * 1. 被企业邀请  ->   加入企业
     * 2. 会员到期    ->   跳转到个人中心
     * 3. 版本升级    ->   跳转到升级页面
     *
     */
    suspend fun processNotifyMsg(
        context: Context,
        noticeId: Long,
        type: Int,
        actionType: Int,
        data: String?,
    ): Boolean

}

internal class NotifyMsgProcessImpl : NotifyMsgProcess {

    private val noticeService: INoticeProcessService by lazy {
        HttpManager.getService()
    }

    /**
     * 通知消息是否有按钮
     */
    override fun notifyMsgHasAction(type: Int): Boolean {
        return type == NOTICE_TYPE_UPDATE || type == NOTICE_TYPE_INVITED_COMPANY || type == NOTICE_TYPE_EXPIRE
    }

    override fun getNotifyMsgBtnText(res: Resources, type: Int): String {
        return when (type) {
            NOTICE_TYPE_UPDATE -> res.getString(R.string.notify_msg_action_upgrade)
            NOTICE_TYPE_INVITED_COMPANY -> res.getString(R.string.notify_msg_action_agree)
            NOTICE_TYPE_EXPIRE -> res.getString(R.string.notify_msg_action_look_up)
            else -> ""
        }
    }

    override suspend fun processNotifyMsg(
        context: Context,
        noticeId: Long,
        type: Int,
        actionType: Int,
        data: String?
    ): Boolean {
        logTagV(TAG, "processNotifyMsg, type:${type}")
        val result = when (type) {
            NOTICE_TYPE_UPDATE -> {
                // 升级通知, 目前用不上了
                val intent = Intent().apply {
                    action = ACTION_BOOT_NOTICE_SETTING
                    putExtra(BOOT_KEY_SETTING_PAGE_MENU_KEY, "update")
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                }
                context.startActivity(intent)
                false
            }
            NOTICE_TYPE_INVITED_COMPANY -> {
                // 加入指定企业
                logTagW(TAG, "加入企业消息(已过期), data:$data")
                true
            }
            NOTICE_TYPE_EXPIRE -> {
                // 启动个人中心
                when (actionType) {
                    ACTION_TYPE_POSITIVE -> {
                        val intent = Intent().apply {
                            action = ACTION_BOOT_PERSONAL
                            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                        }
                        context.startActivity(intent)
                    }
                    else -> {
                        logTagV(TAG,"会员到期消息, 忽略")
                    }  // 不做任何事情
                }

                true
            }
            else -> false
        }
        withContext(Dispatchers.IO) {
            // 将Notice标记为已处理
            noticeService.updateNoticesStatus(noticeId, NOTICE_STATUS_PROCESS)
        }
        // 手动刷新未读消息
        NotifyMsgHandler.refreshHasUnRead()
        return result
    }

}
