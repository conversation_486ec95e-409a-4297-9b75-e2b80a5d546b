package com.czur.starry.device.noticelib.hwconn

import android.content.Context
import android.content.Intent

/**
 * Created by 陈丰尧 on 2023/11/24
 */
object HwConnHelper {
    private const val ALERT_PACKAGE_NAME = "com.czur.starry.device.noticecenter"
    private const val ALERT_CLASS_NAME =
        "com.czur.starry.device.noticecenter.hw.HWConnectAlertWindow"
    const val KEY_HW_CONN_TYPE = "type"

    enum class HwConnType {
        @Deprecated("旧版手写板")
        TOUCH_BOARD_V1,     // 旧版手写板
        TOUCH_BOARD_V2,     // 新版手写板
        BT_KEYBOARD,        // 蓝牙键盘
        CLICK_DROP_TYPE_C,  // TypeC的投屏器
        CLICK_DROP_TYPE_A,  // TypeA的投屏器
        SCREEN_SAVER,  // 欢庆屏保设定成功
        WRITE_PAD,          // 手写板
    }

    fun showHwConnAlert(context: Context, type: HwConnType) {
        val intent = Intent()
        intent.setClassName(ALERT_PACKAGE_NAME, ALERT_CLASS_NAME)
        intent.putExtra(KEY_HW_CONN_TYPE, type.name)
        context.startService(intent)
    }

    fun dismissHwConnAlert(context: Context) {
        val intent = Intent()
        intent.setClassName(ALERT_PACKAGE_NAME, ALERT_CLASS_NAME)
        context.stopService(intent)
    }
}