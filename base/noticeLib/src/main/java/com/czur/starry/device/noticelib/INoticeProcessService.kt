package com.czur.starry.device.noticelib

import com.czur.starry.device.baselib.network.core.MiaoHttpEntity
import com.czur.starry.device.baselib.network.core.MiaoHttpParam
import com.czur.starry.device.baselib.network.core.MiaoHttpPost

/**
 * Created by 陈丰尧 on 2021/7/6
 */
interface INoticeProcessService {
    /**
     * 改变消息状态
     * @param id 消息的ID, 如果传入0, 表示已读所有信息
     * @param status 要更新的状态: 0 未读; 1已读;2已处理
     */
    @MiaoHttpPost("/api/starry/notice/updateNoticesStatus")
    fun updateNoticesStatus(
        @MiaoHttpParam("id") id: Long,
        @MiaoHttpParam("status") status: Int,
        clazz: Class<Boolean> = Boolean::class.java
    ): MiaoHttpEntity<Boolean>
}