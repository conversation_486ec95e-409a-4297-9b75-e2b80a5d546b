<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="80px"
    android:maxWidth="470px"
    android:minWidth="350px"
    android:paddingLeft="19px"
    app:bl_corners_radius="10px"
    app:bl_solid_color="#698FED"
    tools:background="#698FED">

    <TextView
        android:id="@+id/floatNoticeTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:text="@string/notify_title"
        android:textColor="@color/white"
        android:textSize="20px"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/floatNoticeMsgTv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/floatNoticeMsgTv"
        android:layout_width="wrap_content"
        android:maxWidth="296px"
        android:minWidth="176px"
        android:layout_height="wrap_content"
        android:layout_marginTop="5px"
        android:includeFontPadding="false"
        android:textColor="@color/white"
        android:textSize="16px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@id/floatNoticeTitleTv"
        app:layout_constraintTop_toBottomOf="@id/floatNoticeTitleTv"
        tools:text="新投影仪赠送3个月视频会议!"
        app:layout_constraintRight_toLeftOf="@id/floatNoticeBtn"
        android:layout_marginRight="35px"
        android:ellipsize="end"
        android:lines="1"
        />

    <com.czur.starry.device.baselib.widget.CommonButton
        android:id="@+id/floatNoticeBtn"
        android:layout_width="100px"
        android:layout_height="40px"
        android:textSize="16px"
        android:textStyle="bold"
        app:baselib_theme="light"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="同意"
        android:layout_marginRight="20px"/>

</androidx.constraintlayout.widget.ConstraintLayout>