<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="80px"
    android:maxWidth="570px"
    android:minWidth="350px"
    android:paddingLeft="19px"
    app:bl_corners_radius="10px"
    app:bl_solid_color="#698FED"
    tools:background="#698FED"
    tools:ignore="PxUsage">

    <TextView
        android:id="@+id/floatNoticeTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:text="@string/notify_title"
        android:textColor="@color/white"
        android:textSize="20px"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/floatNoticeMsgTv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/floatNoticeMsgTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5px"
        android:ellipsize="end"
        android:includeFontPadding="false"
        android:lines="1"
        android:maxWidth="530px"
        android:paddingRight="35px"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="16px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@id/floatNoticeTitleTv"
        app:layout_constraintTop_toBottomOf="@id/floatNoticeTitleTv"
        tools:text="新投影仪赠送3个月视频会议新投影仪赠送3个月视频会议新投影仪赠送3个月视频会议新投影仪赠送3个月视频会议新投影仪赠送3个月视频会议新投影仪赠送3个月视频会议新投影仪赠送3个月视频会议新投影仪赠送3个月视频会议!" />


</androidx.constraintlayout.widget.ConstraintLayout>