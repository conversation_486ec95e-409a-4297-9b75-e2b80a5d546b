package com.czur.starry.device.bluetoothlib.util

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2024/04/03
 */


class BluetoothProfileProxy {
    companion object {
        const val CONNECTION_POLICY_ALLOWED = 100
        const val CONNECTION_POLICY_FORBIDDEN = 0
        const val A2DP_SINK = 11
        const val PBAP_CLIENT = 17
        const val PAN = 5
        const val OPP = 20
        const val MAP = 9
        const val HID_HOST = 4
        const val PBAP = 6
        const val HEADSET_CLIENT = 16
        const val MAP_CLIENT = 18
    }
}