/*
 * Copyright (C) 2011 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.czur.starry.device.bluetoothlib.bluetooth;

import android.bluetooth.BluetoothClass;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothProfile;

import com.czur.starry.device.bluetoothlib.util.BluetoothProfileProxy;


/**
 * OppProfile handles Bluetooth OPP.
 */
final class OppProfile implements LocalBluetoothProfile {

    static final String NAME = "OPP";

    // Order of this profile in device profiles list
    private static final int ORDINAL = 2;

    public boolean accessProfileEnabled() {
        return false;
    }

    public boolean isAutoConnectable() {
        return false;
    }

    public int getConnectionStatus(BluetoothDevice device) {
        return BluetoothProfile.STATE_DISCONNECTED; // Settings app doesn't handle OPP
    }

    @Override
    public boolean isEnabled(BluetoothDevice device) {
        return false;
    }

    @Override
    public int getConnectionPolicy(BluetoothDevice device) {
        return BluetoothProfileProxy.CONNECTION_POLICY_FORBIDDEN; // Settings app doesn't handle OPP
    }

    @Override
    public boolean setEnabled(BluetoothDevice device, boolean enabled) {
        return false;
    }

    public boolean isProfileReady() {
        return true;
    }

    @Override
    public int getProfileId() {
        return BluetoothProfileProxy.OPP;
    }

    public String toString() {
        return NAME;
    }

    public int getOrdinal() {
        return ORDINAL;
    }

    public int getNameResource(BluetoothDevice device) {
        return 0;
    }

    public int getSummaryResourceForDevice(BluetoothDevice device) {
        return 0;   // OPP profile not displayed in UI
    }

    public int getDrawableResource(BluetoothClass btClass) {
        return 0;   // no icon for OPP
    }
}
