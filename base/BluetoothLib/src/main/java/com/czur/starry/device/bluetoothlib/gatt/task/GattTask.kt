package com.czur.starry.device.bluetoothlib.gatt.task

import android.bluetooth.BluetoothGatt

/**
 * Created by 陈丰尧 on 2022/11/3
 */
abstract class GattTask(
    val needRetry: Boolean = false
) {
    companion object {
        internal const val PROCESS_TIME_IMMEDIATE = -1L  // 立刻执行
    }

    /**
     * 任务的执行时间
     */
    internal var processTime: Long = PROCESS_TIME_IMMEDIATE

    /**
     * 是否已经重试过
     */
    var hasRetry = false

    abstract val name: String

    /**
     * 执行任务
     */
    abstract fun processTask(gatt: BluetoothGatt): Boolean
}