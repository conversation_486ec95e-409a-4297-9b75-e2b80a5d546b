package com.czur.starry.device.bluetoothlib.gatt

import android.annotation.SuppressLint
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.czurutils.log.logTagD
import android.bluetooth.*
import android.content.Context
import android.util.Log
import com.czur.czurutils.log.logStackTrace
import com.czur.starry.device.baselib.utils.*
import com.czur.starry.device.bluetoothlib.gatt.task.GattTask
import com.czur.starry.device.bluetoothlib.gatt.task.ServiceDiscoveredDelayTask
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.filter
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.EmptyCoroutineContext

/**
 * Created by 陈丰尧 on 2022/11/3
 */
@SuppressLint("MissingPermission")
abstract class GattManager(
    protected val scope: CoroutineScope,
    protected val device: BluetoothDevice
) {
    companion object {
        private const val TAG = "GattManager"
        private const val DEF_GATT_TASK_INTERVAL = 500L   // 每个任务间隔
    }

    enum class GattManagerStatus {
        INIT,           // 初始化
        CONNECTED,      // 已连接
        DISCONNECTED,   // 断开连接
        RECYCLE,        // 是否资源
    }

    open val gattTaskInterval = DEF_GATT_TASK_INTERVAL

    private var btGatt: BluetoothGatt? = null
    private var startProcessTaskJob: Job? = null

    private var watchDiscoverJob: Job? = null

    // 当前状态
    private var managerStatus = GattManagerStatus.INIT
        set(value) {
            if (field == value) return
            field = value
            when (field) {
                GattManagerStatus.RECYCLE -> doOnRecycle()
                else -> {}
            }
        }

    private val taskChannel: MutableSharedFlow<GattTask> = MutableSharedFlow(
        extraBufferCapacity = 64,
        onBufferOverflow = BufferOverflow.DROP_OLDEST
    )

    init {
        startProcessTask()
    }

    /**
     * 开始处理执行任务
     */
    private fun startProcessTask() {
        startProcessTaskJob = scope.launch {
            taskChannel
                .filter { managerStatus != GattManagerStatus.RECYCLE }  // Recycler之后的任务都不执行
                .minimumInterval(gattTaskInterval)    // 每个任务之间是需要有间隔的
                .collect { task ->
                    if (task.processTime > System.currentTimeMillis()) {
                        // task 还不到执行时间, 重新插入队列中
                        addGattTask(task)
                        return@collect
                    }

                    val result = btGatt?.let {
                        task.processTask(it)
                    } ?: false
                    logTagV(TAG, "执行ble任务:${task.name},result:${result}")

                    if (!result && task.needRetry) {
                        logTagW(TAG, "task:${task.name} 执行任务失败")
                        // 任务执行失败
                        if (!task.hasRetry) {
                            task.hasRetry = true
                            addGattTask(task)   // 重新加入队列
                        } else {
                            logTagW(TAG, "task:${task.name} 重试后仍然失败")
                        }
                    }
                }
        }
    }

    fun connect(context: Context) {
        btGatt = device.connectGatt(context, false, object : BluetoothGattCallback() {
            override fun onConnectionStateChange(gatt: BluetoothGatt, status: Int, newState: Int) {
                super.onConnectionStateChange(gatt, status, newState)
                logTagW(TAG, "Gatt连接状态更变:${status} - $newState - ${device.name}")
                if (newState == BluetoothProfile.STATE_CONNECTED) {
                    logTagD(TAG, "连接Gatt成功, 开始发现Service")
                    changManagerStatus(GattManagerStatus.CONNECTED)
                    gatt.discoverServices()
                    watchDiscoverJob = scope.launch {
                        delay(5 * ONE_SECOND)
                        logTagW(TAG, "还没有discoverService")
                        connect(context)
                    }
                } else if (newState == BluetoothProfile.STATE_DISCONNECTED) {
                    //重连蓝牙时候,每次都会重新启用一个新的gatt,所以每次断开连接都close这个gatt连
                    logTagW(TAG, "Gatt连接断开,调用close()")
//                    btGatt?.disconnect()
                    disconnect()
                } else {// 不知道连接状态异常时候,会不会newState都是2,先使用体验一下
                    logTagW(TAG, "Gatt连接状态异常:${status} - $newState - ${device.name}")
                    if (managerStatus != GattManagerStatus.RECYCLE) {
                        changManagerStatus(GattManagerStatus.DISCONNECTED)
                        // TODO 重新连接
                        logTagW(TAG, "尝试重新连接")
                        gatt.connect()
                    }
                }
            }

            override fun onServicesDiscovered(gatt: BluetoothGatt, status: Int) {
                super.onServicesDiscovered(gatt, status)
                logTagD(TAG, "onServicesDiscovered:${status}")
                watchDiscoverJob?.cancel()
                addGattTask(ServiceDiscoveredDelayTask())   // 触发延迟的空任务
                doOnServiceDiscovered(gatt)
            }

            override fun onCharacteristicRead(
                gatt: BluetoothGatt?,
                characteristic: BluetoothGattCharacteristic,
                status: Int
            ) {
                super.onCharacteristicRead(gatt, characteristic, status)
                onCharacteristicRead(characteristic)
            }

            override fun onCharacteristicChanged(
                gatt: BluetoothGatt?,
                characteristic: BluetoothGattCharacteristic
            ) {
                super.onCharacteristicChanged(gatt, characteristic)
                onCharacteristicChanged(characteristic)
            }
        }, BluetoothDevice.TRANSPORT_LE)
    }

    fun disconnect() {
        if (managerStatus == GattManagerStatus.RECYCLE) {
            logTagV(TAG, "已经释放资源了, 不需要再次释放")
            return
        }
        logTagV(TAG, "disconnect - managerStatus: $managerStatus")
        managerStatus = GattManagerStatus.RECYCLE
        btGatt?.close()
        btGatt = null
        startProcessTaskJob?.cancel()
        startProcessTaskJob = null
    }

    /**
     * 给子类使用的的, 释放资源时调用
     */
    protected open fun doOnRecycle() {}

    /**
     * 服务发现成功后的回调
     */
    abstract fun doOnServiceDiscovered(gatt: BluetoothGatt)

    open fun onCharacteristicRead(characteristic: BluetoothGattCharacteristic) {}

    open fun onCharacteristicChanged(characteristic: BluetoothGattCharacteristic) {}

    protected fun changManagerStatus(status: GattManagerStatus) {
        if (managerStatus == GattManagerStatus.RECYCLE) {
            // 已经开始释放资源的时候, 禁止迁移状态了
            return
        }
        managerStatus = status
    }

    /**
     * 添加任务
     */
    fun addGattTask(vararg tasks: GattTask) {
        if (managerStatus == GattManagerStatus.RECYCLE) return
        scope.launch {
            tasks.forEach {
                taskChannel.emit(it)
            }
        }
    }

    /**
     * 添加一个延迟任务
     */
    fun addGattTaskDelay(task: GattTask, delayInMillisecond: Long) {
        if (delayInMillisecond > 0) {
            task.processTime = System.currentTimeMillis() + delayInMillisecond
        }
        addGattTask(task)
    }


    protected fun launch(
        context: CoroutineContext = EmptyCoroutineContext,
        start: CoroutineStart = CoroutineStart.DEFAULT,
        block: suspend CoroutineScope.() -> Unit,
    ) = scope.launch(context, start, block)
}
