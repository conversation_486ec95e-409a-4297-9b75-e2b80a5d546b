package com.czur.starry.device.bluetoothlib.util

import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothHeadset

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2024/04/07
 */


class BluetoothHeadsetProxy(private val headset: BluetoothHeadset) {
    companion object {
        const val ACTION_ACTIVE_DEVICE_CHANGED =
            "android.bluetooth.headset.profile.action.ACTIVE_DEVICE_CHANGED"
    }

    private val bluetoothHeadset = headset
    private val bluetoothHeadsetProxyClass by lazy {
        Class.forName("android.bluetooth.BluetoothHeadset")
    }
    private val getActiveDevice by lazy {
        bluetoothHeadsetProxyClass.getDeclaredMethod("getActiveDevice")
            .apply { isAccessible = true }
    }
    private val isAudioOn by lazy {
        bluetoothHeadsetProxyClass.getDeclaredMethod("isAudioOn").apply { isAccessible = true }
    }
    private val getAudioState by lazy {
        bluetoothHeadsetProxyClass.getDeclaredMethod("getAudioState", BluetoothDevice::class.java)
            .apply { isAccessible = true }
    }
    private val getConnectionPolicy by lazy {
        bluetoothHeadsetProxyClass.getDeclaredMethod(
            "getConnectionPolicy",
            BluetoothDevice::class.java
        ).apply { isAccessible = true }
    }
    private val setConnectionPolicy by lazy {
        bluetoothHeadsetProxyClass.getDeclaredMethod(
            "setConnectionPolicy",
            BluetoothDevice::class.java,
            Int::class.java
        ).apply { isAccessible = true }
    }

    fun getActiveDevice(): BluetoothDevice? {
        return getActiveDevice.invoke(bluetoothHeadset) as? BluetoothDevice
    }

    fun isAudioOn(): Boolean {
        return isAudioOn.invoke(bluetoothHeadset) as Boolean
    }

    fun getAudioState(device: BluetoothDevice): Int {
        return getAudioState.invoke(bluetoothHeadset, device) as Int
    }

    fun getConnectionPolicy(device: BluetoothDevice): Int {
        return getConnectionPolicy.invoke(bluetoothHeadset, device) as Int
    }

    fun setConnectionPolicy(device: BluetoothDevice, key: Int): Boolean {
        return setConnectionPolicy.invoke(bluetoothHeadset, device, key) as Boolean
    }
}