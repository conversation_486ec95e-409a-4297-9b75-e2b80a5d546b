package com.czur.starry.device.bluetoothlib.util

import android.bluetooth.BluetoothDevice

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2024/04/03
 */


class BluetoothDeviceProxy(private val device: BluetoothDevice) {
    companion object {
        const val ACTION_BATTERY_LEVEL_CHANGED =
            "android.bluetooth.device.action.BATTERY_LEVEL_CHANGED"
        const val EXTRA_REASON = "android.bluetooth.device.extra.REASON"
        const val UNBOND_REASON_AUTH_FAILED = 1
        const val UNBOND_REASON_AUTH_REJECTED = 2
        const val UNBOND_REASON_AUTH_CANCELED = 3
        const val UNBOND_REASON_REMOTE_DEVICE_DOWN = 4
        const val UNBOND_REASON_DISCOVERY_IN_PROGRESS = 5
        const val UNBOND_REASON_AUTH_TIMEOUT = 6
        const val UNBOND_REASON_REPEATED_ATTEMPTS = 7
        const val UNBOND_REASON_REMOTE_AUTH_CANCELED = 8
        const val METADATA_DEVICE_TYPE = 17
        const val METADATA_IS_UNTETHERED_HEADSET = 6
        const val DEVICE_TYPE_UNTETHERED_HEADSET = "Untethered Headset"
        const val DEVICE_TYPE_WATCH = "Watch"
        const val DEVICE_TYPE_DEFAULT = "Default"
        const val METADATA_MAIN_ICON = 5
        const val ACCESS_UNKNOWN = 0
        const val ACCESS_ALLOWED = 1
        const val ACCESS_REJECTED = 2
        const val BATTERY_LEVEL_UNKNOWN = -1
        const val METADATA_UNTETHERED_LEFT_BATTERY = 10
        const val METADATA_UNTETHERED_RIGHT_BATTERY = 11
    }

    private val bluetoothDevice = device


    private val bluetoothDeviceClass by lazy {
        Class.forName("android.bluetooth.BluetoothDevice")
    }
    private val isConnected by lazy {
        bluetoothDeviceClass.getDeclaredMethod("isConnected").apply { isAccessible = true }

    }
    private val getMetadata by lazy {
        bluetoothDeviceClass.getDeclaredMethod("getMetadata", Int::class.java)
            .apply { isAccessible = true }

    }
    private val cancelBondProcess by lazy {
        bluetoothDeviceClass.getDeclaredMethod("cancelBondProcess").apply {
            isAccessible = true
        }
    }
    private val removeBond by lazy {
        bluetoothDeviceClass.getDeclaredMethod("removeBond").apply {
            isAccessible = true
        }
    }

    private val getBatteryLevel by lazy {
        bluetoothDeviceClass.getDeclaredMethod("getBatteryLevel").apply {
            isAccessible = true
        }
    }
    private val setPhonebookAccessPermission by lazy {
        bluetoothDeviceClass.getDeclaredMethod("setPhonebookAccessPermission", Int::class.java)
            .apply {
                isAccessible = true
            }
    }

    private val setMessageAccessPermission by lazy {
        bluetoothDeviceClass.getDeclaredMethod("setMessageAccessPermission", Int::class.java)
            .apply {
                isAccessible = true
            }
    }
    private val setSimAccessPermission by lazy {
        bluetoothDeviceClass.getDeclaredMethod("setSimAccessPermission", Int::class.java).apply {
            isAccessible = true
        }
    }
    private val isBondingInitiatedLocally by lazy {
        bluetoothDeviceClass.getDeclaredMethod("isBondingInitiatedLocally")
            .apply { isAccessible = true }
    }
    private val getPhonebookAccessPermission by lazy {
        bluetoothDeviceClass.getDeclaredMethod("getPhonebookAccessPermission")
            .apply { isAccessible = true }
    }
    private val getMessageAccessPermission by lazy {
        bluetoothDeviceClass.getDeclaredMethod("getMessageAccessPermission").apply { isAccessible = true }
    }

    private val connect by lazy {
        bluetoothDeviceClass.getDeclaredMethod("connect").apply { isAccessible = true }
    }

    private val disconnect by lazy {
        bluetoothDeviceClass.getDeclaredMethod("disconnect").apply { isAccessible = true }
    }

    fun isConnected(): Boolean {
        return isConnected.invoke(bluetoothDevice) as Boolean
    }

    fun getMetadata(key: Int): ByteArray {
        return getMetadata.invoke(bluetoothDevice, key) as ByteArray
    }

    fun cancelBondProcess() {
        cancelBondProcess.invoke(bluetoothDevice)
    }

    fun removeBond(): Boolean {
        return removeBond.invoke(bluetoothDevice) as Boolean
    }

    fun getBatteryLevel(): Int {
        return getBatteryLevel.invoke(bluetoothDevice) as Int
    }

    fun setPhonebookAccessPermission(key: Int) {
        setPhonebookAccessPermission.invoke(bluetoothDevice, key)
    }

    fun setMessageAccessPermission(key: Int) {
        setMessageAccessPermission.invoke(bluetoothDevice, key)
    }

    fun setSimAccessPermission(key: Int) {
        setSimAccessPermission.invoke(bluetoothDevice, key)
    }

    fun isBondingInitiatedLocally(): Boolean {
        return isBondingInitiatedLocally.invoke(bluetoothDevice) as Boolean
    }

    fun getPhonebookAccessPermission(): Int {
        return getPhonebookAccessPermission.invoke(bluetoothDevice) as Int
    }
    fun getMessageAccessPermission():Int{
        return getMessageAccessPermission.invoke(bluetoothDevice) as Int
    }

    fun connect(): Int {
        return connect.invoke(bluetoothDevice) as Int
    }
    fun disconnect(): Int {
        return disconnect.invoke(bluetoothDevice) as Int
    }
}