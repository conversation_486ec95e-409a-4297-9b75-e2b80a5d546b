package com.czur.starry.device.bluetoothlib.util

import android.annotation.SuppressLint
import android.bluetooth.BluetoothA2dp
import android.bluetooth.BluetoothCodecStatus
import android.bluetooth.BluetoothDevice

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2024/04/03
 */


class BluetoothA2dpProxy(private val bluetoothA2dp: BluetoothA2dp) {
    companion object {
        const val OPTIONAL_CODECS_SUPPORTED = 1
        const val OPTIONAL_CODECS_PREF_UNKNOWN = -1
        const val OPTIONAL_CODECS_PREF_ENABLED = 1
        const val OPTIONAL_CODECS_PREF_DISABLED = 0
        const val ACTION_ACTIVE_DEVICE_CHANGED =
            "android.bluetooth.a2dp.profile.action.ACTIVE_DEVICE_CHANGED"
    }

    private var bluetoothA2dpInstance = bluetoothA2dp
    private val bluetoothA2dpClass by lazy {
        Class.forName("android.bluetooth.BluetoothA2dp")
    }

    private val activeDevice by lazy {
        bluetoothA2dpClass.getDeclaredMethod("getActiveDevice").apply { isAccessible = true }
    }

    private val connectionPolicyGet by lazy {

        bluetoothA2dpClass.getDeclaredMethod("getConnectionPolicy", BluetoothDevice::class.java)
            .apply { isAccessible = true }

    }

    private val connectionPolicySet by lazy {
        bluetoothA2dpClass.getDeclaredMethod(
            "setConnectionPolicy",
            BluetoothDevice::class.java,
            Int::class.java
        ).apply { isAccessible = true }

    }
    private val isOptionalCodecsSupported by lazy {
        bluetoothA2dpClass.getDeclaredMethod(
            "isOptionalCodecsSupported",
            BluetoothDevice::class.java
        ).apply { isAccessible = true }

    }
    private val getCodecStatus by lazy {

        bluetoothA2dpClass.getDeclaredMethod("getCodecStatus", BluetoothDevice::class.java)
            .apply { isAccessible = true }

    }
    private val setOptionalCodecsEnabled by lazy {
        bluetoothA2dpClass.getDeclaredMethod(
            "setOptionalCodecsEnabled",
            BluetoothDevice::class.java,
            Int::class.java
        ).apply { isAccessible = true }

    }
    private val enableOptionalCodecs by lazy {
        bluetoothA2dpClass.getDeclaredMethod(
            "enableOptionalCodecs", BluetoothDevice::class.java
        ).apply { isAccessible = true }

    }
    private val disableOptionalCodecs by lazy {
        bluetoothA2dpClass.getDeclaredMethod(
            "disableOptionalCodecs", BluetoothDevice::class.java
        ).apply { isAccessible = true }

    }
    private val isOptionalCodecsEnabled by lazy {
        bluetoothA2dpClass.getDeclaredMethod(
            "isOptionalCodecsEnabled", BluetoothDevice::class.java
        ).apply { isAccessible = true }

    }

    fun getActiveDevice(): BluetoothDevice? {
        return activeDevice.invoke(bluetoothA2dpInstance) as? BluetoothDevice
    }

    fun getConnectionPolicy(device: BluetoothDevice): Int {
        return connectionPolicyGet.invoke(bluetoothA2dpInstance, device) as Int
    }

    fun setConnectionPolicy(device: BluetoothDevice, policy: Int): Boolean {
        return connectionPolicySet.invoke(bluetoothA2dpInstance, device, policy) as Boolean
    }

    fun isOptionalCodecsSupported(device: BluetoothDevice): Int {
        return isOptionalCodecsSupported.invoke(bluetoothA2dpInstance, device) as Int
    }

    @SuppressLint("NewApi")
    fun getCodecStatus(device: BluetoothDevice): BluetoothCodecStatus? {
        if (getCodecStatus.invoke(bluetoothA2dpInstance, device) != null) {
            return getCodecStatus.invoke(bluetoothA2dpInstance, device) as BluetoothCodecStatus
        }
        return  null
    }

    fun setOptionalCodecsEnabled(device: BluetoothDevice, enabled: Int): Boolean {
        return setOptionalCodecsEnabled.invoke(bluetoothA2dpInstance, device, enabled) as Boolean
    }

    fun enableOptionalCodecs(device: BluetoothDevice) {
        enableOptionalCodecs.invoke(bluetoothA2dpInstance, device)
    }

    fun disableOptionalCodecs(device: BluetoothDevice) {
        disableOptionalCodecs.invoke(bluetoothA2dpInstance, device)
    }

    fun isOptionalCodecsEnabled(device: BluetoothDevice): Int {
        return isOptionalCodecsEnabled.invoke(bluetoothA2dpInstance, device) as Int
    }
}