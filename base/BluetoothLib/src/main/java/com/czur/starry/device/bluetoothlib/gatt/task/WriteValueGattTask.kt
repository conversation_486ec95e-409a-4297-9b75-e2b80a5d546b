package com.czur.starry.device.bluetoothlib.gatt.task

import com.czur.czurutils.log.logTagD
import android.bluetooth.BluetoothGatt
import android.bluetooth.BluetoothGattCharacteristic
import com.czur.starry.device.bluetoothlib.gatt.GattCharacteristic
import com.czur.starry.device.bluetoothlib.util.readCharacteristic
import com.czur.starry.device.bluetoothlib.util.writeCharacteristic

/**
 * Created by 陈丰尧 on 2022/11/3
 * 发送数据的Task
 */
class WriteValueGattTask(
    private val gattCharacteristic: GattCharacteristic,
    private val data: ByteArray,
    needRetry: Boolean = false,
    private val beforeWriteBlock: ((BluetoothGattCharacteristic) -> Unit)? = null
) : GattTask(needRetry) {

    companion object {
        private const val TAG = "WriteValueGattTask"
    }

    override val name: String
        get() = "WriteValueGattTask:${gattCharacteristic.name}"

    override fun processTask(gatt: BluetoothGatt): Boolean {
        logTagD(TAG,"执行发送数据:${data.joinToString { it.toUByte().toString(16) }}")

        return gatt.writeCharacteristic(gattCharacteristic, data,beforeWriteBlock)
    }
}