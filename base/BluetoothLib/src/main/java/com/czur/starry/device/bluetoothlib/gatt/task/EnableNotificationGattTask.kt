package com.czur.starry.device.bluetoothlib.gatt.task

import android.bluetooth.BluetoothGatt
import com.czur.starry.device.bluetoothlib.gatt.GattCharacteristic
import com.czur.starry.device.bluetoothlib.util.enableNotification

/**
 * Created by 陈丰尧 on 2022/11/3
 */
class EnableNotificationGattTask(
    private val gattCharacteristic: GattCharacteristic,
    needRetry: Boolean = false
) : GattTask(needRetry) {
    override val name: String
        get() = "EnableNotificationGattTask:${gattCharacteristic.name}"

    override fun processTask(gatt: BluetoothGatt): Bo<PERSON>an {
        return gatt.enableNotification(gattCharacteristic)
    }
}