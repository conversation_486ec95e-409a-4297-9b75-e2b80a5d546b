package com.czur.starry.device.bluetoothlib.util

import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothHidDevice

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2024/04/07
 */


class BluetoothHidDeviceProxy(private val device: BluetoothHidDevice) {
    private val bluetoothHidDevice = device
    private val bluetoothAdapterClass by lazy {
        Class.forName("android.bluetooth.BluetoothHidDevice")
    }

    private val setConnectionPolicy by lazy {
        bluetoothAdapterClass.getDeclaredMethod(
            "setConnectionPolicy",
            BluetoothDevice::class.java,
            Integer.TYPE
        ).apply { isAccessible = true }
    }

    fun setConnectionPolicy(btDevice: BluetoothDevice, key: Int): Boolean {
        return setConnectionPolicy.invoke(bluetoothHidDevice, btDevice, key) as Bo<PERSON><PERSON>
    }
}