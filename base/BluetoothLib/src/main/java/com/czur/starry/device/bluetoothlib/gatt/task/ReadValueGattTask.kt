package com.czur.starry.device.bluetoothlib.gatt.task

import com.czur.czurutils.log.logTagW
import android.bluetooth.BluetoothGatt
import com.czur.starry.device.bluetoothlib.gatt.GattBatteryService
import com.czur.starry.device.bluetoothlib.gatt.GattCharacteristic
import com.czur.starry.device.bluetoothlib.gatt.GattDeviceInfoService
import com.czur.starry.device.bluetoothlib.util.readCharacteristic

/**
 * Created by 陈丰尧 on 2022/11/3
 */
class ReadValueGattTask(
    private val gattCharacteristic: GattCharacteristic,
    needRetry: Boolean = false
) :
    GattTask(needRetry) {
    override val name: String
        get() = "ReadValueGattTask:${gattCharacteristic.name}"

    override fun processTask(gatt: BluetoothGatt): Boolean {
        return try {
            gatt.readCharacteristic(gattCharacteristic)
        } catch (exp: Exception) {
            logTagW("TAG", "读取蓝牙属性失败", tr = exp)
            false
        }
    }
}

// 方便的2个小方法
fun ReadBatteryLevelGattTask(needRetry: Boolean = false) =
    ReadValueGattTask(GattBatteryService.BatteryLevelCharacteristic, needRetry)

fun ReadFirmwareRevisionGattTask(needRetry: Boolean = false) =
    ReadValueGattTask(GattDeviceInfoService.FirmwareRevisionStrCharacteristic, needRetry)