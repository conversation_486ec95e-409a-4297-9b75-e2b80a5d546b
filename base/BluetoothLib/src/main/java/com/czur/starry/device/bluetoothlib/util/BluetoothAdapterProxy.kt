package com.czur.starry.device.bluetoothlib.util

import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.os.ParcelUuid
import java.lang.reflect.Method


/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2024/04/03
 */


class BluetoothAdapterProxy(private val bluetoothAdapter: BluetoothAdapter) {
    companion object {
        const val ACTIVE_DEVICE_AUDIO = 0
        const val ACTIVE_DEVICE_PHONE_CALL = 1
    }

    private val bluetoothAdapterInstance = bluetoothAdapter

    private val bluetoothAdapterClass by lazy {
        Class.forName("android.bluetooth.BluetoothAdapter")
    }

    private val getSetActiveDeviceMethod: Method by lazy {
        bluetoothAdapterClass.getDeclaredMethod(
            "setActiveDevice",
            BluetoothDevice::class.java,
            Int::class.java
        ).apply { isAccessible = true }

    }
    private val getRemoveActiveDevice: Method by lazy {
        bluetoothAdapterClass.getDeclaredMethod("removeActiveDevice", Int::class.java)
            .apply { isAccessible = true }

    }
    private val profileProxy: Method by lazy {
        bluetoothAdapterClass.getDeclaredMethod("getProfileProxy").apply { isAccessible = true }

    }
    private val disconnectAllEnabledProfiles by lazy {
        bluetoothAdapterClass.getDeclaredMethod(
            "disconnectAllEnabledProfiles",
            BluetoothDevice::class.java
        ).apply { isAccessible = true }

    }
    private val getSupportedProfiles by lazy {
        bluetoothAdapterClass.getDeclaredMethod("getSupportedProfiles")
            .apply { isAccessible = true }

    }
    private val getUuids by lazy {
        bluetoothAdapterClass.getDeclaredMethod("getUuids").apply { isAccessible = true }

    }
    private val getConnectionState by lazy {
        bluetoothAdapterClass.getDeclaredMethod("getConnectionState").apply { isAccessible = true }

    }
    private val setDiscoverableTimeout by lazy {
        bluetoothAdapterClass.getDeclaredMethod("setDiscoverableTimeout", Int::class.java)
            .apply { isAccessible = true }

    }
    private val getDiscoveryEndMillis by lazy {
        bluetoothAdapterClass.getDeclaredMethod("getDiscoveryEndMillis")
            .apply { isAccessible = true }

    }
    private val setScanMode by lazy {
        bluetoothAdapterClass.getDeclaredMethod("setScanMode", Int::class.java)
            .apply { isAccessible = true }

    }
    private val setScanMode2 by lazy {

        bluetoothAdapterClass.getDeclaredMethod("setScanMode", Int::class.java, Int::class.java)
            .apply { isAccessible = true }

    }
    private val connectAllEnabledProfiles by lazy {
        bluetoothAdapterClass.getDeclaredMethod(
            "connectAllEnabledProfiles",
            BluetoothDevice::class.java
        ).apply { isAccessible = true }
    }


    fun setActiveDevice(device: BluetoothDevice, deviceType: Int): Boolean {
        return getSetActiveDeviceMethod.invoke(
            bluetoothAdapterInstance,
            device,
            deviceType
        ) as Boolean
    }

    fun removeActiveDevice(deviceType: Int): Boolean {
        return getRemoveActiveDevice.invoke(bluetoothAdapterInstance, deviceType) as Boolean
    }

    fun disconnectAllEnabledProfiles(device: BluetoothDevice): BluetoothDevice {
        return disconnectAllEnabledProfiles.invoke(
            bluetoothAdapterInstance,
            device
        ) as BluetoothDevice
    }

    fun getSupportedProfiles(): List<Int> {
        return getSupportedProfiles.invoke(bluetoothAdapterInstance) as List<Int>
    }

    fun getUuids(): Array<ParcelUuid> {
        return getUuids.invoke(bluetoothAdapterInstance) as Array<ParcelUuid>
    }

    fun getConnectionState(): Int {
        return getConnectionState.invoke(bluetoothAdapterInstance) as Int
    }

    fun setDiscoverableTimeout(timeout: Int) {
        setDiscoverableTimeout.invoke(bluetoothAdapterInstance, timeout)
    }

    fun getDiscoveryEndMillis(): Long {
        return getDiscoveryEndMillis.invoke(bluetoothAdapterInstance) as Long
    }

    fun setScanMode(mode: Int) {
        setScanMode.invoke(bluetoothAdapterInstance, mode)
    }

    fun setScanMode(mode: Int, duration: Int): Boolean {
        return setScanMode2.invoke(bluetoothAdapterInstance, mode, duration) as Boolean
    }

    fun connectAllEnabledProfiles(device: BluetoothDevice) {
        connectAllEnabledProfiles.invoke(bluetoothAdapterInstance, device)
    }

    fun getInstance(): BluetoothAdapter? {
        return bluetoothAdapterInstance
    }
}