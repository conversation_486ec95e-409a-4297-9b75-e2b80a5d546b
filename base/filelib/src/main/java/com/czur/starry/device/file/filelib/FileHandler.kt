package com.czur.starry.device.file.filelib

import com.czur.czurutils.log.logTagD
import android.content.ContentResolver
import android.content.UriMatcher
import android.database.Cursor
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.utils.*
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2021/5/18
 */
object FileHandler {
    private const val TAG = "FileHandler"

    private const val AUTHORITY = "com.czur.starry.device.file.share.ShareFileProvider"

    val PATH_SHARE_FILE_ROOT = PathWrapper("root", 0)

    // 指定文件夹下的数据
    val PATH_SHARE_FILE_TARGET = PathWrapper("target", 1)

    // 获取文档打开的Url
    val PATH_SHARE_FILE_OPEN = PathWrapper("open", 2)

    val PATH_FILE_SIZE = PathWrapper("fileSize", 3)

    val PATH_COPY_EXPLAIN_FILE = PathWrapper("copyExplainFile", 4)

    private const val COUNT_ALL = Int.MAX_VALUE

    const val KEY_START = "keyStart"
    const val KEY_COUNT = "keyCount"
    const val KEY_SORT_TYPE = "keySortType"
    const val KEY_SHARE_FILE = "shareFileJson"
    const val KEY_FILE_SIZE_TYPE = "sizeFileTypes"

    private val gson by lazy { Gson() }

    /**
     * 排序方式
     */
    enum class SortType {
        TIME_ASC, NAME_ASC, TIME_DESC, NAME_DESC
    }

    val uriMatcher = UriMatcher(UriMatcher.NO_MATCH).apply {
        addURI(AUTHORITY, PATH_SHARE_FILE_ROOT.path, PATH_SHARE_FILE_ROOT.code)
        addURI(AUTHORITY, PATH_SHARE_FILE_TARGET.path, PATH_SHARE_FILE_TARGET.code)
        addURI(AUTHORITY, PATH_SHARE_FILE_OPEN.path, PATH_SHARE_FILE_OPEN.code)
        addURI(AUTHORITY, PATH_FILE_SIZE.path, PATH_FILE_SIZE.code)
        addURI(AUTHORITY, PATH_COPY_EXPLAIN_FILE.path, PATH_COPY_EXPLAIN_FILE.code)
    }

    private val receiver: ContentResolver by lazy {
        CZURAtyManager.appContext.contentResolver
    }

    /**
     * 获取全部可用根据零
     */
    suspend fun getRoots(): List<ShareFile> = withContext(Dispatchers.IO) {
        logTagD(TAG, "getRoots")
        val cursor = receiver.query(PATH_SHARE_FILE_ROOT)
        cursor?.use {
            it.toListBlob()
        } ?: listOf()
    }

    suspend fun getItemsByTarget(
        dirFile: ShareFile,
        start: Int = 0,
        count: Int = COUNT_ALL,
        sortType: SortType = SortType.TIME_ASC
    ): List<ShareFile> = withContext(Dispatchers.IO) {
        val dirCopy = dirFile.copy()
        val dirFileJson = gson.toJson(dirCopy)

        logTagD(TAG, "getItemsByTarget", dirFileJson)
        val cursor = receiver.query(
            PATH_SHARE_FILE_TARGET,
            KEY_SHARE_FILE to dirFileJson,
            KEY_START to start.toString(),
            KEY_COUNT to count.toString(),
            KEY_SORT_TYPE to sortType.name
        )
        cursor?.use {
            it.toListBlob()
        } ?: listOf()
    }

    /**
     * 获取本地文件打开的URL
     * @param targetFile 对应的文件
     * @return office365的网址, 如果上传失败了, 则会返回空串
     */
    suspend fun getOpenUrl(targetFile: ShareFile): String {
        return withContext(Dispatchers.IO) {
            val json = gson.toJson(targetFile)
            logTagD(TAG, "getOpenUrl", json)
            val cursor = receiver.query(
                PATH_SHARE_FILE_OPEN,
                KEY_SHARE_FILE to json
            )
            cursor?.use {
                it.getString()
            } ?: ""
        }

    }

    /**
     * 获取文件大小 只有APK文件会返回详细的文件列表
     * @param sizeFileTypes: 要查询大小的文件类型, 默认是全部文件
     * @return 计算的文件大小, 查询失败返回空List
     */
    suspend fun getFileSize(
        sizeFileTypes: List<SizeFileType> = SizeFileType.values().toList()
    ): List<FileSize> {
        return withContext(Dispatchers.IO) {
            val queryTypeJson = gson.toJson(sizeFileTypes)
            logTagD(TAG, "查询文件大小", queryTypeJson)
            val cursor = receiver.query(
                PATH_FILE_SIZE,
                KEY_FILE_SIZE_TYPE to queryTypeJson
            )
            cursor?.use {
                it.toListBlob()
            } ?: emptyList()
        }
    }

    private fun ContentResolver.query(
        pathWrapper: PathWrapper,
        vararg params: Pair<String, String?> = arrayOf()
    ): Cursor? =
        query(AUTHORITY, pathWrapper, *params)

}