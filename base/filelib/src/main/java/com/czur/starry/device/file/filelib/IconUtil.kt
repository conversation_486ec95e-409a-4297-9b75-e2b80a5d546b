package com.czur.starry.device.file.filelib

import com.czur.czurutils.log.logTagE


/**
 * Created by 陈丰尧 on 2021/5/19
 * 选择浮窗的icon
 */

private const val TAG = "IconUtil"
fun ShareFile.getIconRes(darkTheme: Boolean = true): Int =
    getIconRes(fileType, belongTo, extType, darkTheme)

fun getIconRes(
    fileType: FileType,
    belongTo: AccessType,
    extType: Int = EXT_TYPE_NORMAL,
    darkTheme: <PERSON>olean = true
): Int {
    return when (fileType) {
        FileType.ROOT -> getRootIcon(belongTo, extType, darkTheme)
        FileType.EXCEL -> R.drawable.file_icon_excel
        FileType.DOC -> R.drawable.file_icon_word
        FileType.PDF -> R.drawable.file_icon_pdf
        FileType.IMAGE -> R.drawable.file_icon_pic
        FileType.FOLDER -> R.drawable.file_icon_folder
        FileType.PPT -> R.drawable.file_icon_ppt
        FileType.OTHER -> R.drawable.file_icon_other
        FileType.AUDIO -> R.drawable.file_icon_audio
        FileType.VIDEO -> R.drawable.file_icon_video
        FileType.DOCUMENT -> R.drawable.file_icon_txt
        FileType.ZIP -> R.drawable.file_icon_zip
        FileType.APK -> R.drawable.file_icon_apk
        FileType.LOAD_INIT->R.drawable.file_icon_other //改用other一样图
        FileType.MEET_RECORD -> R.drawable.file_icon_audio // TODO 先用音频文件的图标
        FileType.X_APK -> R.drawable.file_icon_apk
    }
}

private fun getRootIcon(belongTo: AccessType, extType: Int, darkTheme: Boolean = true): Int =
    when (belongTo) {
        AccessType.LOCAL -> {
            when (extType) {
                EXT_TYPE_LOCAL_DOWNLOAD -> if (darkTheme) R.drawable.file_icon_local_download else R.drawable.icon_root_download
                EXT_TYPE_LOCAL_SCREEN_SHOT -> if (darkTheme) R.drawable.file_icon_local_screenshot else R.drawable.icon_root_snap_shot
                EXT_TYPE_LOCAL_CZUR_SHARE -> if (darkTheme) R.drawable.file_icon_local_share else R.drawable.icon_root_share
                else -> if (darkTheme) R.drawable.file_icon_local else R.drawable.icon_root_local
            }
        }
        AccessType.USB -> if (darkTheme) R.drawable.file_icon_usb else R.drawable.icon_root_usb
        else -> {
            logTagE(TAG, "rootType不能是会议记录")
            -1
        }
    }