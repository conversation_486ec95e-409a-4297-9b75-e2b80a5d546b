package com.czur.starry.device.file.filelib


enum class FileType {
    IMAGE, // 图片
    DOC, // word
    PDF, // pdf
    PPT, // ppt
    EXCEL, // excel
    FOLDER, // 文件夹
    OTHER,  // 其他
    AUDIO, // 音频
    MEET_RECORD,    // 会议录音
    VIDEO, // 视频
    DOCUMENT,// 文本类型
    ZIP,// 压缩类型
    APK,// APK类型
    X_APK,//X_APK类型
    ROOT, // 根路径
    LOAD_INIT; // 初始化加载

    fun isTypeOf(vararg types: FileType): Boolean {
        return this in types
    }


    operator fun plus(type: FileType): List<FileType> {
        return listOf(this, type)
    }
}









