package com.czur.starry.device.file.filelib

import androidx.lifecycle.MutableLiveData
import com.czur.starry.device.baselib.handler.SPContentHandler
import kotlin.random.Random

/**
 * created by wangh on 23.0518
 * 保存未读文件状态
 */


object FileHandlerLive : SPContentHandler() {
    private const val TAG = "FileHandlerLive"
    private const val KEY_UNREAD_FILE_TAB_UPDATE = "unReadFileTabUpdate"  // 未读列表更新
    private const val KEY_UNREAD_FILE_NAME = "is_unread_file_name"  // 最近未读文件
    private const val KEY_UNREAD_FILE_STATUS = "is_unread_file_status"  // 未读文件
    private const val KEY_UNREAD_FILE_STATUS_LOCAL = "is_unread_file_status_local"  // 本地未读
    private const val KEY_UNREAD_FILE_STATUS_DOWNLOAD = "is_unread_file_status_download"  // 下载未读
    private const val KEY_UNREAD_FILE_STATUS_SHARE = "is_unread_file_status_share"  // 妙传未读
    const val KEY_FILE_SHARE_CODE_STATUS = "is_file_share_code_status"  // 妙传未读校验码
    const val KEY_FILE_SHARE_CODE_ENABLE = "is_file_share_code_enable"  // 妙传未读校验码开关
    const val KEY_FILE_SHARE_ENABLE_STATUS = "is_file_share_enable_status"  // 妙传开关状态
    const val KEY_FILE_MEETING_ENABLE_STATUS = "is_file_meeting_enable_status"  // 妙传会议录音开关状态
    private const val KEY_CURRENT_FILE_SHARE_STATUS = "current_file_share_status"  // 妙传当前传输状态
    private const val KEY_FILE_SHARE_TIME_OUT_STATUS = "is_file_share_time_out_status"  // 妙传删除时长
    private const val KEY_UNREAD_FILE_STATUS_SCREEN_SHORT =
        "is_unread_file_status_screenshort"  // 截屏未读
    private const val KEY_UNREAD_FILE_STATUS_MEETING = "is_unread_file_status_meeting"  // 会议未读
    private const val KEY_UNREAD_FILE_STATUS_AI_TRANS = "is_unread_file_status_ai_trans"  // AI互译未读
    private const val KEY_FILE_SORT_TYPE_STATE = "is_file_sort_type_status_state"  // 文件排序状态
    private const val KEY_FILE_MEDIA_STATE = "is_file_media_status"  // 视频播放状态
    private const val KEY_CODE_PAGE_STATE = "is_file_keycode_status"  // 语音云文件翻页

    override val authority: String = "com.czur.starry.device.livedata.fileprovider"
    override val keyLiveMap: Map<String, LiveTrans<*>> by lazy {
        mapOf(
            // 未读列表
            KEY_UNREAD_FILE_TAB_UPDATE to LiveTrans(fileTabUpdateLive as MutableLiveData) {
                it.toInt()
            },
            // 未读文件名
            KEY_UNREAD_FILE_NAME to LiveTrans(unReadFileNameLive as MutableLiveData) {
                it
            },
            KEY_UNREAD_FILE_STATUS_LOCAL to LiveTrans(unReadFileLocalLive as MutableLiveData) {
                it.toBoolean()
            },
            KEY_UNREAD_FILE_STATUS_DOWNLOAD to LiveTrans(unReadFileDownloadLive as MutableLiveData) {
                it.toBoolean()
            },
            KEY_UNREAD_FILE_STATUS_SHARE to LiveTrans(unReadFileShareLive as MutableLiveData) {
                it.toBoolean()
            },
            KEY_FILE_SHARE_ENABLE_STATUS to LiveTrans(fileShareEnableLive as MutableLiveData) {
                it.toBoolean()
            },
            KEY_FILE_SHARE_CODE_ENABLE to LiveTrans(fileShareCodeEnableLive as MutableLiveData) {
                it.toBoolean()
            },
            KEY_FILE_SHARE_CODE_STATUS to LiveTrans(fileShareCodeLive as MutableLiveData) {
                it
            },
            KEY_FILE_SHARE_TIME_OUT_STATUS to LiveTrans(fileShareTimeOutLive as MutableLiveData) {
                it.toInt()
            },
            KEY_UNREAD_FILE_STATUS_SCREEN_SHORT to LiveTrans(unReadFileScreenLive as MutableLiveData) {
                it.toBoolean()
            },
            KEY_UNREAD_FILE_STATUS_MEETING to LiveTrans(unReadFileMeetingLive as MutableLiveData) {
                it.toBoolean()
            },
            KEY_UNREAD_FILE_STATUS_AI_TRANS to LiveTrans(unReadFileAITransLive as MutableLiveData) {
                it.toBoolean()
            },
            KEY_FILE_MEETING_ENABLE_STATUS to LiveTrans(fileShareMeetingEnableLive as MutableLiveData) {
                it.toBoolean()
            },
            KEY_FILE_SORT_TYPE_STATE to LiveTrans(currentSortTypeLive as MutableLiveData) {
                it.toInt()
            },
            KEY_FILE_MEDIA_STATE to LiveTrans(fileMediaStateLive as MutableLiveData) {
                it
            },
            KEY_CODE_PAGE_STATE to LiveTrans(fileKeyCodeStatusLive as MutableLiveData) {
                it.toInt()
            }
        )
    }

    /**
     * 更新文件中列表选项
     */
    val fileTabUpdateLive = createMutableLive { fileTabUpdate }
    var fileTabUpdate: Int
        get() = getValue(KEY_UNREAD_FILE_TAB_UPDATE, 0)
        set(value) = setValue(KEY_UNREAD_FILE_TAB_UPDATE, value)
    /**
     * 最近未读文件名
     */
    val unReadFileNameLive = createLive { unReadFileName }
    var unReadFileName: String
        get() = getValue(KEY_UNREAD_FILE_NAME, "null")
        set(value) = setValue(KEY_UNREAD_FILE_NAME, value)


    /**
     * 本地未读
     */
    val unReadFileLocalLive = createLive { unReadFileLocalStatus }

    var unReadFileLocalStatus: Boolean
        get() = getValue(KEY_UNREAD_FILE_STATUS_LOCAL, false)
        set(value) = setValue(KEY_UNREAD_FILE_STATUS_LOCAL, value)

    /**
     * 下载未读
     */
    val unReadFileDownloadLive = createLive { unReadFileDownloadStatus }

    var unReadFileDownloadStatus: Boolean
        get() = getValue(KEY_UNREAD_FILE_STATUS_DOWNLOAD, false)
        set(value) = setValue(KEY_UNREAD_FILE_STATUS_DOWNLOAD, value)

    /**
     * 成者秒传未读
     */
    val unReadFileShareLive = createLive { unReadFileShareStatus }

    var unReadFileShareStatus: Boolean
        get() = getValue(KEY_UNREAD_FILE_STATUS_SHARE, false)
        set(value) = setValue(KEY_UNREAD_FILE_STATUS_SHARE, value)


    /**
     * 成者秒传校验码
     */
    val fileShareCodeLive = createLive { fileShareCodeStatus }

    var fileShareCodeStatus: String
        get() {
            // 尝试从存储中获取值
            var code = getValue(KEY_FILE_SHARE_CODE_STATUS, "")
            if (code.isEmpty()) {
                // 若存储中没有值，生成随机数并存储
                code = Random.nextInt(1000, 10000).toString()
                setValue(KEY_FILE_SHARE_CODE_STATUS, code)
            }
            return code
        }
        set(value) = setValue(KEY_FILE_SHARE_CODE_STATUS, value)

    /**
     * 成者秒传校验码开关
     */
    val fileShareCodeEnableLive = createLive { fileShareCodeEnable }

    var fileShareCodeEnable: Boolean
        get() = getValue(KEY_FILE_SHARE_CODE_ENABLE, true)
        set(value) = setValue(KEY_FILE_SHARE_CODE_ENABLE, value)



    /**
     * 成者秒传开关
     */
    val fileShareEnableLive = createLive { fileShareEnable }
    var fileShareEnable  :Boolean
        get() = getValue(KEY_FILE_SHARE_ENABLE_STATUS, true)
        set(value) = setValue(KEY_FILE_SHARE_ENABLE_STATUS, value)

    /**
     * 成者秒传会议录音开关
     */
    val fileShareMeetingEnableLive = createLive { fileMeetingEnable }
    var fileMeetingEnable  :Boolean
        get() = getValue(KEY_FILE_MEETING_ENABLE_STATUS, true)
        set(value) = setValue(KEY_FILE_MEETING_ENABLE_STATUS, value)

    /**
     * 成者秒传文件删除周期(hour)
     */
    val fileShareTimeOutLive = createLive { fileShareTimeStatus }

    var fileShareTimeStatus: Int
        get() = getValue(KEY_FILE_SHARE_TIME_OUT_STATUS, 4)
        set(value) = setValue(KEY_FILE_SHARE_TIME_OUT_STATUS, value)



    /**
     * 截屏未读
     */
    val unReadFileScreenLive = createLive { unReadFileScreenStatus }

    var unReadFileScreenStatus: Boolean
        get() = getValue(KEY_UNREAD_FILE_STATUS_SCREEN_SHORT, false)
        set(value) = setValue(KEY_UNREAD_FILE_STATUS_SCREEN_SHORT, value)

    /**
     * 会议录像未读
     */
    val unReadFileMeetingLive = createLive { unReadFileMeetingStatus }

    var unReadFileMeetingStatus: Boolean
        get() = getValue(KEY_UNREAD_FILE_STATUS_MEETING, false)
        set(value) = setValue(KEY_UNREAD_FILE_STATUS_MEETING, value)

    /**
     * AI互译未读
     */
    val unReadFileAITransLive = createLive { unReadFileAITransStatus }

    var unReadFileAITransStatus: Boolean
        get() = getValue(KEY_UNREAD_FILE_STATUS_AI_TRANS, false)
        set(value) = setValue(KEY_UNREAD_FILE_STATUS_AI_TRANS, value)

    /**
     * 语音文件排序
     */
    val currentSortTypeLive = createLive { currentSortTypeState }

    var currentSortTypeState: Int
        get() = getValue(KEY_FILE_SORT_TYPE_STATE, -1)
        set(value) = setValue(KEY_FILE_SORT_TYPE_STATE, value)

    /**
     * 视频播放状态
     */
    val fileMediaStateLive = createLive { fileMediaState }
    var fileMediaState: String
        get() = getValue(KEY_FILE_MEDIA_STATE, "")
        set(value) = setValue(KEY_FILE_MEDIA_STATE, value)
    /**
     * 语音云文件翻页
     */
    val fileKeyCodeStatusLive = createLive { fileKeyCodeStatus }
    var fileKeyCodeStatus: Int
        get() = getValue(KEY_CODE_PAGE_STATE, -1)
        set(value) = setValue(KEY_CODE_PAGE_STATE, value)
}