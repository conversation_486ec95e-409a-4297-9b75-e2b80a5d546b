package com.czur.starry.device.file.filelib

import com.czur.starry.device.file.filelib.FileType.*
import java.io.Serializable

enum class SizeFileType {
    MEDIA,      // 媒体
    IMAGE,      // 图片
    DOCUMENT,   // 文档
    APK;        // APK
}

/**
 * 文件大小
 */
data class FileSize(
    val type: SizeFileType,
    var size: Long,
    val fileItems: MutableList<FileSizeItem>       // 每一个文件的详细信息
) : Serializable {
    companion object {
        @JvmStatic
        private val serialVersionUID = 1L
    }
}

/**
 * 文件的详细信息
 */
data class FileSizeItem(
    val fileName: String,
    val absPath: String,
    val size: Long
) : Serializable {
    companion object {
        @JvmStatic
        private val serialVersionUID = 1L
    }
}

/**
 * Created by 陈丰尧 on 2021/5/18
 */
data class ShareFile(
    var absPath: String,
    var name: String,
    var fileType: FileType,
    val belongTo: AccessType,
    var ossKey: String? = null,
    var extType: Int = EXT_TYPE_NORMAL
) : Serializable {
    companion object {
        @JvmStatic
        private val serialVersionUID = 1L
    }

    // 是否可以进入
    val canEnter: Boolean
        get() = fileType == ROOT || fileType == FOLDER

    // 是否可以预览
    val canPreview: Boolean
        get() {
            return fileType in listOf(DOC, PPT, EXCEL, PDF, DOCUMENT, IMAGE)
        }
}

const val EXT_TYPE_NORMAL = 0
const val EXT_TYPE_LOCAL_DOWNLOAD = 1
const val EXT_TYPE_LOCAL_SCREEN_SHOT = 2
const val EXT_TYPE_LOCAL_CZUR_SHARE = 3
const val EXT_TYPE_LOCAL_ENCRYPTION = 4 // 本地加密文件

enum class TakeUpStorageLocation {
    LOCAL,
    USB,
    MEETING_RECORD,
    AI_TRANS,
}

enum class AccessType(val takeUpStorageLocation: TakeUpStorageLocation) {
    LOCAL(TakeUpStorageLocation.LOCAL), //本地
    USB(TakeUpStorageLocation.MEETING_RECORD), //可移动存储
    RECORD(TakeUpStorageLocation.MEETING_RECORD), //会议记录
    LOCAL_MEETING(TakeUpStorageLocation.LOCAL),  // 本地会议录像
    AI_TRANS(TakeUpStorageLocation.AI_TRANS),  // 本地会议录像
}