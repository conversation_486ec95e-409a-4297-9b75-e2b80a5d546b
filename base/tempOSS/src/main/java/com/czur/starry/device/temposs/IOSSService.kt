package com.czur.starry.device.temposs

import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.network.core.MiaoHttpEntity
import com.czur.starry.device.baselib.network.core.MiaoHttpGet
import com.czur.starry.device.baselib.network.core.MiaoHttpParam
import com.google.gson.annotations.SerializedName

/**
 * Created by 陈丰尧 on 2021/9/15
 */
interface IOSSService {
    /**
     * 获取OSS的临时Token
     */
    @MiaoHttpGet("/api/speed/getOssTempToken")
    fun getTmpToken(
        @MiaoHttpParam("sn") sn: String = Constants.SERIAL,
        clazz: Class<OSSTokenEntity> = OSSTokenEntity::class.java,
    ): MiaoHttpEntity<OSSTokenEntity>

    @MiaoHttpGet("/api/starry/logs/getLogsToken")
    fun getLogsToken(
        @MiaoHttpParam("sn") sn: String = Constants.SERIAL,
        clazz: Class<OSSTokenEntity> = OSSTokenEntity::class.java,
    ): MiaoHttpEntity<OSSTokenEntity>

    @MiaoHttpGet("/api/starry/translate/oss/token")
    fun getTransferOsstoken(clazz: Class<OSSTokenEntity> = OSSTokenEntity::class.java): MiaoHttpEntity<OSSTokenEntity>
}

data class OSSTokenEntity(
    @SerializedName("AccessKeyId")
    val accessKeyId: String,
    @SerializedName("AccessKeySecret")
    val accessKeySecret: String,
    @SerializedName("BucketName")
    val bucketName: String,
    @SerializedName("Expiration")
    val expiration: String,
    @SerializedName("Prefix")
    val prefix: String,
    @SerializedName("RequestId")
    val requestId: String,
    @SerializedName("SecurityToken")
    val securityToken: String,
    val status: String,
)