package com.czur.starry.device.temposs

import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagW
import com.czur.czurutils.log.logTagV
import com.alibaba.sdk.android.oss.OSS
import com.alibaba.sdk.android.oss.model.MultipartUploadRequest
import com.alibaba.sdk.android.oss.model.ObjectMetadata
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.utils.createUUID

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import kotlin.math.roundToInt

/**
 * Created by 陈丰尧 on 2021/9/15
 */

/**
 * 无论构建几个TempOSSUtil对象,使用OSS核心都是一个
 * 唯一的区别是对应的默认上传路径不同
 */
class TempOSSUtil(
    private val ossType: TmpOSSType,
) {
    companion object {
        private const val TAG = "TempOSSUtil"
        private const val PART_SIZE = 100 * 1024L
    }

    private val tempPrefix: String = ossType.prefix

    private val ossClient: OSS = getOSS()

    /**
     * 上传临时文件到OSS
     */
    suspend fun upload(
        filePath: String, remotePath: String = "",
        randomFileName: Boolean = false,
        processCallback: ((percent: Int) -> Unit)? = null,
    ) = uploadFile(File(filePath), remotePath, randomFileName, processCallback)

    /**
     * 上传临时文件到OSS上
     * @param file:             要上传的文件
     * @param remotePath:       自定义路径, 该路径会拼接在对应OSS前缀后, 如果没有则放到默认路径下
     * @param randomFileName:   是否要随机生成文件名,true:随机生成 false: 使用[file]的文件名
     * @param processCallback:  进度回调, 不传该参数表示不关心进度
     */
    suspend fun uploadFile(
        file: File,
        remotePath: String = "",
        randomFileName: Boolean = false,
        processCallback: ((percent: Int) -> Unit)? = null,
    ): UploadRes =
        withContext(Dispatchers.IO) {
            logTagD(TAG, "上传到OSS文件:${file.absolutePath}")
            if (!file.exists()) {
                logTagW(TAG, "文件不存在, 不上传")
                return@withContext UploadRes.makeFailRes()
            }

            val name = getUploadFileName(file, randomFileName)
            val targetPath = remotePath.addSymbolWhenNotEmpty("/")

            val ossKey = "${tempPrefix}${targetPath}/${name}"
            logTagV(TAG, "ossKey:${ossKey}")

            // 使用分片传输, 否则大文件容易失败
            val rq = MultipartUploadRequest<MultipartUploadRequest<*>>(
                Constants.TMP_BUCKET_NAME,
                ossKey,
                file.absolutePath,
                ObjectMetadata()
            )
            rq.partSize = PART_SIZE // 小一点比较容易成功
            processCallback?.let {
                // 进度回调
                rq.setProgressCallback { _, currentSize, totalSize ->
                    val percent = (currentSize.toDouble() / totalSize * 100).roundToInt()
                    it(percent)
                }
            }
            try {
                logTagV(TAG, "开始上传")
                ossClient.multipartUpload(rq)
                // 上传成功
                logTagD(TAG, "上传成功:type:${ossType}")
                UploadRes(true, ossKey, "${Constants.DOWNLOAD_HOST}/${ossKey}")
            } catch (exp: Exception) {
                // 上传失败
                logTagD(TAG, "上传失败:type:${ossType}", tr = exp)
                UploadRes.makeFailRes()
            }

        }


    /**
     * 生成上传文件的文件, 包括扩展名
     * @param file: 上传的文件
     * @param randomFileName:   是否要重新随机生成一个文件名
     */
    private fun getUploadFileName(
        file: File,
        randomFileName: Boolean,
    ): String {
        return if (randomFileName) {
            val extension = file.extension.addSymbolWhenNotEmpty(".")
            "${createUUID(true)}${extension}"
        } else {
            file.name
        }
    }

    /**
     * 如果字符串不为空, 并且不是以[symbol]开头, 则会添加symbol
     */
    private fun String.addSymbolWhenNotEmpty(symbol: String): String {
        return if (!isEmpty() && !this.startsWith(symbol)) {
            "${symbol}${this}"
        } else {
            this
        }
    }

    /**
     * 根据ossType的类型,获取对应的OSS实例
     * 他么的Token接口, 文件路径不同
     */
    private fun getOSS(): OSS = when (ossType) {
        TmpOSSType.TEMP_UPLOAD -> tmpOss
        TmpOSSType.LOG -> logOss
    }

}