package com.czur.starry.device.temposs

import com.czur.czurutils.log.logTagD
import com.alibaba.sdk.android.oss.ClientConfiguration
import com.alibaba.sdk.android.oss.OSS
import com.alibaba.sdk.android.oss.OSSClient
import com.alibaba.sdk.android.oss.common.auth.OSSFederationCredentialProvider
import com.alibaba.sdk.android.oss.common.auth.OSSFederationToken
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.network.HttpManager

/**
 * Created by 陈丰尧 on 2021/9/15
 */
private const val TAG = "OSSCore"

/**
 * 核心的OSS对象, 在构造过程中,保证单例
 */
internal val tmpOss: OSS by lazy {
    initOSS(TmpOSSType.TEMP_UPLOAD)
}
internal val logOss: OSS by lazy {
    initOSS(TmpOSSType.LOG)
}
private val ossService: IOSSService by lazy { HttpManager.getService() }


private fun initOSS(ossType: TmpOSSType): OSS {
    val credentialProvider = object : OSSFederationCredentialProvider() {
        override fun getFederationToken(): OSSFederationToken = makeOSSFederationToken(ossType)
    }
    return OSSClient(
        CZURAtyManager.appContext,
        Constants.TMP_END_POINT,
        credentialProvider,
        makeOSSConfig()
    )
}

/**
 * 生成OSSConfig信息
 */
private fun makeOSSConfig(): ClientConfiguration {
    val conf = ClientConfiguration()
    conf.connectionTimeout = 15 * 1000 // 连接超时，默认15秒。
    conf.socketTimeout = 15 * 1000 // socket超时，默认15秒。
    conf.maxConcurrentRequest = 5 // 最大并发请求数，默认5个。
    conf.maxErrorRetry = 1 // 失败后最大重试次数，默认2次。
    return conf
}

private fun makeOSSFederationToken(ossType: TmpOSSType): OSSFederationToken {

    val ossTokenEntity = when (ossType) {
        TmpOSSType.TEMP_UPLOAD -> ossService.getTmpToken()
        TmpOSSType.LOG -> ossService.getLogsToken()
    }.withCheck().body
    logTagD(TAG, "获取Token完成")
    return OSSFederationToken(
        ossTokenEntity.accessKeyId,
        ossTokenEntity.accessKeySecret,
        ossTokenEntity.securityToken,
        ossTokenEntity.expiration
    )
}