package com.czur.starry.device.diagnosislib

import com.czur.czurutils.log.logTagE
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagW
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2021/9/28
 */
class DiagnosisUtil {
    companion object {
        private const val TAG = "DiagnosisUtil"

        private const val SERVICE_ACTION = "com.czur.starry.device.diagnosis.Diagnosis"
        private const val SERVICE_PKG = "com.czur.starry.device.diagnosis"
    }


    private val conn = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder) {
            diagnosis = IDiagnosis.Stub.asInterface(service)
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            logTagW(TAG, "onServiceDisconnected")
            diagnosis = null
        }
    }

    private var diagnosis: IDiagnosis? = null
    private var cacheContext: Context? = null

    fun bindService(context: Context) {
        val intent = Intent().apply {
            action = SERVICE_ACTION
            `package` = SERVICE_PKG
        }
        cacheContext = context
        context.bindService(intent, conn, Context.BIND_AUTO_CREATE)
    }

    fun unbindService(context: Context) {
        context.unbindService(conn)
        cacheContext = null
    }

    /**
     * 上传意见反馈
     * @param feedbackType      反馈类型
     * @param contactInformation:    联系方式
     * @param collectLog:   是否收集Log
     * @param feedback:     反馈内容
     */
    suspend fun uploadFeedback(
        feedbackType: String?,
        contactInformation: String?,
        collectLog: Boolean,
        feedback: String?,
    ): Boolean {
        val feedbackContent = if (feedback.isNullOrEmpty()) null else feedback
        return withContext(Dispatchers.IO) {
            try {
                if (diagnosis == null) {
                    logTagW(TAG, "意见反馈失败, 未绑定服务")
                    cacheContext?.let {
                        logTagI(TAG, "尝试重新绑定服务")
                        bindService(it)
                        delay(300)
                        return@withContext uploadFeedback(
                            feedbackType,
                            contactInformation,
                            collectLog,
                            feedback)
                    }
                    return@withContext false
                }
                diagnosis?.collectionAndUpload(
                    feedbackContent,
                    feedbackType,
                    contactInformation,
                    collectLog
                )
                    ?: false
            } catch (exp: Exception) {
                logTagE(TAG, "意见反馈失败", tr = exp)
                false
            }
        }
    }
}