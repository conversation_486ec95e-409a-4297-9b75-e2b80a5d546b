// IDiagnosis.aidl
package com.czur.starry.device.diagnosislib;

// Declare any non-default types here with import statements

interface IDiagnosis {
    /**
     * 手机Log并上传 是耗时操作, 请在子线程中调用
     * @params feedback 意见反馈, 可以为空
     * @params deviceFeedbackType 意见反馈类型
     * @params contact 是否通过手机联系用户
     * @params collectLog 是否收集Log
     */
    boolean collectionAndUpload(String feedback, String deviceFeedbackType, String contactInformation, boolean collectLog);
}