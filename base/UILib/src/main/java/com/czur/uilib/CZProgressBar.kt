package com.czur.uilib

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View

/**
 * Created by 陈丰尧 on 2023/8/2
 */
class CZProgressBar @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : View(context, attrs, defStyleAttr) {
    private val defBgColor: Int by color(R.color.cz_progress_bar_bg)
    private val defProgressColor: Int by color(R.color.cz_progress_bar_progress)

    private val bgColor: Int
    private val progressColor: Int

    var max: Long = 100L
        set(value) {
            if (value <= 0) throw IllegalArgumentException("max must be positive")
            if (field == value) return
            field = value
            invalidate()
        }
    var progress: Long = 0L
        set(value) {
            if (value < 0) throw IllegalArgumentException("progress must be positive")
            if (field == value) return

            field = value
            invalidate()
        }
    private val paint = Paint().apply {
        isAntiAlias = true
        style = Paint.Style.FILL
    }

    private val radius: Float by lazy(LazyThreadSafetyMode.NONE) { height / 2F }

    init {
        val ta = context.obtainStyledAttributes(attrs, R.styleable.CZProgressBar)
        progress =
            ta.getInt(R.styleable.CZProgressBar_czPbProgress, 0).toLong()
        max = ta.getInt(R.styleable.CZProgressBar_czPbMax, 100).toLong()
        bgColor =
            ta.getColor(R.styleable.CZProgressBar_czPbBgColor, defBgColor)
        progressColor =
            ta.getColor(R.styleable.CZProgressBar_czPbProgressColor, defProgressColor)
        ta.recycle()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        // 绘制背景
        paint.color = bgColor
        val drawWidth = width.toFloat()
        val drawHeight = height.toFloat()
        canvas.drawRoundRect(0F, 0F, drawWidth, drawHeight, radius, radius, paint)
        // 绘制progress
        val progressLength = drawWidth * progress / max
        paint.color = progressColor
        canvas.drawRoundRect(0F, 0F, progressLength, drawHeight, radius, radius, paint)
    }

}