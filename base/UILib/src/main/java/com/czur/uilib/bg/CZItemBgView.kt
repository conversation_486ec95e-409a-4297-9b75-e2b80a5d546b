package com.czur.uilib.bg

import android.content.Context
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.czur.uilib.R

private const val RADIUS = 10F  // 圆角半径

private enum class ItemPosition(val value: Int) {
    TOP(0),
    MID(1),
    BOTTOM(2),
    SINGLE(3);

    companion object {
        fun fromValue(value: Int) = entries.first { it.value == value }
    }

    fun createCornerRadii(ignoreCorner: Boolean): FloatArray {
        if (ignoreCorner) {
            return floatArrayOf(
                0F, 0F, 0F, 0F,
                0F, 0F, 0F, 0F
            )
        }
        return when (this) {
            TOP -> floatArrayOf(
                RADIUS, RADIUS, RADIUS, RADIUS,
                0F, 0F, 0F, 0F
            )

            MID -> floatArrayOf(
                0F, 0F, 0F, 0F,
                0F, 0F, 0F, 0F
            )

            BOTTOM -> floatArrayOf(
                0F, 0F, 0F, 0F,
                RADIUS, RADIUS, RADIUS, RADIUS
            )

            SINGLE -> floatArrayOf(
                RADIUS, RADIUS, RADIUS, RADIUS,
                RADIUS, RADIUS, RADIUS, RADIUS
            )
        }
    }
}

class CZItemBgView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : View(context, attrs, defStyleAttr) {
    private var itemPosition: ItemPosition

    private var selDrawable: Drawable
    private var hoverDrawable: Drawable
    private var defNormalDrawable: Drawable
    private var ignoreHover: Boolean

    private var customNormalDrawable: Drawable? = null

    private val normalDrawable: Drawable
        get() = customNormalDrawable ?: defNormalDrawable

    private var ignoreCorner = false

    private var syncColorViews = mutableListOf<View>()

    init {
        val ta = context.obtainStyledAttributes(
            attrs,
            R.styleable.CZItemBgView
        )
        itemPosition = ItemPosition.Companion.fromValue(
            ta.getInt(
                R.styleable.CZItemBgView_czItemPosition,
                ItemPosition.SINGLE.value
            )
        )
        ignoreCorner = ta.getBoolean(R.styleable.CZItemBgView_czIgnoreCorner, false) // 是否忽略圆角
        ignoreHover = ta.getBoolean(R.styleable.CZItemBgView_czIgnoreHover, false)
        val syncSelectColorIds =
            ta.getString(R.styleable.CZItemBgView_czSyncSelectColorIDs).orEmpty()
        if (syncSelectColorIds.isNotEmpty()) {
            post {
                initSyncColorViews(syncSelectColorIds)
            }
        }

        val select = ta.getBoolean(R.styleable.CZItemBgView_czSelected, false)
        val hoverWithId = ta.getResourceId(R.styleable.CZItemBgView_czHoverWith, 0)
        if (hoverWithId != 0 && !ignoreHover) {
            post {
                // 通过hoverWithId找到对应的view, 并设置监听,否则上面覆盖View, 会导致本身的Over监听失效
                val hoverWithView = (parent as View).findViewById(hoverWithId) as View
                hoverWithView.setOnHoverListener { _, event ->
                    if (event.action == MotionEvent.ACTION_HOVER_ENTER) {
                        isHovered = true
                    } else if (event.action == MotionEvent.ACTION_HOVER_EXIT) {
                        isHovered = false
                    }
                    false
                }
            }

        }

        // 自定义正常状态颜色
        val normalColor = ta.getColor(
            R.styleable.CZItemBgView_czNormalColor,
            resources.getColor(R.color.cz_item_bg_normal, null)
        )

        ta.recycle()

        selDrawable = GradientDrawable().apply {
            setColor(resources.getColor(R.color.cz_item_bg_selected, null))
            cornerRadii = itemPosition.createCornerRadii(ignoreCorner)
        }
        hoverDrawable = GradientDrawable().apply {
            setColor(resources.getColor(R.color.cz_item_bg_hover, null))
            cornerRadii = itemPosition.createCornerRadii(ignoreCorner)
        }
        defNormalDrawable = GradientDrawable().apply {
            setColor(normalColor)
            cornerRadii = itemPosition.createCornerRadii(ignoreCorner)
        }

        background = normalDrawable
        isClickable = true
        isSelected = select
    }

    private fun initSelDrawable() {
        selDrawable = GradientDrawable().apply {
            setColor(resources.getColor(R.color.cz_item_bg_selected, null))
            cornerRadii = itemPosition.createCornerRadii(ignoreCorner)
        }
        hoverDrawable = GradientDrawable().apply {
            setColor(resources.getColor(R.color.cz_item_bg_hover, null))
            cornerRadii = itemPosition.createCornerRadii(ignoreCorner)
        }
        defNormalDrawable = GradientDrawable().apply {
            setColor(resources.getColor(R.color.cz_item_bg_normal, null))
            cornerRadii = itemPosition.createCornerRadii(ignoreCorner)
        }
    }

    private fun initSyncColorViews(idsStr: String) {
        val ids = idsStr.split(",")
        if (ids.isEmpty()) return
        val parent = parent as? ViewGroup ?: return
        ids.forEach {
            val id = resources.getIdentifier(it.trim(), "id", context.packageName)
            if (id != 0) {
                val view = parent.findViewById(id) as View
                syncColorViews.add(view)
            }
        }
        syncViewColor()
    }

    fun setHoverItemPosition(position: Int, length: Int) {
        itemPosition = if (length <= 1) {
            ItemPosition.SINGLE
        } else {
            when (position) {
                0 -> ItemPosition.TOP
                length - 1 -> ItemPosition.BOTTOM
                else -> ItemPosition.MID
            }

        }
        initSelDrawable()
        changeDrawable()
    }

    fun setCustomBgColor(color: Int) {
        customNormalDrawable = GradientDrawable().apply {
            setColor(color)
            cornerRadii = itemPosition.createCornerRadii(ignoreCorner)
        }
        changeDrawable()
    }

    fun setIgnoreHover(ignore: Boolean) {
        if (ignoreHover != ignore) {
            ignoreHover = ignore
            changeDrawable()
        }
    }

    /**
     * 改变背景
     * 1. 选中则展示选中状态(此时忽略鼠标悬浮状态)
     * 2. 鼠标悬浮, 则展示悬浮状态
     * 3. 其他情况展示正常状态
     */
    private fun changeDrawable() {
        background = when {
            isEnabled && isSelected -> selDrawable
            isEnabled && isHovered && !ignoreHover -> hoverDrawable
            else -> normalDrawable
        }
    }

    override fun onHoverChanged(hovered: Boolean) {
        super.onHoverChanged(hovered)
        if (!ignoreHover) {
            changeDrawable()
        }
    }

    override fun setEnabled(enabled: Boolean) {
        super.setEnabled(enabled)
        changeDrawable()
    }

    override fun setSelected(selected: Boolean) {
        super.setSelected(selected)
        changeDrawable()
        syncViewColor()
    }

    private fun syncViewColor() {
        syncColorViews.forEach {
            when (it) {
                is TextView -> {
                    if (isSelected) {
                        it.setTextColor(resources.getColor(R.color.cz_item_text_sel, null))
                    } else {
                        it.setTextColor(resources.getColor(R.color.text_common, null))
                    }
                }

                is ImageView -> {
                    if (isSelected) {
                        it.setColorFilter(resources.getColor(R.color.cz_item_text_sel, null))
                    } else {
                        it.setColorFilter(resources.getColor(R.color.bg_main_blue, null))
                    }
                }
            }
        }
    }
}