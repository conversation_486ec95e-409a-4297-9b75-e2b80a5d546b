package com.czur.uilib.et

import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.View
import android.widget.EditText
import androidx.appcompat.widget.AppCompatEditText
import androidx.core.widget.doAfterTextChanged
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.utils.clearContentText
import com.czur.starry.device.baselib.utils.darkContentTextColor
import com.czur.starry.device.baselib.utils.keyboard.keyboardHide
import com.czur.uilib.R

/**
 * Created by 陈丰尧 on 2024/8/5
 */
class AutoChangeFocusEditText @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = android.R.attr.editTextStyle
) : AppCompatEditText(context, attrs, defStyleAttr) {

    //当前是否编辑状态
    var isEditedStatus = false

    // 是否自动清除文本
    var autoClearText = true

    init {
        setHintTextColor(0x33000000)
        setBackgroundResource(R.drawable.bg_auto_change_focus_et)

        doAfterTextChanged {
            val changeText = it?.toString() ?: ""

            if (changeText.length > 1) {
                // 取最后一个字符
                setText(changeText.substring(changeText.length - 1))
                return@doAfterTextChanged
            }

            if (changeText.isNotEmpty() && hasFocus()) {
                // 切换到下一个焦点
                val nextFocus: View? = focusSearch(FOCUS_DOWN)
                if (nextFocus !is EditText) {
                    // 焦点View不是EditText,隐藏键盘
                    keyboardHide()
                }
                if (nextFocus != null) {
                    nextFocus.requestFocus()
                } else {
                    clearFocus()
                }
            }
        }
    }

    override fun onFocusChanged(focused: Boolean, direction: Int, previouslyFocusedRect: Rect?) {
        super.onFocusChanged(focused, direction, previouslyFocusedRect)
        if (focused) {
            isEditedStatus = true
            if (autoClearText) {
                clearContentText()
            }
        } else {
            isEditedStatus = false
        }
    }


}