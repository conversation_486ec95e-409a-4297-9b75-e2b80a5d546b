package com.czur.uilib.btn

import android.app.Activity
import android.content.Context
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import android.view.MotionEvent
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.appcompat.widget.AppCompatImageView
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.uilib.R
import com.czur.uilib.color

/**
 * Created by 陈丰尧 on 2023/8/2
 * 通用的返回按钮
 * 默认是结束页面
 */
private const val RADIUS = 10F  // 圆角半径
private const val SCALE_HOVER_ENTER = 1.2F
private const val SCALE_HOVER_NORMAL = 1F
private const val DURATION_SCALE_ANIM = 100L

class CZBackBtn @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : FrameLayout(context, attrs, defStyleAttr) {
    private val backgroundColor: Int by color(R.color.btn_bg_back)
    private val backIv: ImageView by lazy {
        AppCompatImageView(context).apply {
            setImageResource(R.drawable.ic_cz_back_btn)
        }
    }

    private var onBackClickListener: (() -> Unit)? = null

    init {
        addView(backIv, LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT).apply {
            gravity = android.view.Gravity.CENTER
        })

        val backGroundDrawable = GradientDrawable().apply {
            setColor(backgroundColor)
            cornerRadius = RADIUS
        }
        background = backGroundDrawable

        setOnHoverListener { v, event ->
            when (event.action) {
                MotionEvent.ACTION_HOVER_ENTER -> {
                    backIv.animate()
                        .scaleX(SCALE_HOVER_ENTER)
                        .scaleY(SCALE_HOVER_ENTER)
                        .setDuration(DURATION_SCALE_ANIM)
                        .start()
                    true
                }

                MotionEvent.ACTION_HOVER_EXIT -> {
                    backIv.animate()
                        .scaleX(SCALE_HOVER_NORMAL)
                        .scaleY(SCALE_HOVER_NORMAL)
                        .setDuration(DURATION_SCALE_ANIM)
                        .start()
                    true
                }

                else -> {
                    false
                }
            }
        }

        setOnDebounceClickListener {
            onBackClickListener?.invoke() ?: kotlin.run {
                (context as? Activity)?.finish()
            }
        }
    }

    /**
     * 设置点击事件, 会代替默认的结束页面
     */
    fun setOnBackClickListener(onBackClickListener: (() -> Unit)?) {
        this.onBackClickListener = onBackClickListener
    }
}