package com.czur.uilib.choose

import android.content.Context
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.PointerIcon
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.AppCompatImageView
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.uilib.R

/**
 * Created by 陈丰尧 on 2022/8/6
 */
class CZImageCheckBox @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {
    private var checkedDrawable: Drawable? = null
    private var unCheckedDrawable: Drawable? = null
    var isChecked: Boolean = false
        set(value) {
            field = value
            refreshUI()
        }

    /**
     * 选中状态改变回调
     * 只有用户手动点击了,才调用
     */
    var onCheckChangeListener: ((checked: Boolean) -> Unit)? = null


    init {
        val ta = context.obtainStyledAttributes(attrs, R.styleable.CZImageCheckBox)
        checkedDrawable = ta.getDrawable(R.styleable.CZImageCheckBox_checkedImg)
        unCheckedDrawable = ta.getDrawable(R.styleable.CZImageCheckBox_unCheckedImg)
        isChecked = ta.getBoolean(R.styleable.CZImageCheckBox_checked, false)
        ta.recycle()

        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.CZBindClickView)
        val withClickIds = typedArray.getString(R.styleable.CZBindClickView_withClickIDs) ?: ""
        typedArray.recycle()
        if (withClickIds.isNotEmpty()) {
            post {
                bindOtherViewClick(withClickIds)
            }
        }

        refreshUI()

        pointerIcon = PointerIcon.getSystemIcon(context, PointerIcon.TYPE_ARROW) // 防止出现小手


        setOnDebounceClickListener {
            isChecked = !isChecked
            onCheckChangeListener?.invoke(isChecked)
        }
    }

    private fun refreshUI() {
        if (isChecked) {
            setImageDrawable(checkedDrawable)
        } else {
            setImageDrawable(unCheckedDrawable)
        }
    }

    private fun bindOtherViewClick(withClickIds: String) {
        val ids = withClickIds.split(",")
        val parent = parent as? ViewGroup ?: return
        ids.forEach {
            val id = resources.getIdentifier(it.trim(), "id", context.packageName)
            if (id != 0) {
                val view = parent.findViewById<View>(id)
                view.setOnDebounceClickListener {
                    performClick()
                }
            }
        }
    }
}