package com.czur.uilib.choose

import android.animation.Animator
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PathMeasure
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateDecelerateInterpolator
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.uilib.R
import com.czur.uilib.color
import kotlin.math.min
import androidx.core.content.withStyledAttributes

/**
 * Created by 陈丰尧 on 2023/8/1
 * 复选框
 */

private const val RADIUS = 10F // 圆角半径
private const val BORDER_WIDTH = 5F // 边框宽度

private const val ON_VALUE = 1F
private const val OFF_VALUE = 0F

private const val ANIM_DURATION = 250L   // 动画时长

class CZCheckBox @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    private var bgColor: Int = 0
    private var checkMarkColor: Int = 0

    private val markPath: Path by lazy(LazyThreadSafetyMode.NONE) {
        initCheckMarkPath()
    }
    private val pathMeasure: PathMeasure by lazy(LazyThreadSafetyMode.NONE) {
        PathMeasure(markPath, false).also {
            markPathLength = it.length
        }
    }
    private var markPathLength = 0F
    private val checkMarkWidth: Float by lazy {
        width / 20F
    }
    private val drawPath = Path()

    private val paint by lazy {
        Paint().apply {
            isAntiAlias = true
        }
    }
    private var targetState: Float? = null
    private var animProcess = OFF_VALUE    // 动画进度(0:OFF, 1:ON)
        set(value) {
            field = value
            if (value == ON_VALUE || value == OFF_VALUE) {
                // target已经完成
                targetState = null
            }
        }
    private val isAnim      // 是否正在动画中
        get() = animProcess != 0F && animProcess != 1F

    private var currentAnim: ValueAnimator = ValueAnimator.ofFloat().apply {
        duration = ANIM_DURATION
        interpolator = AccelerateDecelerateInterpolator()
        addUpdateListener {
            if (it.isRunning) {
                animProcess = it.animatedValue as Float
                invalidate()
            }
        }
    }

    // listener
    private var onCheckedChangeListener: ((isOn: Boolean, fromUser: Boolean) -> Unit)? = null

    private var cusRadius: Float = RADIUS

    var blockOperation: BlockOperation? = null

    init {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.CZBindClickView)
        val withClickIds = typedArray.getString(R.styleable.CZBindClickView_withClickIDs) ?: ""
        typedArray.recycle()
        if (withClickIds.isNotEmpty()) {
            post {
                bindOtherViewClick(withClickIds)
            }
        }

        context.withStyledAttributes(attrs, R.styleable.CZCheckBox) {
            cusRadius = getFloat(R.styleable.CZCheckBox_cusRadius, RADIUS)
            bgColor = getColor(
                R.styleable.CZCheckBox_cusBgColor,
                context.getColor(R.color.cz_checkbox_bg)
            )
            checkMarkColor = getColor(
                R.styleable.CZCheckBox_cusMarkColor,
                context.getColor(R.color.cz_checkbox_check_mark)
            )
        }

        setOnDebounceClickListener {
            if (isAnim) return@setOnDebounceClickListener
            if (isChecked()) {
                changeCheckedState(checked = false, useAnim = true, fromUser = true)
            } else {
                changeCheckedState(checked = true, useAnim = true, fromUser = true)
            }
        }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        // 1. 绘制底色
        paint.style = Paint.Style.FILL
        paint.color = bgColor
        canvas.drawRoundRect(0F, 0F, width.toFloat(), height.toFloat(), cusRadius, cusRadius, paint)

        // 2. 绘制勾
        paint.style = Paint.Style.STROKE
        paint.strokeCap = Paint.Cap.ROUND
        paint.strokeJoin = Paint.Join.ROUND
        paint.strokeWidth = checkMarkWidth
        paint.color = checkMarkColor

        drawPath.reset()
        pathMeasure.getSegment(0F, markPathLength * animProcess, drawPath, true)
        canvas.drawPath(drawPath, paint)

        // 3. 绘制遮挡的白框
        paint.style = Paint.Style.FILL
        paint.color = checkMarkColor
        paint.alpha = (255 * (1 - animProcess)).toInt()
        // 计算遮挡框的宽度
        val coverEdgeMax = width - BORDER_WIDTH
        val coverEdgeMin = width * 0.8F
        val coverEdge = (coverEdgeMax - coverEdgeMin) * (1 - animProcess) + coverEdgeMin
        if (coverEdge > 0) {
            val radiusMin = cusRadius - BORDER_WIDTH / 2F  // 内部白框的圆角小一些, 好看一些, 不然四个角会比较粗
            val radiusMax = coverEdgeMin / 2F
            val radiusResult = (radiusMax - radiusMin) * (animProcess) + radiusMin
            canvas.drawRoundRect(
                (width - coverEdge) / 2F,
                (height - coverEdge) / 2F,
                (width + coverEdge) / 2F,
                (height + coverEdge) / 2F,
                radiusResult,
                radiusResult,
                paint
            )
        }
    }


    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        // 如果不是正方形, 则强制修改成正方形
        val width = MeasureSpec.getSize(widthMeasureSpec)
        val height = MeasureSpec.getSize(heightMeasureSpec)
        if (width == height) {
            super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        } else {
            val size = min(width, height)
            setMeasuredDimension(size, size)
        }
    }

    fun setOnCheckedChangeListener(listener: ((isOn: Boolean, fromUser: Boolean) -> Unit)?) {
        onCheckedChangeListener = listener
    }

    fun setChecked(checked: Boolean, useAnim: Boolean = true) {
        changeCheckedState(checked, useAnim, false)
    }

    fun isChecked(): Boolean = (targetState?.let { it == ON_VALUE } ?: animProcess) == ON_VALUE


    private fun changeCheckedState(checked: Boolean, useAnim: Boolean, fromUser: Boolean) {
        val setState = if (checked) ON_VALUE else OFF_VALUE
        if (targetState == setState) {
            return
        } // 已经是目标状态了
        if (blockOperation?.shouldBlock(checked, fromUser) == true) return // 阻止操作

        cancelAnim()
        if (useAnim) {
            startAnim(animProcess, setState, fromUser)
        } else {
            targetState = null
            animProcess = setState
            invalidate()
            onCheckedChangeListener?.invoke(animProcess == ON_VALUE, fromUser)
        }
    }

    private fun bindOtherViewClick(withClickIds: String) {
        val ids = withClickIds.split(",")
        val parent = parent as? ViewGroup ?: return
        ids.forEach {
            val id = resources.getIdentifier(it.trim(), "id", context.packageName)
            if (id != 0) {
                val view = parent.findViewById<View>(id)
                view.setOnDebounceClickListener {
                    performClick()
                }
            }
        }
    }

    private fun initCheckMarkPath(): Path {
        val path = Path()
        val startXPosition = 0.25F
        val startYPosition = 0.45F

        val middleXPosition = 0.45F
        val middleYPosition = 0.65F

        val endXPosition = 0.75F
        val endYPosition = 0.35F

        path.moveTo(width * startXPosition, height * startYPosition)
        path.lineTo(width * middleXPosition, height * middleYPosition)
        path.lineTo(width * endXPosition, height * endYPosition)
        return path
    }


    private fun startAnim(start: Float, end: Float, fromUser: Boolean) {
        currentAnim.setFloatValues(start, end)
        val listener = object : Animator.AnimatorListener {
            override fun onAnimationEnd(animator: Animator) {
                currentAnim.removeListener(this)
            }

            override fun onAnimationCancel(animator: Animator) {
                targetState = null
                currentAnim.removeListener(this)
            }

            override fun onAnimationRepeat(animation: Animator) {}

            override fun onAnimationStart(animator: Animator) {
                targetState = end
                onCheckedChangeListener?.invoke(end == ON_VALUE, fromUser)
            }
        }
        currentAnim.addListener(listener)
        currentAnim.start()
    }

    private fun cancelAnim() {
        if (currentAnim.isRunning) {
            currentAnim.cancel()
        }
    }

    interface BlockOperation {
        fun shouldBlock(willChecked: Boolean, fromUser: Boolean): Boolean
    }

    object BlockUserCheckOperation : BlockOperation {
        override fun shouldBlock(willChecked: Boolean, fromUser: Boolean): Boolean {
            return willChecked && fromUser
        }
    }

    object BlockUserUnCheckOperation : BlockOperation {
        override fun shouldBlock(willChecked: Boolean, fromUser: Boolean): Boolean {
            return !willChecked && fromUser
        }
    }
}