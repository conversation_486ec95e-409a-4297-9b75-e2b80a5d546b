package com.czur.uilib.seek

/**
 * Created by 陈丰尧 on 2023/8/1
 */
/**
 * 用于监听SeekBar的进度改变
 */
typealias ProgressChangedListener = (progress: Int, fromUser: Boolean) -> Unit

/**
 * 用于监听SeekBar的进度改变完成
 * 1. 用户拖动完成
 * 2. 点击按钮改变完成
 */
typealias ProgressChangeCompletedListener = (progress: Int, fromUser: Boolean) -> Unit

internal enum class Boundary {
    MIN, MAX, MID
}
internal typealias ProgressReachesBoundaryListener = (boundary: Boundary) -> Unit