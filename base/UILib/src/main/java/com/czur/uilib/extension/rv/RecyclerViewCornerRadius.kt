package com.czur.uilib.extension.rv

import android.graphics.Canvas
import android.graphics.Path
import androidx.recyclerview.widget.RecyclerView

/**
 * Created by 陈丰尧 on 2023/8/3
 */
private const val RV_CORNER_RADIUS_DEF = 10F    // rv的默认圆角是10dp

/**
 * 为RecyclerView设置圆角
 * @param radius 圆角半径
 * @param backgroundColor 背景色
 */
class RecyclerViewCornerRadius(
    private val radius: Float = RV_CORNER_RADIUS_DEF,
    private val backgroundColor: Int? = null
) : RecyclerView.ItemDecoration() {
    private val clipPath = Path()

    override fun onDraw(c: Canvas, parent: RecyclerView, state: RecyclerView.State) {
        super.onDraw(c, parent, state)
        calculationPath(parent)
        c.clipPath(clipPath)
        backgroundColor?.let {
            c.drawColor(it)
        }
    }


    private fun calculationPath(parent: RecyclerView) {
        clipPath.reset()
        clipPath.addRoundRect(
            0F,
            0F,
            parent.width.toFloat(),
            parent.height.toFloat(),
            radius,
            radius,
            Path.Direction.CW
        )
    }
}

fun RecyclerView.addCornerRadius(
    radius: Float = RV_CORNER_RADIUS_DEF,
    backgroundColor: Int? = null
) {
    addItemDecoration(RecyclerViewCornerRadius(radius, backgroundColor))
}