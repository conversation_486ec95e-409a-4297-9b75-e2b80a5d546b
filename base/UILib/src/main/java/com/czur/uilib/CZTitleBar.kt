package com.czur.uilib

import android.app.Activity
import android.content.Context
import android.content.res.ColorStateList
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import androidx.constraintlayout.widget.ConstraintLayout
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.utils.getString
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.uilib.databinding.WidgetCzTitleBarBinding

/**
 * Created by 陈丰尧 on 2023/7/31
 * 高度固定为140px
 * 1. 左上角返回箭头: 默认点击事件时Finish当前Activity
 * 2. 中间标题: 默认为App名称
 */
private const val SCALE_HOVER_ENTER = 1.2F
private const val SCALE_HOVER_NORMAL = 1F
private const val DURATION_SCALE_ANIM = 100L

class CZTitleBar @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val binding by lazy {
        WidgetCzTitleBarBinding.inflate(LayoutInflater.from(context), this, true)
    }

    // 左上角返回箭头
    var onBackClick: (() -> Unit)? = null

    init {
        val ta = context.obtainStyledAttributes(attrs, R.styleable.BaseLib_TitleBar)
        val title = ta.getString(R.styleable.BaseLib_TitleBar_baselib_titlebar_title)
            ?: context.applicationInfo.loadLabel(context.packageManager).toString()
        binding.titleTv.text = title

        val color = ta.getColor(R.styleable.BaseLib_TitleBar_baselib_titlebar_color, 0)

        ta.recycle()
        if (color != 0) {
            binding.titleTv.setTextColor(color)
            binding.backIv.imageTintList = ColorStateList.valueOf(color)
        }

        if (Constants.starryHWInfo.hasTouchScreen) {
            binding.backIv.gone()
        }
        binding.backIv.setOnDebounceClickListener {
            onBackClick?.invoke() ?: kotlin.run {
                (context as? Activity)?.finish()
            }
        }

        // 动画
        binding.backIv.setOnHoverListener { v, event ->
            when (event.action) {
                MotionEvent.ACTION_HOVER_ENTER -> {
                    v.animate()
                        .scaleX(SCALE_HOVER_ENTER)
                        .scaleY(SCALE_HOVER_ENTER)
                        .setDuration(DURATION_SCALE_ANIM)
                        .start()
                    true
                }

                MotionEvent.ACTION_HOVER_EXIT -> {
                    v.animate()
                        .scaleX(SCALE_HOVER_NORMAL)
                        .scaleY(SCALE_HOVER_NORMAL)
                        .setDuration(DURATION_SCALE_ANIM)
                        .start()
                    true
                }

                else -> {
                    false
                }
            }
        }
    }

    fun setTitle(titleRes: Int) {
        val titleText = getString(titleRes)
        binding.titleTv.text = titleText
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        // 固定高度 140px, 忽略外部指定的高度
        val height = MeasureSpec.makeMeasureSpec(140, MeasureSpec.EXACTLY)
        super.onMeasure(widthMeasureSpec, height)
    }
}