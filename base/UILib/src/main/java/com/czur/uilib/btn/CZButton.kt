package com.czur.uilib.btn

import android.content.Context
import android.graphics.Canvas
import android.graphics.Path
import android.graphics.Typeface
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import android.view.Gravity
import android.view.PointerIcon
import android.view.View
import androidx.appcompat.widget.AppCompatButton
import androidx.core.view.ViewCompat
import com.czur.czurutils.log.logTagD
import com.czur.uilib.R
import com.czur.uilib.UI_DISABLE_ALPHA
import androidx.core.content.withStyledAttributes

/**
 * Created by 陈丰尧 on 2023/8/1
 * 和之前的CommonButton实际上是一样的，
 * 但是在禁用等状态时的UI不一样
 */
private const val RADIUS = 10F  // 圆角半径

/**
 * 颜色风格
 * 命名格式: 背景色_文字颜色
 */
private enum class CZButtonColorStyle(
    val attrFlag: Int,
    val bgColorRes: Int,
    val textColorRes: Int
) {
    BLUE_WHITE(0, R.color.cz_btn_blue_white_bg, R.color.cz_btn_blue_white_text),
    PALE_BLUE_BLUE(1, R.color.cz_btn_pale_blue_white_bg, R.color.cz_btn_pale_blue_white_text),
    ALPHA_WHITE_WHITE(
        2,
        R.color.cz_btn_alpha_white_white_bg,
        R.color.cz_btn_alpha_white_white_text
    ),
    POSITIVE_IN_BLUE(
        3,
        R.color.cz_btn_positive_bg_in_blue,
        R.color.cz_btn_positive_text_in_blue
    ),
    NEGATIVE_IN_BLUE(
        4,
        R.color.cz_btn_negative_bg_in_blue,
        R.color.cz_btn_negative_text_in_blue
    ),
    LITTLE_BLUE_WHITE(5, R.color.cz_btn_little_blue_bg, R.color.cz_btn_blue_white_text);


    companion object {
        fun createByAttrFlag(attrFlag: Int): CZButtonColorStyle {
            return entries.find { it.attrFlag == attrFlag } ?: BLUE_WHITE
        }
    }
}

private lateinit var colorStyle: CZButtonColorStyle

class CZButton @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : AppCompatButton(context, attrs, defStyleAttr) {
    private val clipPath = Path()

    init {
        context.withStyledAttributes(attrs, R.styleable.CZButton) {
            val attrFlag = getInt(R.styleable.CZButton_colorStyle, 0)
            colorStyle = CZButtonColorStyle.createByAttrFlag(attrFlag)
        }
        // 指定默认鼠标样式, 防止出现小手
        pointerIcon = PointerIcon.getSystemIcon(getContext(), PointerIcon.TYPE_ARROW)

        initColorStyle()
        initOtherStyle()
    }

    private fun initOtherStyle() {
        gravity = Gravity.CENTER
        setTypeface(null, Typeface.BOLD)
    }

    override fun draw(canvas: Canvas) {
        clipPath.reset()
        clipPath.addRoundRect(
            0F,
            0F,
            width.toFloat(),
            height.toFloat(),
            RADIUS,
            RADIUS,
            Path.Direction.CW
        )
        canvas.clipPath(clipPath)
        super.draw(canvas)
    }

    private fun initColorStyle() {
        setBackgroundColor(resources.getColor(colorStyle.bgColorRes,null))
        setTextColor(resources.getColor(colorStyle.textColorRes, null))
    }

    override fun setEnabled(enabled: Boolean) {
        super.setEnabled(enabled)
        alpha = if (enabled) 1F else UI_DISABLE_ALPHA
    }
}