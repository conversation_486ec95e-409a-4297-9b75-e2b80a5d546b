<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/menuIconIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <TextView
        android:id="@+id/menuTitleTv"
        android:layout_width="280px"
        android:layout_height="wrap_content"
        android:layout_marginLeft="17px"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        android:singleLine="true"
        android:ellipsize="end"
        app:float_tips="@string/float_tip_empty"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/menuIconIv"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/dividerTop"
        android:layout_width="0px"
        android:layout_height="1px"
        android:background="@color/white"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="@id/menuIconIv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/dividerBottom"
        android:layout_width="0px"
        android:layout_height="1px"
        android:background="@color/white"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@id/menuIconIv"
        app:layout_constraintRight_toRightOf="parent" />

</merge>