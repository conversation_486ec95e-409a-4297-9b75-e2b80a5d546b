<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F88"
    android:orientation="vertical"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.czur.uilib.seek.CZSeekBar
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"/>
    
    <com.czur.uilib.CZSwitch
        android:layout_width="94px"
        android:layout_height="44px"/>

    <com.czur.uilib.choose.CZMultiStateCheckBox
        android:layout_width="50px"
        android:layout_height="50px"/>

    <com.czur.uilib.choose.CZMultiStateCheckBox
        android:layout_width="200px"
        android:layout_height="200px"/>

    <com.czur.uilib.CZProgressBar
        android:layout_width="820px"
        android:layout_height="6px"
        app:czPbMax="100"
        app:czPbProgress="50"/>
</LinearLayout>