<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="130px"
    tools:ignore="PxUsage">

    <ImageView
        android:id="@+id/backIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="35px"
        android:padding="15px"
        android:src="@drawable/ic_cz_title_back"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <Space
        android:id="@+id/space"
        android:layout_width="35px"
        android:layout_height="match_parent"
        app:layout_constraintLeft_toRightOf="@id/backIv" />

    <TextView
        android:id="@+id/titleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:includeFontPadding="false"
        android:textColor="@color/text_title_bar_content"
        android:textSize="30px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/space"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="设置" />

</androidx.constraintlayout.widget.ConstraintLayout>