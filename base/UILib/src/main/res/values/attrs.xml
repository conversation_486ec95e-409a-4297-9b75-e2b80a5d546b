<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="CZTitleBar">
        <attr name="title" format="string" />
    </declare-styleable>

    <declare-styleable name="CZSeekBar">
        <attr name="showActionBtn" format="boolean" />
        <attr name="min" format="integer" />
        <attr name="max" format="integer" />
        <attr name="progress" format="integer" />
    </declare-styleable>

    <declare-styleable name="CZButton">
        <attr name="colorStyle" format="enum">
            <enum name="blueWhite" value="0" />
            <enum name="PaleBlueBlue" value="1" />
            <enum name="AlphaWhiteWhite" value="2" />
            <enum name="PositiveInBlue" value="3" />
            <enum name="NegativeInBlue" value="4" />
            <enum name="SkyBlueWhite" value="5" />
        </attr>
    </declare-styleable>

    <declare-styleable name="CZBindClickView">
        <attr name="withClickIDs" format="string" />
    </declare-styleable>

    <declare-styleable name="CZProgressBar">
        <attr name="czPbProgress" format="integer" />
        <attr name="czPbMax" format="integer" />
        <attr name="czPbProgressColor" format="color" />
        <attr name="czPbBgColor" format="color" />
    </declare-styleable>

    <declare-styleable name="CZRoundIndexTv">
        <attr name="czRoundIndexTvBgColor" format="color" />
        <attr name="czRoundIndexTvTextColor" format="color" />
    </declare-styleable>

    <declare-styleable name="CZMenuItem">
        <attr name="czMenuIcon" format="reference" />
        <attr name="czMenuTitle" format="string" />
    </declare-styleable>

    <declare-styleable name="CZItemBgView">
        <attr name="czItemPosition" format="enum">
            <enum name="top" value="0" />
            <enum name="middle" value="1" />
            <enum name="bottom" value="2" />
            <enum name="single" value="3" />
        </attr>
        <attr name="czHoverWith" format="reference" />
        <attr name="czIgnoreHover" format="boolean" />
        <attr name="czIgnoreCorner" format="boolean" />
        <attr name="czSelected" format="boolean" />
        <attr name="czNormalColor" format="color" />
        <attr name="czSyncSelectColorIDs" format="string" />
    </declare-styleable>

    <declare-styleable name="CZSwitch">
        <attr name="czSwitchBgOnColor" format="color" />
        <attr name="czSwitchBgOffColor" format="color" />
        <attr name="czSwitchThumbOnColor" format="color" />
        <attr name="czSwitchThumbOffColor" format="color" />
        <attr name="czSwitchTextOnColor" format="color" />
        <attr name="czSwitchTextOffColor" format="color" />
        <attr name="czSwitchBorderColor" format="color" />
    </declare-styleable>

    <declare-styleable name="CZCheckBox">
        <attr name="cusRadius" format="float" />
        <attr name="cusBgColor" format="color" />
        <attr name="cusMarkColor" format="color" />
    </declare-styleable>

    <declare-styleable name="CZImageCheckBox">
        <attr name="checkedImg" format="reference" />
        <attr name="unCheckedImg" format="reference" />
        <attr name="checked" format="boolean" />
    </declare-styleable>
</resources>