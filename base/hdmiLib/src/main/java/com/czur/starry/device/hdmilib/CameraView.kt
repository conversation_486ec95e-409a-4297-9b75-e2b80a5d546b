package com.czur.starry.device.hdmilib

import android.content.Context
import android.graphics.SurfaceTexture
import android.hardware.Camera
import android.util.AttributeSet
import android.view.SurfaceHolder
import android.view.SurfaceView
import android.view.TextureView
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import com.czur.czurutils.log.logTagV


/**
 * Created by 陈丰尧 on 2022/5/12
 */
class CameraView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {
    companion object {
        private const val TAG = "HDMI_CAMERA_VIEW"
        private const val MODE_SURFACE = 0
        private const val MODE_TEXTURE = 1
    }

    private val mode: Int

    private var displayView: View

    init {
        val ta = context.obtainStyledAttributes(attrs, R.styleable.CameraView)
        mode = ta.getInt(R.styleable.CameraView_cameraDisplayMode, MODE_SURFACE)
        ta.recycle()
        val layoutParams =
            ViewGroup.LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT)
        displayView = if (mode == MODE_SURFACE) {
            logTagV(TAG,"使用SurfaceView")
            SurfaceView(context)
        } else {
            logTagV(TAG,"使用TextureView")
            TextureView(context)
        }
        addView(displayView, layoutParams)
    }

    fun setCameraDisplay(camera: Camera) {
        if (mode == MODE_SURFACE) {
            camera.setPreviewDisplay((displayView as SurfaceView).holder)
        } else {
            camera.setPreviewTexture((displayView as TextureView).surfaceTexture)
        }
    }

    fun getSurfaceTexture() = (displayView as TextureView).surfaceTexture

    fun onReadyCallback(callback: () -> Unit) {
        if (mode == MODE_SURFACE) {
            (displayView as SurfaceView).holder.addCallback(object : SurfaceHolder.Callback {
                override fun surfaceCreated(holder: SurfaceHolder) {
                    callback()
                }

                override fun surfaceChanged(
                    holder: SurfaceHolder,
                    format: Int,
                    width: Int,
                    height: Int
                ) {
                }

                override fun surfaceDestroyed(holder: SurfaceHolder) {
                }
            })
        } else {
            (displayView as TextureView).surfaceTextureListener =
                object : TextureView.SurfaceTextureListener {
                    override fun onSurfaceTextureAvailable(
                        surface: SurfaceTexture,
                        width: Int,
                        height: Int
                    ) {
                        callback()
                    }

                    override fun onSurfaceTextureSizeChanged(
                        surface: SurfaceTexture,
                        width: Int,
                        height: Int
                    ) {
                    }

                    override fun onSurfaceTextureDestroyed(surface: SurfaceTexture): Boolean {
                        return true
                    }

                    override fun onSurfaceTextureUpdated(surface: SurfaceTexture) {
                    }

                }
        }
    }
}