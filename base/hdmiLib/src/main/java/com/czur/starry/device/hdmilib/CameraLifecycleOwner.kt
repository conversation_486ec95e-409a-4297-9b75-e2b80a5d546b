package com.czur.starry.device.hdmilib

import com.czur.czurutils.log.logTagD
import android.os.Handler
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import com.czur.starry.device.baselib.utils.doWithoutCatch

/**
 * Created by 陈丰尧 on 2021/11/11
 * 仿照LifecycleService 实现的LifecycleOwner
 * 只在包装的provider 执行到onDestroy时, 才会销毁
 * 否则保持在resume生命周期
 */
private const val TAG = "HDMI-CameraLifecycleOwner"

class CameraLifecycleOwner(provider: LifecycleOwner) : LifecycleOwner {
    private val dispatcher = CameraLifecycleDispatcher(provider)

    init {
        dispatcher.onCameraCreate()

    }

    fun onDestroy() {
        logTagD(TAG, "依赖的provider destroy")
        dispatcher.onCameraDestroy()
    }

    override val lifecycle: Lifecycle
        get() = dispatcher.registry
}

class CameraLifecycleDispatcher(provider: LifecycleOwner) {
    private val handler = Handler()
    val registry = LifecycleRegistry(provider)
    private var lastDispatchRunnable: DispatchRunnable? = null

    fun onCameraCreate() {
        postDispatchRunnable(Lifecycle.Event.ON_CREATE)
        postDispatchRunnable(Lifecycle.Event.ON_START)
    }

    fun onCameraDestroy() {
        postDispatchRunnable(Lifecycle.Event.ON_STOP)
        postDispatchRunnable(Lifecycle.Event.ON_DESTROY)
    }


    private fun postDispatchRunnable(event: Lifecycle.Event) {
        if (lastDispatchRunnable != null) {
            lastDispatchRunnable!!.run()
        }
        lastDispatchRunnable = DispatchRunnable(registry, event)
        handler.postAtFrontOfQueue(lastDispatchRunnable!!)
    }

    internal class DispatchRunnable(
        private val mRegistry: LifecycleRegistry,
        val mEvent: Lifecycle.Event
    ) :
        Runnable {
        private var mWasExecuted = false
        override fun run() {
            if (!mWasExecuted) {
                doWithoutCatch(ignoreError = true) {
                    mRegistry.handleLifecycleEvent(mEvent)
                }
                mWasExecuted = true
            }
        }
    }
}