package com.czur.starry.device.hdmilib

import android.app.Activity
import android.content.Context
import android.hardware.camera2.CameraManager
import android.os.RemoteException
import android.util.Size
import androidx.fragment.app.Fragment
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.lifecycleScope
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.common.KEY_HDMI_AUTO_OPEN
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.baselib.utils.prop.setBooleanSystemProp
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.hdmilib.Camera1Util.Companion.previewSize
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import okio.withLock
import rockchip.hardware.hdmi.V1_0.IHdmi
import rockchip.hardware.hdmi.V1_0.IHdmiCallback
import java.util.concurrent.locks.ReentrantLock

/**
 * Created by 陈丰尧 on 2021/8/19
 */
class HDMIUtil(
    private val cameraManager: CameraManager,
    private val lifecycleOwner: LifecycleOwner,
    private val cameraView: CameraView,
) : DefaultLifecycleObserver {
    companion object {
        private const val TAG = "HDMI-HDMIUtil"

        private var service: IHdmi? = null
        private val lock = ReentrantLock()
        /**
         * 修改自动打开的配置
         */
        fun enableAutoOpen(autoOpen: Boolean) {
            logTagV(TAG, "HDMI 自动打开:${autoOpen}")
            setBooleanSystemProp(KEY_HDMI_AUTO_OPEN, autoOpen)
        }

        /**
         * 获取是否自动打开
         */
        fun getAutoOpen(): Boolean {
            return getBooleanSystemProp(KEY_HDMI_AUTO_OPEN, false)
        }
    }


    private val context: Context by lazy { mkContext() }
    private val cameraLifecycleOwner = CameraLifecycleOwner(lifecycleOwner)

    var onHdmiChangeListener: ((HDMIIFStatus) -> Unit)? = null

    private fun mkContext(): Context {
        return when (lifecycleOwner) {
            is Context -> lifecycleOwner
            is Fragment -> lifecycleOwner.requireContext()
            else -> {
                logTagD(
                    TAG,
                    "lifecycleOwner 既不是Context 也不是 Fragment:${lifecycleOwner::javaClass.name}"
                )
                CZURAtyManager.appContext
            }
        }
    }


    /**
     * HDMI的状态
     */
    enum class HDMIStatus {
        PREPARE,    // 准备中
        PREVIEW,    // 预览中
        IDLE,       // 空闲中
        RESET       // 重置
    }



    private val _hdmiStatus =
        MutableStateFlow(HDMIStatus.PREPARE) // stateFlow会自动去重

    val hdmiStatusLive: LiveData<HDMIStatus> = _hdmiStatus.asLiveData()

    // HDMI 线材状态
    var hdmiIFStatus = HDMIIFStatus.OUT
        set(value) {
            if (field != value) {
                field = value
                onHDMIIFStateChange()
            }
        }

    private val hdmiCallback by lazy {
        HdmiCallback(_hdmiStatus) {
            // 回调给调用方
            hdmiIFStatus = it
        }
    }

    private val cameraUtil by lazy {
        Camera1Util(
            cameraView,
            lifecycleOwner.lifecycleScope,
            ::onOpenCameraFail
        )
    }


    /**
     * 开始播放HDMI
     */
    fun start() {
        // 注册生命周期回调
        lifecycleOwner.lifecycle.addObserver(this)
        // 1. 注册HDMI广播
        registerHDMIReceiver()
        // 2. 初始化camera
        cameraUtil.initCamera()

        // 先认为HDMI是IN的状态
        hdmiIFStatus = HDMIIFStatus.IN
    }


    /**
     * 当HDMI插入状态改变
     */
    private fun onHDMIIFStateChange() {
        logTagD(TAG, "HDMI插入状态改变:${hdmiIFStatus}")
        onHdmiChangeListener?.invoke(hdmiIFStatus)
        if (hdmiIFStatus == HDMIIFStatus.IN) {
            rePreview()
        } else if (hdmiIFStatus == HDMIIFStatus.OUT) {
            releaseCamera()
        }
    }

    /**
     * 开始监控分辨率的改变
     */
    private fun startHDMIScan() {
        logTagD(TAG, "开始打开camera")
        lifecycleOwner.lifecycleScope.launch {
            cameraUtil.startBackgroundThread()
            val result = cameraUtil.openCamera(service!!, cameraManager)
            _hdmiStatus.value = result
            when (result) {
                HDMIStatus.PREVIEW -> {
                    logTagD(TAG, "========打开camera成功")
                }

                HDMIStatus.IDLE -> {
                    logTagD(TAG, "====打开camera失败")
                    _hdmiStatus.value = HDMIStatus.IDLE
                    cameraUtil.stopBackgroundThread()
                }

                HDMIStatus.PREPARE -> {
                    logTagD(TAG, "====分辨率为0，等待预览")
                    delay(10000)
                    if (_hdmiStatus.value == HDMIStatus.PREPARE) {
                        _hdmiStatus.value = HDMIStatus.IDLE
                    }
                }

                else -> {}
            }
        }
    }

    /**
     * 释放Camera资源
     */
    private fun releaseCamera() {
        logTagD(TAG, "释放Camera资源")
        cameraUtil.releaseCamera()
        cameraUtil.stopBackgroundThread()
    }



    /**
     * 注册HDMI广播
     */
    private fun registerHDMIReceiver() {
        logTagV(TAG, "注册回调:")
        try {
            service = IHdmi.getService(true)
            service?.registerListener(context.packageName, hdmiCallback as IHdmiCallback?)
        } catch (e: RemoteException) {
            logTagD(TAG, "=======注册回调失败")
            e.printStackTrace()
        }
    }

    override fun onPause(owner: LifecycleOwner) {
        super.onPause(owner)
        //停止播放声音
        doWithoutCatch(tag = TAG) {
            logTagV(TAG, "关闭hdmi声音")
            cameraUtil.enableAudio(false)
            logTagV(TAG, "关闭hdmi声音 完成")
        }
    }

    override fun onResume(owner: LifecycleOwner) {
        super.onResume(owner)
        //恢复播放声音
        doWithoutCatch(tag = TAG) {
            logTagV(TAG, "播放hdmi声音=${_hdmiStatus.value}")
            if (_hdmiStatus.value == HDMIStatus.PREVIEW) {
                cameraUtil.enableAudio()
            }
            logTagV(TAG, "播放hdmi声音 完成")
        }
    }

    override fun onStop(owner: LifecycleOwner) {
        super.onStop(owner)

        // 这里没有选择onDestroy, 因为onDestroy可能会被其他页面影响, 导致迟迟不能被调用
        // HDMI功能禁止在后台运行, 所以不可见时, 就已经要执行销毁逻辑了
        // 取消注册广播
        doWithoutCatch(tag = TAG) {
            logTagV(TAG, "取消注册HDMI回调")
            service?.unregisterListener(context.packageName, hdmiCallback as IHdmiCallback?)
            logTagV(TAG, "取消注册HDMI回调 完成")
        }
        doWithoutCatch(tag = TAG) {
            logTagV(TAG, "停止预览")
            cameraUtil.releaseCamera()
            logTagV(TAG, "releaseCamera 完成")
        }
        lifecycleOwner.lifecycleScope.cancel()
        cameraLifecycleOwner.onDestroy()
    }

    private fun onOpenCameraFail(exp: Exception) {
        logTagE(TAG, "打开相机失败", tr = exp)
        _hdmiStatus.value = HDMIStatus.IDLE
        if (lifecycleOwner is Activity) {
            lifecycleOwner.lifecycleScope.launch(Dispatchers.Main) {
                lifecycleOwner.toast(R.string.toast_hdmi_open_fail)
                lifecycleOwner.finish()
            }
        }
    }


    /**
     * 分辨率切换，重新预览
     */
    fun rePreview() {
        lock.withLock {
            releaseCamera()
            startHDMIScan()
        }
    }
}

/**
 * 监听HDMI插拔
 */
class HdmiCallback(
    private val statusFlow: MutableStateFlow<HDMIUtil.HDMIStatus>,
    private val hdmiIfStatusListener: (HDMIIFStatus) -> Unit
) : IHdmiCallback.Stub() {
    companion object {
        private const val TAG = "HDMI-HdmiCallback"
    }

    override fun onConnect(p0: String?) {
        logTagD(TAG, "onConnect:${p0} ")
        hdmiIfStatusListener(HDMIIFStatus.WAIT)
        hdmiIfStatusListener(HDMIIFStatus.IN)
    }

    override fun onFormatChange(p0: String?, width: Int, height: Int) {
        logTagD(TAG, "onFormatChange:${p0} $width $height")
        if (p0.isNullOrEmpty()) {
            statusFlow.value = HDMIUtil.HDMIStatus.PREPARE
        } else if (Size(width, height) != previewSize) {
            logTagD(TAG, "=====分辨率切换with=$width height=$height")
            previewSize = Size(width, height)
            statusFlow.value = HDMIUtil.HDMIStatus.RESET
        }
    }

    override fun onDisconnect(p0: String?) {
        hdmiIfStatusListener(HDMIIFStatus.OUT)
    }

}
