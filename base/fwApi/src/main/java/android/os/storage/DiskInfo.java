package android.os.storage;

import android.os.Parcel;
import android.os.Parcelable;
import android.util.IndentingPrintWriter;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Information about a physical disk which may contain one or more
 * {@link VolumeInfo}.
 *
 * @hide
 */
public class DiskInfo implements Parcelable {
    public static String ACTION_DISK_SCANNED =
            "android.os.storage.action.DISK_SCANNED";
    public static String EXTRA_DISK_ID =
            "android.os.storage.extra.DISK_ID";
    public static String EXTRA_VOLUME_COUNT =
            "android.os.storage.extra.VOLUME_COUNT";

    public static int FLAG_ADOPTABLE = 1 << 0;
    public static int FLAG_DEFAULT_PRIMARY = 1 << 1;
    public static int FLAG_SD = 1 << 2;
    public static int FLAG_USB = 1 << 3;
    /**
     * The FLAG_STUB_VISIBLE is set from vold, which gets the flag from outside (e.g., ChromeOS)
     */
    public static int FLAG_STUB_VISIBLE = 1 << 6;

    public String id;

    public int flags;

    public long size;

    public String label;
    /**
     * Hacky; don't rely on this count
     */
    public int volumeCount;
    public String sysPath;

    public DiskInfo(String id, int flags) {
        
    }

    
    public DiskInfo(Parcel parcel) {
    }


    public static final Creator<DiskInfo> CREATOR = new Creator<DiskInfo>() {
        @Override
        public DiskInfo createFromParcel(Parcel in) {
            return new DiskInfo(in);
        }

        @Override
        public DiskInfo[] newArray(int size) {
            return new DiskInfo[size];
        }
    };

    public @NonNull String getId() {
        throw new RuntimeException("Stub!");
    }

    public @Nullable String getDescription() {
        throw new RuntimeException("Stub!");
    }

    public @Nullable String getShortDescription() {
        throw new RuntimeException("Stub!");
    }


    public boolean isAdoptable() {
        throw new RuntimeException("Stub!");
    }


    public boolean isDefaultPrimary() {
        throw new RuntimeException("Stub!");
    }


    public boolean isSd() {
        throw new RuntimeException("Stub!");
    }


    public boolean isUsb() {
        throw new RuntimeException("Stub!");
    }

    public boolean isStubVisible() {
        throw new RuntimeException("Stub!");
    }

    public void dump(IndentingPrintWriter pw) {

    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel parcel, int flags) {
    }
}
