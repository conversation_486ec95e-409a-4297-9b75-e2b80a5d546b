package android.os;

import android.graphics.Rect;

/**
 * Created by 陈丰尧 on 2024/12/2
 */
public class RkDisplayOutputManager {
    private static final String TAG = "RkDisplayOutputManager";
    private final boolean DBG = true;
    public final int MAIN_DISPLAY = 0;
    public final int AUX_DISPLAY = 1;

    public final int DRM_MODE_CONNECTOR_Unknown = 0;
    public final int DRM_MODE_CONNECTOR_VGA = 1;
    public final int DRM_MODE_CONNECTOR_DVII = 2;
    public final int DRM_MODE_CONNECTOR_DVID = 3;
    public final int DRM_MODE_CONNECTOR_DVIA = 4;
    public final int DRM_MODE_CONNECTOR_Composite = 5;
    public final int DRM_MODE_CONNECTOR_SVIDEO = 6;
    public final int DRM_MODE_CONNECTOR_LVDS = 7;
    public final int DRM_MODE_CONNECTOR_Component = 8;
    public final int DRM_MODE_CONNECTOR_9PinDIN = 9;
    public final int DRM_MODE_CONNECTOR_DisplayPort = 10;
    public final int DRM_MODE_CONNECTOR_HDMIA = 11;
    public final int DRM_MODE_CONNECTOR_HDMIB = 12;
    public final int DRM_MODE_CONNECTOR_TV = 13;
    public final int DRM_MODE_CONNECTOR_eDP = 14;
    public final int DRM_MODE_CONNECTOR_VIRTUAL = 15;
    public final int DRM_MODE_CONNECTOR_DSI = 16;

    private final String DISPLAY_TYPE_UNKNOW = "UNKNOW";
    private final String DISPLAY_TYPE_VGA = "VGA";
    private final String DISPLAY_TYPE_DVII = "DVII";
    private final String DISPLAY_TYPE_DVID = "DVID";
    private final String DISPLAY_TYPE_DVIA = "DVIA";
    private final String DISPLAY_TYPE_Composite = "Composite";
    private final String DISPLAY_TYPE_LVDS = "LVDS";
    private final String DISPLAY_TYPE_Component = "Component";
    private final String DISPLAY_TYPE_9PinDIN = "9PinDIN";
    //private final String DISPLAY_TYPE_YPbPr = "YPbPr";
    private final String DISPLAY_TYPE_DP = "DP";
    private final String DISPLAY_TYPE_HDMIA = "HDMIA";
    private final String DISPLAY_TYPE_HDMIB = "HDMIB";
    private final String DISPLAY_TYPE_TV = "TV";
    private final String DISPLAY_TYPE_EDP = "EDP";
    private final String DISPLAY_TYPE_VIRTUAL = "VIRTUAL";
    private final String DISPLAY_TYPE_DSI = "DSI";
    public final String COLOR_FORMAT_RGB = "RGB";

    public final String DISPLAY_TYPE_HDMI = "HDMI";
    public final int DISPLAY_OVERSCAN_X = 0;
    public final int DISPLAY_OVERSCAN_Y = 1;
    public final int DISPLAY_OVERSCAN_LEFT = 2;
    public final int DISPLAY_OVERSCAN_RIGHT = 3;
    public final int DISPLAY_OVERSCAN_TOP = 4;
    public final int DISPLAY_OVERSCAN_BOTTOM = 5;
    public final int DISPLAY_OVERSCAN_ALL = 6;

    public final int DISPLAY_3D_NONE = -1;
    public final int DISPLAY_3D_FRAME_PACKING = 0;
    public final int DISPLAY_3D_TOP_BOTTOM = 6;
    public final int DISPLAY_3D_SIDE_BY_SIDE_HALT = 8;

    public final int DRM_MODE_CONNECTED = 1;
    public final int DRM_MODE_DISCONNECTED = 2;
    public final int DRM_MODE_UNKNOWNCONNECTION = 3;

    public final int HDR_CLOSE = 0;
    public final int HDR_OPEN = 1;
    public final int HDR_AUTO = 2;

    public final int HDR10 = 1;
    public final int DOLBY_VISION = 2;

    private int m_main_iface[] = null;
    private int m_aux_iface[] = null;


    public RkDisplayOutputManager() {
    }


    public String typetoface(int type) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public int getCurrentDpyConnState(int display) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public int getDisplayNumber() {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public int[] getIfaceList(int display) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public int getCurrentInterface(int display) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public String[] getModeList(int display, int type) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public String getCurrentMode(int display, int type) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public String getCurrentColorMode(int display, int type) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public void setColorMode(int display, int type, String format) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public String[] getSupportCorlorList(int display, int type) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public void setHdrMode(int display, int type, int hdrMode) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public void setMode(int display, int type, String mode) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public void setOverScan(int display, int direction, int value) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public Rect getOverScan(int display) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public void setDisplaySize(int display, int width, int height) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public int get3DModes(int display, int type) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public int getCur3DMode(int display, int type) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public void set3DMode(int display, int type, int mode) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public int saveConfig() {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public int updateDisplayInfos() {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public int setBrightness(int display, int bright_percent) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public int setContrast(int display, int con_percent) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }


    public int setSaturation(int display, int sat_percent) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }


    public int setHue(int display, int degree_percent) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }


    public int getBrightness(int display) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }


    public int getContrast(int display) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }


    public int getSaturation(int display) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }


    public int getHue(int display) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public int[] getBcsh(int display) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public int setGamma(int display, int size, int[] red, int[] green, int[] blue) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public int set3DLut(int display, int size, int[] red, int[] green, int[] blue) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public String[] getConnectorInfo() {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public int updateDispHeader() {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public int getResolutionSupported(int display, String resolution) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public boolean isDolbyVisionStatus() {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public boolean setDolbyVisionEnabled(boolean enable) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public boolean isHDR10Status() {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public boolean setHDR10Enabled(boolean enable) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public boolean isAiImageQuality() {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public boolean setAiImageQuality(boolean enabled) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public boolean isAiImageQualityLabMode() {
        throw new UnsupportedOperationException("Not implemented by mock");
    }

    public boolean setAiImageQualityLabMode(boolean enable) {
        throw new UnsupportedOperationException("Not implemented by mock");
    }
}
