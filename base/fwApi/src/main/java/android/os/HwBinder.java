package android.os;

import java.util.NoSuchElementException;

/**
 * Created by 陈丰尧 on 2024/12/2
 */
public abstract class HwBinder implements IHwBinder {
    /**
     * Create and initialize a HwBinder object and the native objects
     * used to allow this to participate in hwbinder transactions.
     */
    public HwBinder() {
    }

    @Override
    public final native void transact(
            int code, HwParcel request, HwParcel reply, int flags)
            throws RemoteException;

    /**
     * Process a hwbinder transaction.
     *
     * @param code interface specific code for interface.
     * @param request parceled transaction
     * @param reply object to parcel reply into
     * @param flags transaction flags to be chosen by wire protocol
     */
    public abstract void onTransact(
            int code, HwParcel request, HwParcel reply, int flags)
            throws RemoteException;


    /**
     * Returns the specified service from the hwservicemanager. Does not retry.
     *
     * @param iface fully-qualified interface name for example foo.bar@1.3::IBaz
     * @param serviceName the instance name of the service for example default.
     * @throws NoSuchElementException when the service is unavailable
     */
    public static final IHwBinder getService(
            String iface,
            String serviceName)
            throws RemoteException, NoSuchElementException {
        throw new UnsupportedOperationException("getService() not implemented");
    }


    /**
     * Enable instrumentation if available.
     * On a non-user build, this method:
     * - tries to enable atracing (if enabled)
     * - tries to enable coverage dumps (if running in VTS)
     * - tries to enable record and replay (if running in VTS)
     */
    public static void enableInstrumentation() {

    }

    /**
     * Notifies listeners that a system property has changed
     */
    public static void reportSyspropChanged() {
    }
}
