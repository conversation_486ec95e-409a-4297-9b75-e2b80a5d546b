package android.os;

/**
 * Created by 陈丰尧 on 2024/12/2
 */
public interface IHwBinder {
    /**
     * Process a hwbinder transaction.
     *
     * @param code interface specific code for interface.
     * @param request parceled transaction
     * @param reply object to parcel reply into
     * @param flags transaction flags to be chosen by wire protocol
     */
    public void transact(
            int code, HwParcel request, HwParcel reply, int flags)
            throws RemoteException;

    /**
     * Return as IHwInterface instance only if this implements descriptor.
     *
     * @param descriptor for example foo.bar@1.0::IBaz
     */
    public IHwInterface queryLocalInterface(String descriptor);

    /**
     * Interface for receiving a callback when the process hosting a service
     * has gone away.
     */
    public interface DeathRecipient {
        /**
         * Callback for a registered process dying.
         *
         * @param cookie cookie this death recipient was registered with.
         */
        public void serviceDied(long cookie);
    }

    /**
     * Notifies the death recipient with the cookie when the process containing
     * this binder dies.
     *
     * @param recipient callback object to be called on object death.
     * @param cookie value to be given to callback on object death.
     */
    public boolean linkToDeath(DeathRecipient recipient, long cookie);
    /**
     * Unregisters the death recipient from this binder.
     *
     * @param recipient callback to no longer recieve death notifications on this binder.
     */
    public boolean unlinkToDeath(DeathRecipient recipient);
}