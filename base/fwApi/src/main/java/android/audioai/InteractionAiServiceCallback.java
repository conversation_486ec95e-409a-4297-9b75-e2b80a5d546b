package android.audioai;

/**
 * Created by 陈丰尧 on 2025/1/18
 */
public interface InteractionAiServiceCallback {

    void onWakeUp(String meta);

    void  onStreamStoped (String meta);

    void onAudioMeta(String metaJson);

    void onInstruct(String instructJson);

    void onChatMsg(String msgJson);

    void onTtS(byte[] audioData,  String meta);

    void onError(int errorCode, String errorMsg);

    void onMicAmp(String json);

    void onFirstStageWakeUp(String meta);

}
