package android.app;

import android.os.Build;
import android.os.IBinder;
import android.os.RemoteException;
import android.os.ServiceManager;
import android.util.Singleton;

import java.util.List;

/**
 * author : WangHao
 * time   :2025/01/04
 */


public class ActivityTaskManager {
    private static final Singleton<ActivityTaskManager> sInstance =
            new Singleton<ActivityTaskManager>() {
                @Override
                protected ActivityTaskManager create() {
                    return new ActivityTaskManager();
                }
            };

    public static ActivityTaskManager getInstance() {
        return sInstance.get();
    }

    public static IActivityTaskManager getService() {
        return IActivityTaskManagerSingleton.get();
    }

    private static final Singleton<IActivityTaskManager> IActivityTaskManagerSingleton =
            new Singleton<IActivityTaskManager>() {
                @Override
                protected IActivityTaskManager create() {
                    final IBinder b = ServiceManager.getService("activity_task");
                    return IActivityTaskManager.Stub.asInterface(b);
                }
            };

    /**
     * @return List of recent tasks.
     * @hide
     */
    public List<ActivityManager.RecentTaskInfo> getRecentTasks(
            int maxNum, int flags, int userId) {
        try {
            return getService().getRecentTasks(maxNum, flags, userId).getList();
        } catch (RemoteException e) {
            throw e.rethrowFromSystemServer();
        }
    }


    /**
     * @return List of running tasks that can be filtered by visibility in recents.
     * @hide
     */
    public List<ActivityManager.RunningTaskInfo> getTasks(
            int maxNum, boolean filterOnlyVisibleRecents) {
        if (Build.VERSION.SDK_INT >= 33) {
            return getTasks(maxNum, filterOnlyVisibleRecents, false /* keepIntentExtra */,
                    -1);
        }else {
            return getTasks(maxNum, filterOnlyVisibleRecents, false /* keepIntentExtra */);
        }

    }


    /**
     * @return List of running tasks that can be filtered by visibility and displayId in recents
     * and keep intent extra.
     * @param displayId the target display id, or {@link INVALID_DISPLAY} not to filter by displayId
     * @hide
     */
    public List<ActivityManager.RunningTaskInfo> getTasks(
            int maxNum, boolean filterOnlyVisibleRecents, boolean keepIntentExtra, int displayId) {
        try {
            return getService().getTasks(maxNum, filterOnlyVisibleRecents, keepIntentExtra,
                    displayId);
        } catch (RemoteException e) {
            throw e.rethrowFromSystemServer();
        }
    }

    /**
     * @return List of running tasks that can be filtered by visibility in recents and keep intent
     * extra.
     * @hide
     */
    public List<ActivityManager.RunningTaskInfo> getTasks(
            int maxNum, boolean filterOnlyVisibleRecents, boolean keepIntentExtra) {
        try {
            return getService().getTasks(maxNum, filterOnlyVisibleRecents, keepIntentExtra);
        } catch (RemoteException e) {
            throw e.rethrowFromSystemServer();
        }
    }

    public boolean removeTask(int taskId) {
        try {
            return getService().removeTask(taskId);
        } catch (RemoteException e) {
            throw e.rethrowFromSystemServer();
        }
    }

    public void removeAllVisibleRecentTasks() {
        try {
            getService().removeAllVisibleRecentTasks();
        } catch (RemoteException e) {
            throw e.rethrowFromSystemServer();
        }
    }
}
