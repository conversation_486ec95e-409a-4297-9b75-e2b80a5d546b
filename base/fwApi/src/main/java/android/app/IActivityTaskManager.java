package android.app;

import android.content.pm.ParceledListSlice;
import android.os.Binder;
import android.os.Bundle;
import android.os.IBinder;
import android.os.IInterface;
import android.os.RemoteException;
import android.window.TaskSnapshot;

import java.util.List;

/**
 * author : <PERSON><PERSON><PERSON>
 * time   :2025/01/04
 */


public interface IActivityTaskManager extends IInterface {
    public abstract static class Stub extends Binder implements IActivityTaskManager {
        public static IActivityTaskManager asInterface(IBinder obj) {
            return null;
        }
    }
    ParceledListSlice<ActivityManager.RecentTaskInfo> getRecentTasks(int var1, int var2, int var3) throws RemoteException;

    List<ActivityManager.RunningTaskInfo> getTasks(int maxNum, boolean filterOnlyVisibleRecents,
                                                   boolean keepIntentExtra) throws RemoteException;
    List<ActivityManager.RunningTaskInfo> getTasks(int maxNum, boolean filterOnlyVisibleRecents,
                                                   boolean keepIntentExtra, int displayId)throws RemoteException;
    int startActivityFromRecents(int var1, Bundle var2) throws RemoteException;

    boolean removeTask(int var1) throws RemoteException;

    void removeAllVisibleRecentTasks() throws RemoteException;

    TaskSnapshot getTaskSnapshot(int var1, boolean isLowResolution, boolean takeSnapshotIfNeeded) throws RemoteException;

    TaskSnapshot getTaskSnapshot(int var1, boolean isLowResolution) throws RemoteException;


}
