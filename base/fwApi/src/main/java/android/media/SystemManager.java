package android.media;

import android.content.Context;

/**
 * Created by 陈丰尧 on 2024/5/14
 */
public class SystemManager {

    public SystemManager(Context context) {
    }

    public static final int LOOKS_MODE_6500K = 1;
    public static final int LOOKS_MODE_8880K = 0;
    public static final int LOOKS_MODE_NOT_SUPPORTED = -1;

    public static final int[] BCSH_8880K = {0, 0, 0, 0};//zhg##,Fix the issue of gray UI not displaying: 49,54,59,44-->53,50,59,44,
    public static final int[] BCSH_6500K = {0, 0, 0, 0};


    public interface OnSystemChangeListener {
        /**
         * Called on the listener to notify it that system has been changed.
         *
         * @param category system state changed category.
         * @param eventID  customized event ID.
         * @param para1    for extend.
         * @param extend1  for extend string 1.
         * @param extend2  for extend string 2.
         */
        public void onSystemChange(int category, int eventID, int para1, String extend1, String extend2);
    }

    /**
     * Get ethernet mac address.
     */
    public String getEthernetMac() {
        return "";
    }

    /**
     * set cpu governor to usersapce and max freq.
     *
     * @return 0, success, other, failed..
     */
    public int perfCpuGov() {
        return 0;
    }

    /**
     * restore cpu governor to interactive.
     *
     * @return 0, success, other, failed..
     */
    public int restoreCpuGov() {
        return 0;
    }

    /**
     * Set CZCV&CZSL track mode.
     * value:
     * 0, vision track mode.
     * 1, vision and doa track mode.
     * 2, no any track conduction.
     * -1, you want to get the saved track mode. return value is.
     */
    public int setTrackMode(int mode) {
        return 0;
    }

    /**
     * Enable/Disable audio and camera data collection.
     * audio:
     * true, enable audio collection.
     * false, disable audio collection.
     * camera:
     * true, enable camera collection.
     * false, disable camera collection.
     */
    public int enableAlgoCollection(boolean audio, boolean camera) {
        return 0;
    }

    /**
     * Get system performance constraint.
     *
     * @return onOff 0, off; 1, resume.
     */
    public int getPerfConstraint() {
        return 0;
    }

    /**
     * get DLP3439 looks mode.
     * LOOKS_MODE_NOT_SUPPORTED or LOOKS_MODE_6500K or LOOKS_MODE_8880K.
     */
    public int getDlp3439LooksMode() {
        return 0;
    }

    /**
     * Save DLP3439 looks value.
     * Old HW, compitable only.
     */
    public int saveDlp3439Looks(int value) {
        return 0;
    }

    /**
     * Save DLP3439 looks value.
     * value is -1, you want to set the looks mode only.
     * value is bigger than or equal to 1000, you want to set the looks mode and make it worked.
     * mode range is LOOKS_MODE_6500K, LOOKS_MODE_NOT_SUPPORTED or LOOKS_MODE_8880K
     * any other value will be treat as LOOKS_MODE_8880K.
     */
    public int saveDlp3439Looks(int value, int mode) {
        return 0;
    }

    // 提供一个方法让 app 注册回调

    /**
     * 添加回调事件
     */
    public void addListener(OnSystemChangeListener listener) {
    }

    /**
     * Optical machine mvoe forward, async API.
     *
     * @param direction move direction, 1 forward, 0 backward.
     * @param step      steps will move.
     * @return always 0, if service is alive.
     */
    public int opticalMachMove(int direction, int step) {
        return 0;
    }

    /**
     * Optical machine auto focus, async API.
     *
     * @return always 0, if service is alive.
     */
    public int opticalMachAutoFocus() {
        return 0;
    }

    public int opticalMachResetAutoFocus() {
        return 0;
    }

    /**
     * Control usb mode.
     *
     * @param mode: get is gadget or not. return zero is not, not zero is.
     *              public static final int USB_MODE_IS_GADGET = -1;
     *              just reserved.
     *              public static final int USB_MODE_OTG = 0;
     *              peripheral and start adb.
     *              public static final int USB_MODE_ENABLE_ADB = 1;
     *              host and stop adb.
     *              public static final int USB_MODE_HOST = 2;
     *              adb is determined by debuggable. enbale gadget not stored.
     *              public static final int USB_MODE_EN_GADGET = 3;
     *              adb is determined by debuggable. enable gadget and stored.
     *              public static final int USB_MODE_EN_GADGET_STORE = 4;
     *              fallback to host or adb determined by debuggable.
     *              public static final int USB_MODE_DIS_GADGET = 5;
     */
    public int controlUsbMode(int mode) {
        return 0;   // 24.05.21 这个接口希望是同步的, 已经告知宝哥了, 他会修改
    }

    /**
     * Update Audio Policy when ent BYOM.
     *
     * @param enterByom true, enter byom; false, exit byom.
     */
    public int updateAudioPolicyByByom(boolean enterByom) {
        return 0;
    }

    public boolean getHdmiOutState() {
        return false;
    }


    // CEC控制
    // CEC 外部设备 关闭 Studio 状态
    public boolean isHdmiCecInShutDownState() {
        throw new RuntimeException("Stub!");
    }

    // CEC 外部设备 启动 Studio 状态
    public boolean isHdmiCecInStartState() {
        throw new RuntimeException("Stub!");
    }

    // CEC Studio 关闭 外部设备 状态
    public boolean isHdmiCecOutShutDownState() {
        throw new RuntimeException("Stub!");
    }

    // CEC Studio 启动 外部设备 状态
    public boolean isHdmiCecOutStartState() {
        throw new RuntimeException("Stub!");
    }

    // 设置 CEC 外部设备 关闭 Studio 状态
    public int setHdmiCecInShutDownState(boolean state) {
        throw new RuntimeException("Stub!");
    }

    // 设置 CEC 外部设备 启动 Studio 状态
    public int setHdmiCecInStartState(boolean state) {
        throw new RuntimeException("Stub!");
    }

    // 设置 CEC Studio 关闭 外部设备 状态
    public int setHdmiCecOutShutDownState(boolean state) {
        throw new RuntimeException("Stub!");
    }

    // 设置 CEC Studio 启动 外部设备 状态
    public int setHdmiCecOutStartState(boolean state) {
        throw new RuntimeException("Stub!");
    }

    /**
     * 设置音频输入输出设备.
     *
     * @param inout 1 in, 0 out.
     * @param type  AudioDeviceInfo type.
     * @param index AudioDeviceInfo index.
     * @return 0 success, otherwise failed, keep selected device.
     */
    public int setPreferredAudioDevice(int inout, int type, int index) {
        throw new RuntimeException("Stub!");
    }

}
