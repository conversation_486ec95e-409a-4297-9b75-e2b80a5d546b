package android.provider;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executor;

/**
 * Created by 陈丰尧 on 2024/5/22
 */
public final class DeviceConfig {
    public static final String NAMESPACE_PRIVACY = "privacy";

    public static boolean getBoolean(String namespace, String name,
                                     boolean defaultValue) {
        return false;
    }

    public static void addOnPropertiesChangedListener(
            String namespace,
            Executor executor,
            OnPropertiesChangedListener onPropertiesChangedListener) {
    }

    public interface OnPropertiesChangedListener {
        /**
         * Called when one or more properties have changed, providing a Properties object with all
         * of the changed properties. This object will contain only properties which have changed,
         * not the complete set of all properties belonging to the namespace.
         *
         * @param properties Contains the complete collection of properties which have changed for a
         *                   single namespace. This includes only those which were added, updated,
         *                   or deleted.
         */
        void onPropertiesChanged(Properties properties);
    }

    public static class Properties {
        private final String mNamespace = "";
        private final HashMap<String, String> mMap = new HashMap();
        private Set<String> mKeyset;

        /**
         * Create a mapping of properties to values and the namespace they belong to.
         *
         * @param namespace   The namespace these properties belong to.
         * @param keyValueMap A map between property names and property values.
         * @hide
         */
        public Properties(String namespace, Map<String, String> keyValueMap) {
        }

        /**
         * @return the namespace all properties within this instance belong to.
         */

        public String getNamespace() {
            return mNamespace;
        }

        /**
         * @return the non-null set of property names.
         */

        public Set<String> getKeyset() {
            return mKeyset;
        }

        /**
         * Look up the String value of a property.
         *
         * @param name         The name of the property to look up.
         * @param defaultValue The value to return if the property has not been defined.
         * @return the corresponding value, or defaultValue if none exists.
         */

        public String getString(String name, String defaultValue) {
            return "";
        }

        /**
         * Look up the boolean value of a property.
         *
         * @param name         The name of the property to look up.
         * @param defaultValue The value to return if the property has not been defined.
         * @return the corresponding value, or defaultValue if none exists.
         */
        public boolean getBoolean(String name, boolean defaultValue) {

            String value = mMap.get(name);
            return value != null ? Boolean.parseBoolean(value) : defaultValue;
        }

        /**
         * Look up the int value of a property.
         *
         * @param name         The name of the property to look up.
         * @param defaultValue The value to return if the property has not been defined or fails to
         *                     parse into an int.
         * @return the corresponding value, or defaultValue if no valid int is available.
         */
        public int getInt(String name, int defaultValue) {
            return 0;
        }

        /**
         * Look up the long value of a property.
         *
         * @param name         The name of the property to look up.
         * @param defaultValue The value to return if the property has not been defined. or fails to
         *                     parse into a long.
         * @return the corresponding value, or defaultValue if no valid long is available.
         */
        public long getLong(String name, long defaultValue) {
            return 0;
        }

        /**
         * Look up the int value of a property.
         *
         * @param name         The name of the property to look up.
         * @param defaultValue The value to return if the property has not been defined. or fails to
         *                     parse into a float.
         * @return the corresponding value, or defaultValue if no valid float is available.
         */
        public float getFloat(String name, float defaultValue) {
            return 0;
        }
    }
}
