package android.content.pm;

import android.annotation.SuppressLint;
import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.NonNull;

/**
 * author : <PERSON><PERSON><PERSON>
 * time   :2025/01/04
 */


@SuppressLint("ParcelCreator")
public class UserInfo implements Parcelable {
    public int id = -1;

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(@NonNull Parcel parcel, int i) {

    }
}
