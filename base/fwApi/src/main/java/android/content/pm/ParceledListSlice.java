package android.content.pm;

import android.annotation.SuppressLint;
import android.os.Parcel;
import android.os.Parcelable;

/**
 * author : <PERSON><PERSON><PERSON>
 * time   :2025/01/04
 */


@SuppressLint("ParcelCreator")
public class ParceledListSlice<T extends Parcelable> extends BaseParceledListSlice<T> {
    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel parcel, int i) {

    }
}
