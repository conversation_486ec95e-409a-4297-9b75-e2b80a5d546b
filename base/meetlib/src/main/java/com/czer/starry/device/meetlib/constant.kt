package com.czer.starry.device.meetlib

/**
 * Created by 陈丰尧 on 2021/5/27
 */
/**
 * 会议CMD
 * @param isTargetCMD: 这个命令是否是有指向的
 */
enum class MeetingCMD(val cmd: String, val isTargetCMD: Boolean = false) {
    REJECT("reject"),       // 拒绝加入会议
    JOIN("join"),           // 加入会议室
    EXIT("exit"),           // 自己退出
    STOP("stop"),           // 结束会议
    STOP_FORCE("stop_force"),   // 服务器强制结束会议
    STEP_OUT("stepOut"),    // 暂时离开会议
    MUTE_AUDIO("muteAudio", true),// 关闭音频
    OPEN_AUDIO("openAudio", true),// 打开音频
    MUTE_VIDEO("muteVideo", true),// 关闭视频
    OPEN_VIDEO("openVideo", true),// 打开视频
    HOST("host", true),               // 设置成员为管理员
    UNHOST("unhost", true),
    SHARE("share"),             // 开始分享
    STOP_SHARE("stopShare"),    // 停止分享
    REMOVE("remove", true),           // 移除会议成员
    SYNC_SELF("audioVideoStatus"),   // 同步音视频状态

    START_RECORD("startRecord"),    // 开始会议录制
    STOP_RECORD("stopRecord"),      // 结束会议录制

    QUIT_SHARE("quitShare", true), // 停止分享

    OTHER_MEETING_JOIN("otherMeetingJoined", false), // 其他有没有退出的会议

    CHECK_MEETING("check_meeting");// 获取正在进行会议

    companion object {
        /**
         * 根据枚举的[MeetingCMD.cmd] 找到对应类型
         */
        fun cmdOf(cmd: String): MeetingCMD {
            return MeetingCMD.values().find {
                it.cmd.equals(cmd, true)
            } ?: throw IllegalArgumentException("cmd:${cmd} 无法找到对应的枚举类型")
        }
    }
}

const val DEVICES_MODULE = "Starry"

const val MEETING_KEY_CMD = "cmd"
const val MEETING_KEY_UDID_TO = "udid_to"
const val MEETING_KEY_UDID_FROM = "udid_from"
const val MEETING_KEY_SUBJECT = "room_name" // 会议主题
const val MEETING_KEY_ROOM = "room"
const val MEETING_STOP_REASON = "stopReason" // 会议停止原因
const val MEETING_KEY_MEETING_NO = "meeting_no"

const val MEETING_KEY_HEAD_IMG = "headImage" // 头像
const val MEETING_KEY_NICK_FROM = "nickname_from"
const val MEETING_KEY_USER_ID_FROM = "userid_from"


const val MEETING_KEY_USERS = "users"       // 用户列表 users的key
const val MEETING_KEY_USER = "user"         // 更新用户列表 user的KEY
const val MEETING_KEY_LOCKED = "locked"     // 用户列表 房间是否锁定的key
const val MEETING_KEY_IS_RECORD = "isRecoder"// 用户列表 会议是否正在录音
const val MEETING_KEY_SB_RECORDING = "someoneRecording" // 会议中有人录音
const val MEETING_RECORDING_LIST = "recordingList"      // 会议中录音人的列表
const val MEETING_KEY_MEETING_CODE = "meetingCode"      // 会议号
const val MEETING_KEY_MEETING_PWD = "meetingPassword"   // 会议密码
const val MEETING_KEY_MEETING_NAME = "meetingName"      // 会议主题
const val MEETING_KEY_META_DATA = "metadata" // 用户列表 额外属性(房间是否锁定)

const val MEETING_KEY_TARGET = "target" // 要控制的目标(打开/关闭音视频流时使用)


/**
 * 会议的状态
 */
enum class MeetingStatus(val statusCode: Int) {
    CALLING(1),        // 呼叫中
    STARTING(2),       // 正在进行中
    STOPPED(0);        // 没在开会

    companion object {
        fun createByStatusCode(statusCode: Int): MeetingStatus {
            return MeetingStatus.values().find {
                it.statusCode == statusCode
            } ?: STOPPED
        }
    }
}