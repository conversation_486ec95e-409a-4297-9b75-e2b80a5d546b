package com.czur.starry.device.sharescreen.esharelib.widget

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.lifecycle.lifecycleScope
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.utils.getString
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.requireLifecycleOwner
import com.czur.starry.device.baselib.utils.setDebounceTouchClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.utils.view.findView
import com.czur.starry.device.sharescreen.esharelib.R
import com.czur.starry.device.sharescreen.esharelib.entity.GuidVideoItem
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.launch
import androidx.core.view.isNotEmpty

/**
 * Created by 陈丰尧 on 2023/5/12
 */
class GuideImageView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : FrameLayout(context, attrs, defStyleAttr) {
    private val guideMaskIv: ImageView by findView(R.id.guideMaskIv)
    private val guideImageFl: FrameLayout by findView(R.id.guideImageFl)
    private val videoDurationLl: LinearLayout by findView(R.id.videoDurationLl)
    private val videoDurationTv: TextView by findView(R.id.videoDurationTv)
    private val videoPlayIv: ImageView by findView(R.id.videoPlayIv)
    private var guideVideoItem: GuidVideoItem? = null
    private var videoClickListener: ((GuidVideoItem?) -> Unit)? = null

    private val touchDownTimeFlow = MutableStateFlow(0L)
    private val hoverInFlow = MutableStateFlow(false)
    private val showMaskFlow = touchDownTimeFlow.combine(hoverInFlow) { touchTime, hoverIn ->
        (System.currentTimeMillis() - touchTime) < 100 || hoverIn
    }

    init {
        inflate(context, R.layout.view_guide_image, this)
        initListener()
    }

    fun setVideoDuration(guideVideoItem: GuidVideoItem?) {
        this.guideVideoItem = guideVideoItem
        if (guideVideoItem == null) {
            logTagD("setVideoDuration: guideVideoItem is null")
            videoDurationLl.gone()
        } else {
            // 只有设置了视频时长才显示
            videoDurationTv.text =
                getString(R.string.str_video_duration, guideVideoItem.durationInSec)
            videoDurationLl.show()
        }
    }

    fun requestHideMask() {
        guideMaskIv.gone()
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        when (ev?.action) {
            MotionEvent.ACTION_DOWN, MotionEvent.ACTION_MOVE -> {
                touchDownTimeFlow.value = System.currentTimeMillis()
            }
        }
        return super.dispatchTouchEvent(ev)
    }


    fun setOnVideoClickListener(listener: ((GuidVideoItem?) -> Unit)?) {
        videoClickListener = listener
    }

    override fun addView(child: View?, index: Int, params: ViewGroup.LayoutParams?) {
        if (isNotEmpty()) {
            guideImageFl.addView(child, guideImageFl.childCount - 1, params)
        } else {
            super.addView(child, index, params)
        }
    }

    private fun initListener() {
        setOnHoverListener { _, event ->
            hoverInFlow.value = event.action != MotionEvent.ACTION_HOVER_EXIT
            false
        }
        requireLifecycleOwner().lifecycleScope.launch {
            showMaskFlow.collect {
                guideMaskIv.gone(!it)
            }
        }

        setDebounceTouchClickListener {
            videoClickListener?.invoke(guideVideoItem)
        }
        guideMaskIv.setDebounceTouchClickListener {
            videoClickListener?.invoke(guideVideoItem)
        }
        videoPlayIv.setDebounceTouchClickListener {
            videoClickListener?.invoke(guideVideoItem)
        }
    }
}