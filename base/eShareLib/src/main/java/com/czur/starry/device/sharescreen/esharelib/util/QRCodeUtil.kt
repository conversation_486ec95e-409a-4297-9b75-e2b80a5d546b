package com.czur.starry.device.sharescreen.esharelib.util

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.sharescreen.esharelib.R

val logoBitmap: Bitmap by lazy {
    BitmapFactory.decodeResource(CZURAtyManager.appContext.resources, R.drawable.qr_logo)
}

val logoBitmapBK: Bitmap by lazy {
    BitmapFactory.decodeResource(CZURAtyManager.appContext.resources, R.drawable.qr_logo_black)
}

