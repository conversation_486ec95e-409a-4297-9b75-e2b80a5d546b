package com.czur.starry.device.sharescreen.esharelib

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.eshare.serverlibrary.api.EShareCallback
import com.eshare.serverlibrary.api.EShareServerSDK
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

private const val TAG = "EshareByomStatusModel"

interface EshareByomRunningStatusCallBack {
    fun byomRunningCallBack()
}


class EshareByomStatusModel(application: Application) : AndroidViewModel(application) {

    var byomRunningStatusCallBack: EshareByomRunningStatusCallBack? = null

    private val eShareCallback: EShareCallback = object : SimpleEShareCallback() {
        override fun onSettingsChanged(key: String, newValue: Any?) {
            if (key == E_SHARE_BYOM_AUDIO_RUNNING || key == E_SHARE_BYOM_CAMERA_RUNNING) {
                val running = isEshareByomRunning()
                if (!running && byomRunningStatusCallBack != null) {
                    byomRunningStatusCallBack?.byomRunningCallBack()
                    byomRunningStatusCallBack = null
                }
            }
        }
    }

    private val eShareServerSDK by lazy {
        EShareServerSDK.getSingleton(application)
    }

    init {
        registerCallback()
    }

    private fun registerCallback() = viewModelScope.launch(Dispatchers.IO) {
        eShareServerSDK.registerCallback(eShareCallback)
    }

    /// 打开EShareSDK
    fun startShareService() {
        doWithoutCatch {
            eShareServerSDK.startEShareServer()
        }
    }

    override fun onCleared() {
        super.onCleared()
        eShareServerSDK.unregisterCallback(eShareCallback)
    }

    /// 获取byom运行状态
    fun isEshareByomRunning(): Boolean = eShareServerSDK.isBYOMRunning

    /// 停止byom
    fun stopByom() {
        logTagD(TAG, "stopByom")
        eShareServerSDK.stopAllBYOM()
    }

    fun setOnEshareRunningStatusChanged(callback: EshareByomRunningStatusCallBack) {
        this.byomRunningStatusCallBack = callback
    }

}