package com.czur.starry.device.sharescreen.esharelib.util

import android.content.Context
import android.content.Intent
import android.os.Process
import android.provider.Settings
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.common.KEY_HDMI_AIRPLAY_OPEN
import com.czur.starry.device.baselib.common.KEY_LAUNCHER_E_SHARE_MAC
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.notice.MsgType
import com.czur.starry.device.baselib.notice.NoticeHandler
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.fw.proxy.SystemManagerProxy
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.prop.setBooleanSystemProp
import com.czur.starry.device.baselib.utils.sendBroadcastAsUser
import com.czur.starry.device.baselib.utils.prop.setStringSystemProp
import com.czur.starry.device.sharescreen.esharelib.EShareClientType
import com.czur.starry.device.sharescreen.esharelib.E_SHARE_QR_CODE_URL
import com.czur.starry.device.sharescreen.esharelib.KEY_AUDIO_ENABLE
import com.czur.starry.device.sharescreen.esharelib.R
import com.czur.starry.device.sharescreen.esharelib.shareQRCodeUrl
import com.czur.starry.device.sharescreen.esharelib.ui.AirPlayRequestDialog
import com.eshare.serverlibrary.api.EShareServerSDK
import com.eshare.serverlibrary.api.IEShareServerSDK
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2022/1/8
 */

private const val TAG = "EShareUtil"
private val systemManager by lazy { SystemManagerProxy() }

/**
 * 停止屏幕共享
 */
fun stopAllCast(context: Context) {
    val intent = Intent().apply {
        action = "com.ecloud.eairplay.stopbackground.action"
        `package` = "com.ecloud.eairplay"
    }
    try {
        context.sendBroadcastAsUser(intent, Process.myUserHandle())
    } catch (exp: Exception) {
        exp.printStackTrace()
        context.sendBroadcast(intent)
    }
}

/**
 * 判断eShare是否激活
 */
suspend fun isEShareActive(context: Context): Boolean = withContext(Dispatchers.IO) {
    val appContext = context.applicationContext
    val eShareSDK = EShareServerSDK.getSingleton(appContext)
    eShareSDK.isEShareActivated
}

/**
 * 激活EShare
 * 激活的地方有3:
 * 1. 设备调用激活的时候, 会连带激活宜享SDK
 * 2. Launcher在启动的时候, 会激活SDK
 */
suspend fun checkAndActiveEShare(context: Context): Boolean = withContext(Dispatchers.IO) {
    try {
        logTagV(TAG, "checkAndActiveEShare")
        val appContext = context.applicationContext
        val eShareSDK = EShareServerSDK.getSingleton(appContext)
        if (eShareSDK.isEShareActivated) {
            logTagV(TAG, "宜享SDK已激活")
            if (!SettingUtil.ShareScreenSetting.isUserSetHdmiMix()){
                logTagD(TAG, "宜享SDK已激活, 但是没有设置HDMI混合")
                // 设置HDMI混合
                setBooleanSystemProp(KEY_HDMI_AIRPLAY_OPEN, true)
            }
            return@withContext true
        }

        val activeCode = UserHandler.eShareActiveCode
        if (activeCode.isEmpty()) {
            logTagD(TAG, "没有获取到激活码")
            return@withContext false
        }

        logTagD(TAG, "宜享SDK没有激活, 激活SDK")
        // 没有激活
        eShareSDK.startRegisterForLicencekey(activeCode)
        if (!SettingUtil.ShareScreenSetting.isUserSetHdmiMix()){
            logTagD(TAG, "宜享SDK已激活, 但是没有设置HDMI混合")
            // 设置HDMI混合
            setBooleanSystemProp(KEY_HDMI_AIRPLAY_OPEN, true)
        }
        true
    } catch (exp: Exception) {
        logTagW(TAG, "激活出错", tr = exp)
        false
    }

}

/**
 * 重新激活宜享SDK
 */
fun reActiveEShare(context: Context, activeCode: String, activeMac: String) {
    logTagI(TAG, "重新激活宜享SDK:${activeCode}")
    val intent = Intent("com.eshare.action.activate_with_key").apply {
        putExtra("lickey", activeCode)
    }
    sendBroadcastAsUser(context.applicationContext, intent, "com.ecloud.eshare.server")
    setStringSystemProp(KEY_LAUNCHER_E_SHARE_MAC, activeMac)
}

/**
 * 打开/关闭EShare的声音
 */
suspend fun enableEShareVoice(context: Context, enable: Boolean) {
    withContext(Dispatchers.IO) {
        logTagD(TAG, "enableEShareVoice:${enable}")
        val value = if (enable) 1 else 0
        Settings.Global.putInt(context.contentResolver, KEY_AUDIO_ENABLE, value)
    }
}

/**
 * 展示投屏码的Dialog
 */
fun startAirplayRequestDialog(context: Context, clientIp: String, clientName: String) {
    val intent = Intent(context, AirPlayRequestDialog::class.java).apply {
        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        putExtra(AirPlayRequestDialog.KEY_CLIENT_IP, clientIp)
        putExtra(AirPlayRequestDialog.KEY_CLIENT_NAME, clientName)
    }
    doWithoutCatch {
        context.startActivity(intent)
    }

}

/**
 * 取消已显示的Dialog
 */
fun stopAirplayDialog() {
    CZURAtyManager.removeActivityByAtyTag(AirPlayRequestDialog.AIR_PLAY_ATY_TAG)
}

suspend fun showDeviceNameAlertWindow(context: Context, pause: Boolean = false) {
    val enable = SettingUtil.ShareScreenSetting.isNameAlertWindowEnable()
    if (!enable) {
        logTagD(TAG, "showDeviceNameAlertWindow:没有启用设备名称弹窗")
        return
    }
    val intent = Intent().apply {
        `package` = "com.czur.starry.device.sharescreen"
        action = "com.czur.starry.device.sharescreen.DEVICE_NAME_ALERT_WINDOW_SERVICE"
        putExtra("pauseDisplay", pause)
    }
    context.startService(intent)
}

fun stopDeviceNameAlertWindow(context: Context) {
    val intent = Intent().apply {
        `package` = "com.czur.starry.device.sharescreen"
        action = "com.czur.starry.device.sharescreen.DEVICE_NAME_ALERT_WINDOW_SERVICE"
    }
    context.stopService(intent)
}

fun onShowRequestAlert(
    context: Context,
    eShareServerSDK: IEShareServerSDK,
    clientType: EShareClientType,
    clientIp: String?,
    clientName: String?
) {
    if (SettingUtil.ShareScreenSetting.isAskBeforeCast()) {
        logTagD(TAG, "开启询问投屏")
        var showClientName = clientName.orEmpty()
        if (clientType == EShareClientType.DLNA) {
            logTagI(TAG, "DLNA设备, 设备名称使用DLNA代替")
            showClientName = context.getString(R.string.client_name_dlna)
        }
        startAirplayRequestDialog(
            context,
            clientIp.orEmpty(),
            showClientName
        )
    } else {
        logTagD(TAG, "没有开启投屏询问, 默认允许")
        eShareServerSDK.allowScreenCast(clientIp, 7)
        if (systemManager.needPerfConstraint(SystemManagerProxy.PerfConstraintScene.HDMI)) {
            logTagW(TAG, "无线投屏 需要性能约束")
            NoticeHandler.sendMessage(MsgType(MsgType.COMMON, MsgType.COMMON_TOAST)) {
                put(context.getString(R.string.toast_pref_constraint))
            }
        } else {
            logTagV(TAG, "无线投屏 不需要性能约束")
        }
    }
}

val IEShareServerSDK.czQRCodeInfo: String
    get() {
        return qrCodeInfo.orEmpty().replace(E_SHARE_QR_CODE_URL, shareQRCodeUrl)
    }

fun reFormatEShareQRCodeInfo(qrCodeInfo: String): String {
    val ipListKeyStr = "iplist="
    val selfIP = qrCodeInfo.substringAfter("?").substringBefore(":")
    val ipList = qrCodeInfo.substringAfter(ipListKeyStr).substringBefore("&")
    val ipListArray = ipList.replace("null", "").split(":").toMutableSet()
    ipListArray.add(selfIP) // 添加本机ip
    val formatIpList = mutableListOf<String>()
    // 将本机ip调整到第一个
    ipListArray.forEach {
        if (it.isNotEmpty()) {
            if (it == selfIP) {
                formatIpList.add(0, it)
            } else {
                formatIpList.add(it)
            }
        }
    }
    val formatIpListStr = formatIpList.joinToString(":")
    return qrCodeInfo.replace("${ipListKeyStr}${ipList}", "${ipListKeyStr}${formatIpListStr}")
        .also {
            logTagV(TAG, "reFormatEShareQRCodeInfo:${it}")
        }
}