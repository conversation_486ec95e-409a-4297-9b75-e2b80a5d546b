package com.czur.starry.device.sharescreen.esharelib.ui.guide

import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.czur.starry.device.baselib.base.BaseDifferAdapter
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.setDebounceTouchClickListener
import com.czur.starry.device.sharescreen.esharelib.R

/**
 * Created by 陈丰尧 on 2023/8/17
 */
private const val TYPE_ITEM = 0
private const val TYPE_DIVIDER = 1

class GuideCategoryAdapter : BaseDifferAdapter<GuideItemAdapterData>() {
    var onItemTouchDownListener: ((itemData: GuideItemAdapterData) -> Unit)? = null

    override fun bindViewHolder(holder: BaseVH, position: Int, itemData: GuideItemAdapterData) {
        if (itemData.guideCategory == null) return
        holder.tag = itemData
        holder.setText(itemData.guideCategory.nameStr, R.id.guideCategoryTitleTv)
        holder.visible(itemData.isSelect, R.id.guideCategorySelView)
        if (itemData.isSelect) {
            holder.getView<View>(R.id.viewHover).gone()
            holder.setImgResource(itemData.guideCategory.iconSelectedRes, R.id.guideCategoryIconIv)
            holder.getView<TextView>(R.id.guideCategoryTitleTv).setTextColor(android.graphics.Color.WHITE)
        } else {
            holder.setImgResource(itemData.guideCategory.iconDefaultRes, R.id.guideCategoryIconIv)
            holder.getView<TextView>(R.id.guideCategoryTitleTv).setTextColor(android.graphics.Color.parseColor("#393939"))
        }
    }

    override fun areItemsTheSame(
        oldItem: GuideItemAdapterData,
        newItem: GuideItemAdapterData
    ): Boolean {
        return oldItem.guideCategory == newItem.guideCategory
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
        return when (viewType) {
            TYPE_ITEM -> {
                BaseVH(R.layout.item_guide_category, parent).also {
                    val hoverView = it.getView<View>(R.id.viewHover)
                    it.itemView.setOnHoverListener { _, event ->
                        val tag = it.tag as? GuideItemAdapterData ?: return@setOnHoverListener false
                        hoverView.gone(tag.isSelect || (event.action == MotionEvent.ACTION_HOVER_EXIT && !it.itemView.isPressed))
                        true
                    }

                    it.itemView.setDebounceTouchClickListener {_ ->
                        onItemTouchDownListener?.invoke(it.tag as? GuideItemAdapterData ?: return@setDebounceTouchClickListener)
                    }
                }
            }

            TYPE_DIVIDER -> {
                BaseVH(R.layout.item_guide_category_line, parent)
            }

            else -> {
                throw IllegalArgumentException("viewType is error")
            }
        }
    }

    override fun getItemViewType(position: Int): Int {
        return if (getData(position).guideCategory == null) TYPE_DIVIDER else TYPE_ITEM
    }
}