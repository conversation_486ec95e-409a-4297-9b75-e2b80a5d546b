package com.czur.starry.device.sharescreen.esharelib.ui

import android.annotation.SuppressLint
import android.os.Bundle
import androidx.fragment.app.viewModels
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.v2.fragment.floating.CZVBFloatingFragment
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.VersionIndustry
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.widget.toggle.SwitchIcon
import com.czur.starry.device.sharescreen.esharelib.CastFullscreenValue
import com.czur.starry.device.sharescreen.esharelib.ShareViewModel
import com.czur.starry.device.sharescreen.esharelib.databinding.FloatSettingBinding
import com.czur.starry.device.sharescreen.esharelib.util.isEShareActive
import com.eshare.serverlibrary.settings.MultiScreenMode

private const val TAG = "SettingFloat"

class SettingFloat : CZVBFloatingFragment<FloatSettingBinding>() {

    private val shareVM: ShareViewModel by viewModels({ requireActivity() })

    private var initDataTime = 0L

    override fun FloatSettingBinding.initBindingViews() {
        // 关闭按钮
        closeBtn.setOnClickListener {
            dismiss()
        }

        // 多屏同投
        multipleDisplayPreIv.setOnClickListener {
            shareVM.subScreenSize()
        }
        multipleDisplayNextIv.setOnClickListener {
            shareVM.addScreenSize()
        }

        /**
         * 智能满屏功能
         */
        smartFullSwitch.setOnUserSwitchChange { isOn ->
            shareVM.changeCastFullscreen(if (isOn) CastFullscreenValue.ENABLE else CastFullscreenValue.DISABLE)
        }

        /**
         * 设备名悬浮窗
         */
        deviceNameWindowSwitch.setOnUserSwitchChange { isOn ->
            launch {
                shareVM.changeEShareInfoWindowOpen(isOn)
            }
        }

        /**
         * 投屏询问
         */
        askBeforeCastingSwitch.setOnUserSwitchChange { isOn ->
            shareVM.changeAskBeforeCasting(isOn)
        }


        /**
         * hdmi混投
         */
        hdmiSwitch.setOnUserSwitchChange { isOn ->
            launch {
                if (isEShareActive(requireActivity())) {
                    shareVM.changeHDMIOpen(isOn)
                    SettingUtil.ShareScreenSetting.setUserSetHdmiMix(true)  // 标记用户设置过HDMI混投
                } else {//eshare没激活的时候,按钮任何状态都设置为非混投模式,(false)
                    shareVM.changeHDMIOpen(false)
                }
            }
        }

        /**
         * Miracast
         */
        miracastSwitch.setOnUserSwitchChange { isOn ->
            shareVM.changeMiracastOpen(isOn)
        }

        /**
         * Airplay
         */
        airPlaySwitch.setOnUserSwitchChange { isOn ->
            shareVM.changeAirplayOpen(isOn)
        }

        /**
         * DLNA
         */
        dlnaSwitch.setOnUserSwitchChange { isOn ->
            shareVM.changeDLNAOpen(isOn)
        }

        /**
         * Chromecast
         */
        chromecastSwitch.setOnUserSwitchChange { isOn ->
            shareVM.changeChromecastOpen(isOn)
        }

        // 国内版本 不使用Chromecast
        if (Constants.starryHWInfo.salesLocale == StarryDevLocale.Mainland) {
            logTagD(TAG, "国内版本 不使用Chromecast")
            chromecastSwitch.gone()
            labelChromeCast.gone()
        }

        // 军队版不使用Miracast AirPlay
        if (Constants.versionIndustry == VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD) {
            logTagD(TAG, "军队版不使用Miracast AirPlay")
            miracastSwitch.gone()
            airPlaySwitch.gone()
            labelMiracast.gone()
            labelAirPlay.gone()
        }
    }

    @SuppressLint("SetTextI18n")
    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        initDataTime = System.currentTimeMillis()
        // 多画面
        shareVM.multiScreenSizeLive.observe(this) { size ->
            binding.multipleDisplayContentTv.text = size.toString()
            binding.multipleDisplayPreIv.isEnabled = size != MultiScreenMode.SINGLE
            binding.multipleDisplayNextIv.isEnabled = size < shareVM.maxScreenCount
        }

        // 设备名悬浮窗
        shareVM.eShareInfoWindowOpenLive.observe(this) { open ->
            binding.deviceNameWindowSwitch.updateSwitchOn(open)
        }

        // 智能满屏功能
        shareVM.castFullscreenLive.observe(this) { value ->
            val enable = value.toBoolean()
            binding.smartFullSwitch.updateSwitchOn(enable)
        }

        // 投屏询问
        shareVM.askBeforeShareLive.observe(this) { open ->
            binding.askBeforeCastingSwitch.updateSwitchOn(open)
        }

        // hdmi
        shareVM.hdmiOpenLive.observe(this) { open ->
            binding.hdmiSwitch.updateSwitchOn(open)
        }

        //miracast
        shareVM.miracastOpenLive.observe(this) { open ->
            binding.miracastSwitch.updateSwitchOn(open)
        }
        shareVM.miracastEnableLive.observe(this) { enable ->
            if (Constants.versionIndustry == VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD) {
                binding.miracastDisableHintTv.gone()
            } else {
                binding.miracastDisableHintTv.gone(enable)
            }
        }


        //airplay
        shareVM.airplayOpenLive.observe(this) { open ->
            binding.airPlaySwitch.updateSwitchOn(open)
        }
        // DLNA
        shareVM.dlnaOpenLive.observe(this) { open ->
            binding.dlnaSwitch.updateSwitchOn(open)
        }
        // Chromecast
        shareVM.chromecastOpenLive.observe(this) { open ->
            binding.chromecastSwitch.updateSwitchOn(open)
        }
    }

    private fun SwitchIcon.updateSwitchOn(isOn: Boolean) {
        setSwitchOn(isOn, System.currentTimeMillis() - initDataTime < ONE_SECOND)
    }
}