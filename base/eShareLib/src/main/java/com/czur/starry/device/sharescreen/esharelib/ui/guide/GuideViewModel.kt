package com.czur.starry.device.sharescreen.esharelib.ui.guide

import android.app.Application
import android.graphics.drawable.Drawable
import android.text.Spannable
import android.text.SpannableString
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.baselib.utils.appContext
import com.czur.starry.device.baselib.utils.data.LiveDataDelegate
import com.czur.starry.device.baselib.utils.getString
import com.czur.starry.device.baselib.view.CenteredImageSpan
import com.czur.starry.device.sharescreen.esharelib.R
import com.czur.starry.device.sharescreen.esharelib.ShareViewModel
import com.czur.starry.device.sharescreen.esharelib.entity.GuidVideoItem
import com.czur.starry.device.sharescreen.esharelib.entity.GuideVideoEntity
import com.czur.starry.device.sharescreen.esharelib.entity.NoNetworkGuidVideoItem
import com.czur.starry.device.sharescreen.esharelib.ui.guide.GuideCategory.*
import com.eshare.serverlibrary.api.EShareServerSDK
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.map
import kotlin.reflect.KProperty1

/**
 * Created by 陈丰尧 on 2023/8/17
 */
data class GuideItemAdapterData(
    val guideCategory: GuideCategory?,
    val isSelect: Boolean,
)

/**
 * 引导标题信息
 */
data class GuideTitleInfo(
    @StringRes val titleRes: Int,
    @DrawableRes val iconRes: Int,
)

/**
 * 引导视频信息, 目前还没做完全部的视频
 */
data class GuideVideoInfo(
    val netProp: KProperty1<GuideVideoEntity, GuidVideoItem>,
    val videoImgRes: Int? = null,
    val videoNameRes: String? = null,
    val noNetworkGuidVideoItem: NoNetworkGuidVideoItem,
)

/**
 * 一条一条的引导步骤
 */
data class GuideStep(
    val stepStr: CharSequence,
)

/**
 * 扩展信息, 只有硬件投屏器会用上
 */
data class GuideExtraInfo(
    val imgRes: Int,
    val text: String,
)

/**
 * 引导信息
 */
data class GuideInfo(
    val titleInfo: GuideTitleInfo,              // 标题
    val subtitleInfo: GuideTitleInfo? = null,   // 副标题
    val guideVideoInfo: GuideVideoInfo? = null, // 引导视频, 应该都有,但是现在没做好, 先可为空
    val guideStepList: List<GuideStep>,         // 引导步骤
    val remarks: String? = null,                // 备注
    val recommend: String? = null,              // 推荐
    val extraInfo: GuideExtraInfo? = null,      // 扩展信息
)

/**
 * 需要传递投屏名称
 */
class GuideViewModelFactory(
    private val shareViewModel: ShareViewModel
) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        val app = CZURAtyManager.appContext as Application
        return GuideViewModel(app, shareViewModel) as T
    }
}

class GuideViewModel(
    application: Application,
    private val shareViewModel: ShareViewModel // 将 shareViewModel 存储为类属性
) : AndroidViewModel(application) {

    val TAG = "GuideViewModel"

    private val eShareServerSDK by lazy {
        EShareServerSDK.getSingleton(application)
    }

    var deviceName: String = eShareServerSDK.deviceName ?: ""

    fun updateDeviceName(newName: String) {
        if (newName.isNotEmpty()) {
            deviceName = newName
        }
    }

    private val guideItems = mapOf(
        GuideCategoryGroup.System to listOf(
            Windows,
            Android,
            Apple,
            Linux,
        ),
        GuideCategoryGroup.Software to listOf(
            VideoApp,
            CZURShare,
//            GuideCategory.P2P,
        ),
        GuideCategoryGroup.Hardware to listOf(
            ClickDrop,
        ),
    )

    // 当前选择的引导类型
    private val selGuideCategoryFlow = MutableStateFlow<GuideCategory?>(Windows)

    val showGuideCategoryFlow = selGuideCategoryFlow.map { selCategory ->
        val showCategoryList = mutableListOf<GuideItemAdapterData>()
        val addAction = fun(guideCategory: GuideCategory) {
            if (!guideCategory.use) return
            showCategoryList.add(
                GuideItemAdapterData(
                    guideCategory,
                    guideCategory == selCategory,
                )
            )
        }
        guideItems[GuideCategoryGroup.System]?.forEach(addAction)
        showCategoryList.add(
            GuideItemAdapterData(
                null,
                false,
            )
        )
        guideItems[GuideCategoryGroup.Software]?.forEach(addAction)
        showCategoryList.add(
            GuideItemAdapterData(
                null,
                false,
            )
        )
        guideItems[GuideCategoryGroup.Hardware]?.forEach(addAction)
        showCategoryList
    }

    // 根据选择的引导类型, 生成引导信息
    val guideInfoFlow = selGuideCategoryFlow.map { category ->
        when (category) {
            Apple -> GuideInfo(
                titleInfo = GuideTitleInfo(
                    titleRes = R.string.str_guide_title_apple,
                    iconRes = R.drawable.ic_guide_title_apple,
                ),
                guideVideoInfo = GuideVideoInfo(
                    netProp = GuideVideoEntity::ios,
                    videoImgRes = R.drawable.img_apple_device,
                    noNetworkGuidVideoItem = NoNetworkGuidVideoItem.APPLE,
                ),
                guideStepList = listOf(
                    GuideStep(
                        appContext.getString(R.string.str_guide_apple_1)
                    ),
                    GuideStep(
                        appContext.getString(R.string.str_guide_apple_2)
                    ),
                    GuideStep(
                        appContext.getString(R.string.str_guide_apple_3, deviceName)
                    ),
                )
            )

            Android -> GuideInfo(
                titleInfo = GuideTitleInfo(
                    titleRes = R.string.str_guide_title_android,
                    iconRes = R.drawable.ic_guide_title_android,
                ),
                subtitleInfo = GuideTitleInfo(
                    titleRes = R.string.str_guide_sub_title_harmony,
                    iconRes = R.drawable.logo_harmony,
                ),
                guideVideoInfo = GuideVideoInfo(
                    netProp = GuideVideoEntity::android,
                    videoImgRes = when (Constants.starryHWInfo.salesLocale) {
                        StarryDevLocale.Mainland -> R.drawable.img_android_download_qr
                        StarryDevLocale.Overseas -> R.drawable.img_android_download_qr_overseas
                    },
                    videoNameRes = getString(
                        R.string.str_guide_android_download,
                        getString(R.string.str_czur_share_app_name)
                    ),
                    noNetworkGuidVideoItem = NoNetworkGuidVideoItem.ANDROID,
                ),
                guideStepList = listOf(
                    GuideStep(
                        appContext.getString(R.string.str_guide_android_1)
                    ),
                    GuideStep(
                        appContext.getString(R.string.str_guide_android_2)
                    ),
                    GuideStep(
                        appContext.getString(R.string.str_guide_android_3, deviceName)
                    ),

                ),
                recommend = appContext.getString(R.string.str_guide_android_4)
            )

            Windows -> GuideInfo(
                titleInfo = GuideTitleInfo(
                    titleRes = R.string.str_guide_title_windows,
                    iconRes = R.drawable.ic_guide_title_windows,
                ),
                guideVideoInfo = GuideVideoInfo(
                    netProp = GuideVideoEntity::windows,
                    videoImgRes = R.drawable.img_device_pc,
                    noNetworkGuidVideoItem = NoNetworkGuidVideoItem.WINDOWS,
                ),
                guideStepList = listOf(
                    GuideStep(
                        appContext.getString(R.string.str_guide_pc_1)
                    ),
                    GuideStep(
                        appContext.getString(R.string.str_guide_pc_2, deviceName)
                    ),
                ),
                recommend = appContext.getString(
                    R.string.str_guide_pc_3,
                    getString(R.string.str_czur_share_app_name)
                )
            )

            Linux -> GuideInfo(
                titleInfo = GuideTitleInfo(
                    titleRes = R.string.str_guide_title_linux,
                    iconRes = R.drawable.ic_guide_title_linux,
                ),
                guideVideoInfo = GuideVideoInfo(
                    netProp = GuideVideoEntity::linux,
                    videoImgRes = R.drawable.img_dlna,
                    noNetworkGuidVideoItem = NoNetworkGuidVideoItem.LINUX,
                ),
                guideStepList = listOf(
                    GuideStep(
                        appContext.getString(R.string.str_guide_linux_1)
                    ),
                    GuideStep(
                        appContext.getString(R.string.str_guide_linux_2)
                    ),
                    GuideStep(
                        appContext.getString(R.string.str_guide_linux_3, deviceName)
                    ),
                )
            )

            VideoApp -> GuideInfo(
                titleInfo = GuideTitleInfo(
                    titleRes = R.string.str_guide_title_video_app,
                    iconRes = when (Constants.starryHWInfo.salesLocale) {
                        StarryDevLocale.Mainland -> R.drawable.ic_guide_title_video_app_mainland
                        StarryDevLocale.Overseas -> R.drawable.ic_guide_title_video_app
                    }
                ),
                guideVideoInfo = GuideVideoInfo(
                    netProp = GuideVideoEntity::dlna,
                    videoImgRes = when (Constants.starryHWInfo.salesLocale) {
                        StarryDevLocale.Mainland -> R.drawable.img_dlna_mainland
                        StarryDevLocale.Overseas -> R.drawable.img_dlna_os
                    },
                    noNetworkGuidVideoItem = NoNetworkGuidVideoItem.VIDEO_APP,
                ),
                guideStepList = listOf(
                    GuideStep(
                        appContext.getString(R.string.str_guide_video_app_1)
                    ),
                    GuideStep(
                        getIconStepImage(
                            imgDrawable = when (Constants.starryHWInfo.salesLocale) {
                                StarryDevLocale.Mainland -> appContext.getDrawable(R.drawable.ic_category_video_app_mainland)!!
                                StarryDevLocale.Overseas -> appContext.getDrawable(R.drawable.ic_category_video_app)!!
                            },
                            text = when (Constants.starryHWInfo.salesLocale) {
                                StarryDevLocale.Mainland -> appContext.getString(R.string.str_guide_video_app_2_mainland)!!
                                StarryDevLocale.Overseas -> appContext.getString(R.string.str_guide_video_app_2)
                            }
                        )
                    ),
                    GuideStep(
                        when (Constants.starryHWInfo.salesLocale) {
                            StarryDevLocale.Mainland -> appContext.getString(
                                R.string.str_guide_video_app_3,
                                deviceName
                            )

                            StarryDevLocale.Overseas -> appContext.getString(
                                R.string.str_guide_video_app_3_oversea,
                                deviceName
                            )
                        },

                        ),
                )
            )

            CZURShare -> GuideInfo(
                titleInfo = GuideTitleInfo(
                    titleRes = R.string.str_guide_title_czur_share,
                    iconRes = R.drawable.ic_guide_title_czur_share,
                ),
                guideVideoInfo = GuideVideoInfo(
                    netProp = GuideVideoEntity::share,
                    videoImgRes = when (Constants.starryHWInfo.salesLocale) {
                        StarryDevLocale.Mainland -> R.drawable.img_android_download_qr
                        StarryDevLocale.Overseas -> R.drawable.img_android_download_qr_overseas
                    },
                    videoNameRes = getString(
                        R.string.str_guide_android_download,
                        getString(R.string.str_czur_share_app_name)
                    ),
                    noNetworkGuidVideoItem = NoNetworkGuidVideoItem.CZUR_SHARE,
                ),
                guideStepList = listOf(
                    GuideStep(
                        appContext.getString(R.string.str_guide_czur_share_1)
                    ),
                    GuideStep(
                        appContext.getString(R.string.str_guide_czur_share_2, deviceName)
                    ),
                    GuideStep(
                        getIconStepImage(
                            appContext.getDrawable(R.drawable.ic_czur_share_play)!!,
                            appContext.getString(R.string.str_guide_czur_share_3)
                        )
                    ),
                ),
                remarks = appContext.getString(
                    R.string.str_guide_czur_share_4,
                    getString(R.string.str_czur_share_app_name),
                    getString(R.string.str_guide_czur_share_4_store)
                )
            )

            P2P -> GuideInfo(
                titleInfo = GuideTitleInfo(
                    titleRes = R.string.str_guide_title_p2p,
                    iconRes = R.drawable.ic_guide_title_p2p,
                ),
                guideVideoInfo = GuideVideoInfo(
                    netProp = GuideVideoEntity::p2p,
                    videoImgRes = R.drawable.img_p2p,
                    noNetworkGuidVideoItem = NoNetworkGuidVideoItem.P2P,
                ),
                guideStepList = listOf(
                    GuideStep(
                        appContext.getString(R.string.str_guide_p2p_1, Constants.SERIAL)
                    ),
                    GuideStep(
                        appContext.getString(R.string.str_guide_p2p_2)
                    ),
                ),
                remarks = appContext.getString(R.string.str_guide_remark_p2p)
            )

            ClickDrop -> GuideInfo(
                titleInfo = GuideTitleInfo(
                    titleRes = R.string.str_guide_title_click_drop,
                    iconRes = R.drawable.ic_guide_title_click_drop,
                ),
                guideVideoInfo = GuideVideoInfo(
                    netProp = GuideVideoEntity::clickDrop,
                    videoImgRes = when (Constants.starryHWInfo.salesLocale) {
                        StarryDevLocale.Mainland -> R.drawable.img_click_drop
                        StarryDevLocale.Overseas -> R.drawable.img_click_drop_type_c
                    },
                    noNetworkGuidVideoItem = NoNetworkGuidVideoItem.CLICK_DROP,
                ),
                guideStepList = listOf(
                    GuideStep(
                        appContext.getString(R.string.str_guide_click_drop_1)
                    ),
                    GuideStep(
                        appContext.getString(R.string.str_guide_click_drop_2)
                    ),
                    GuideStep(
                        appContext.getString(R.string.str_guide_click_drop_3)
                    ),
                ),
                /// 海外暂时隐藏二维码，后续还需打开
                extraInfo = if (Constants.starryHWInfo.salesLocale == StarryDevLocale.Mainland) {
                    GuideExtraInfo(
                        imgRes = R.drawable.img_click_drop_qrcode,
                        text = appContext.getString(R.string.str_buy_click_drop_hint)
                    )
                } else {
                    null
                }
//                extraInfo = GuideExtraInfo(
//                    imgRes = when (Constants.versionLocation) {
//                        VersionLocation.Mainland -> R.drawable.img_click_drop_qrcode
//                        VersionLocation.Overseas -> R.drawable.img_click_drop_qrcode_overseas
//                    },
//                    text = appContext.getString(R.string.str_buy_click_drop_hint)
//                )
            )

            null -> null
        }
    }.filterNotNull()


    fun updateSelGuideCategory(guideCategory: GuideCategory? = null) {
        val target = guideCategory ?: selGuideCategoryFlow.value
        selGuideCategoryFlow.value = guideCategory
        selGuideCategoryFlow.value = target
    }

    private fun getIconStepImage(imgDrawable: Drawable, text: String): SpannableString {
        val placeholderCharacter = "[ICON]"
        val index = text.indexOf(placeholderCharacter)
        val span = SpannableString(text)
        if (index < 0) return span

        imgDrawable.setBounds(0, 0, 25, 25)
        val imgSpan = CenteredImageSpan(imgDrawable)
        span.setSpan(
            imgSpan,
            index,
            index + placeholderCharacter.length,
            Spannable.SPAN_INCLUSIVE_EXCLUSIVE
        )
        return span
    }
}