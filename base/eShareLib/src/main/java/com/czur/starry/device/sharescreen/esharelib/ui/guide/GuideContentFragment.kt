package com.czur.starry.device.sharescreen.esharelib.ui.guide

import android.content.Intent
import android.os.Bundle
import android.widget.ImageView
import android.widget.TextView
import androidx.fragment.app.viewModels
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.utils.basic.otherwise
import com.czur.starry.device.baselib.utils.basic.yes
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.invisible
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.sharescreen.esharelib.ShareViewModel
import com.czur.starry.device.sharescreen.esharelib.databinding.FragmentGuideContentBinding
import com.czur.starry.device.sharescreen.esharelib.entity.GuidVideoItem
import com.czur.starry.device.sharescreen.esharelib.entity.NoNetworkGuidVideoItem
import com.czur.starry.device.sharescreen.esharelib.ui.GuideNoNetFloat
import com.czur.starry.device.sharescreen.esharelib.widget.GuideImageView
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.map

/**
 * Created by 陈丰尧 on 2023/8/17
 */
private const val TAG = "GuideContentFragment"
private const val ACTION_EXTERNAL_MEDIA = "com.czur.starry.device.file.EXTERNAL_MEDIA"
private const val KEY_SOURCE_TYPE = "srcDataType"
private const val VALUE_SRC_DATA_TYPE_SINGLE = "single" // 单个文件
private const val KEY_SRC_DATA = "srcData"              // 数据文件
private const val KEY_BOOT_FROM = "bootFrom"
private const val VALUE_BOOT_FROM_EXTERNAL = "external" // 外部启动
private const val KEY_EXTERNAL_AUTO_FINISH = "autoFinish"

class GuideContentFragment : CZViewBindingFragment<FragmentGuideContentBinding>() {
    private val guideViewModel: GuideViewModel by viewModels({ requireParentFragment() })
    private val eShareViewModel: ShareViewModel by viewModels({ requireActivity() })

    private val stepViewHolderList by lazy {
        listOf(
            StepViewHolder(
                binding.guideIv1,
                binding.guideTv1,
            ),
            StepViewHolder(
                binding.guideIv2,
                binding.guideTv2,
            ),
            StepViewHolder(
                binding.guideIv3,
                binding.guideTv3,
            ),
            StepViewHolder(
                binding.guideIv4,
                binding.guideTv4,
            ),
        )
    }

    private var videoInfoJob: Job? = null

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        eShareViewModel.deviceNameLive.observe(this){
            guideViewModel.updateDeviceName(it) // 更新 GuideViewModel 中的数据
            guideViewModel.updateSelGuideCategory()
        }

        eShareViewModel.netWorkEnableLive.observe(this) {
            guideViewModel.updateSelGuideCategory()
            launch {
                eShareViewModel.refreshGuideVideoEntity()
            }
        }

        repeatCollectOnResume(guideViewModel.guideInfoFlow) {
            // 副标题
            if (it.subtitleInfo != null) {
                binding.subTitleIconIv.setImageResource(it.subtitleInfo.iconRes)
                binding.subTitleTv.setText(it.subtitleInfo.titleRes)
                binding.subTitleGroup.show()
            } else {
                binding.subTitleGroup.gone()
            }
            updateGuideVideoInfo(it.guideVideoInfo) // 视频
            updateGuideSteps(it.guideStepList)      // 引导步骤
            updateGuideRemark(it.remarks)           // 备注
            updateGuideRecommend(it.recommend)      // 推荐
            updateGuideExtraInfo(it.extraInfo)      // 额外信息
        }
    }

    private fun updateGuideExtraInfo(guideExtraInfo: GuideExtraInfo?) {
        if (guideExtraInfo == null) {
            binding.extraGroup.gone()
        } else {
            binding.extraGroup.show()
            binding.extraIv.setImageResource(guideExtraInfo.imgRes)
            binding.extraTv.text = guideExtraInfo.text
        }
    }

    private fun updateGuideRemark(guideRemark: String?) {
        if (guideRemark == null) {
            binding.remarkGroup.gone()
        } else {
            binding.remarkGroup.show()
            binding.remarkContentTv.text = guideRemark
        }
    }

    private fun updateGuideRecommend(guideRecommend: String?) {
        if (guideRecommend == null) {
            binding.recommendGroup.gone()
        } else {
            binding.recommendGroup.show()
            binding.guideRecommendContentTv.text = guideRecommend
        }
    }

    private fun updateGuideSteps(steps: List<GuideStep>) {
        stepViewHolderList.forEachIndexed { index, stepViewHolder ->
            stepViewHolder.updateStep(steps.getOrNull(index))
        }
    }

    private fun updateGuideVideoInfo(guideVideoInfo: GuideVideoInfo?) {
        videoInfoJob?.cancel()
        videoInfoJob = null
        // 有的视频现还没做出来, 正常不会的
        if (guideVideoInfo == null) {
            binding.guideVideoView.invisible()
            return
        } else {
            binding.guideVideoView.show()
        }

        // 内部的图片
        if (guideVideoInfo.videoImgRes != null) {
            binding.guideVideoIv.setImageResource(guideVideoInfo.videoImgRes)
            binding.guideVideoIv.show()
        } else {
            binding.guideVideoIv.setImageDrawable(null)
            binding.guideVideoIv.gone()
        }

        if (guideVideoInfo.videoNameRes != null) {
            binding.guideVideoTv.setText(guideVideoInfo.videoNameRes)
            binding.guideVideoTv.show()
        } else {
            binding.guideVideoTv.text = null
            binding.guideVideoTv.gone()
        }

        // 视频
        videoInfoJob =
            repeatCollectOnResume(eShareViewModel.guideVideoEntityFlow.filterNotNull().map {
                guideVideoInfo.netProp.get(it)
            }) {
                binding.guideVideoView.setVideoDuration(it)
            }

        binding.guideVideoView.setOnVideoClickListener {
            logTagV(TAG, "guideVideoView click")
            onGuideImageViewClick(binding.guideVideoView, it, guideVideoInfo.noNetworkGuidVideoItem)
        }

    }

    private fun onGuideImageViewClick(
        guideImageView: GuideImageView,
        guideVideoItem: GuidVideoItem?,
        emptyItem: NoNetworkGuidVideoItem
    ) {
        eShareViewModel.canPlayGuideVideo(guideVideoItem).yes {
            bootFileManagerShowVideo(guideVideoItem!!)
            guideImageView.requestHideMask()
        }.otherwise {
            // 展示二维码弹窗
            GuideNoNetFloat(getString(emptyItem.nameRes), emptyItem.url, emptyItem.textSize).show()
        }
    }

    private fun bootFileManagerShowVideo(guidVideoItem: GuidVideoItem) {
        val videoUrl = guidVideoItem.url
        val name = guidVideoItem.name
        logTagV(TAG, "播放视频:${name}: $videoUrl")
        val intent = Intent(ACTION_EXTERNAL_MEDIA).apply {
            putExtra(KEY_SOURCE_TYPE, VALUE_SRC_DATA_TYPE_SINGLE)
            putExtra(KEY_SRC_DATA, videoUrl)
            putExtra(KEY_BOOT_FROM, VALUE_BOOT_FROM_EXTERNAL)   // 外部启动
            putExtra(KEY_EXTERNAL_AUTO_FINISH, true)
        }
        startActivity(intent)
    }

    private inner class StepViewHolder(
        val stepIconIv: ImageView,
        val stepTv: TextView,
    ) {
        fun updateStep(step: GuideStep?) {
            if (step == null) {
                stepIconIv.gone()
                stepTv.gone()
            } else {
                stepIconIv.show()
                stepTv.show()
                stepTv.text = step.stepStr
            }
        }
    }

}