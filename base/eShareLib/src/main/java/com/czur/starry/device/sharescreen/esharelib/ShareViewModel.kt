package com.czur.starry.device.sharescreen.esharelib

import android.app.Application
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Color
import android.net.wifi.WifiManager
import androidx.lifecycle.AndroidViewModel
import com.czur.czurutils.img.QrCodeUtil
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.KEY_ESHARE_PWD
import com.czur.starry.device.baselib.common.KEY_ESHARE_SSID
import com.czur.starry.device.baselib.common.KEY_HDMI_AIRPLAY_OPEN
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.baselib.utils.NetStatusUtil
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.appContext
import com.czur.starry.device.baselib.utils.data.LiveDataDelegate
import com.czur.starry.device.baselib.utils.getShowLength
import com.czur.starry.device.baselib.utils.getString
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.baselib.utils.prop.getStringSystemProp
import com.czur.starry.device.baselib.utils.prop.setBooleanSystemProp
import com.czur.starry.device.sharescreen.esharelib.entity.GuidVideoItem
import com.czur.starry.device.sharescreen.esharelib.entity.GuideVideoEntity
import com.czur.starry.device.sharescreen.esharelib.net.IShareScreenService
import com.czur.starry.device.sharescreen.esharelib.util.czQRCodeInfo
import com.czur.starry.device.sharescreen.esharelib.util.logoBitmap
import com.czur.starry.device.sharescreen.esharelib.util.logoBitmapBK
import com.czur.starry.device.sharescreen.esharelib.util.reFormatEShareQRCodeInfo
import com.czur.starry.device.sharescreen.esharelib.util.showDeviceNameAlertWindow
import com.czur.starry.device.sharescreen.esharelib.util.stopDeviceNameAlertWindow
import com.eshare.serverlibrary.api.EShareCallback
import com.eshare.serverlibrary.api.EShareServerSDK
import com.eshare.serverlibrary.settings.MultiScreenMode
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.withContext

class ShareViewModel(application: Application) : AndroidViewModel(application) {
    companion object {
        private const val TAG = "ScreenShareViewModel"
    }

    // 最多支持的投屏画面数量
    val maxScreenCount = when (Constants.starryHWInfo.model) {
        StarryModel.StudioModel.StudioSPlus,
        StarryModel.StudioModel.StudioSProMax -> MultiScreenMode.MULTI_9
        else -> MultiScreenMode.MULTI_4
    }.also {
        logTagV(TAG, "maxScreenCount:${it}")
    }

    private val wifiManager: WifiManager by lazy {
        appContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
    }

    private val eShareServerSDK by lazy {
        EShareServerSDK.getSingleton(application)
    }

    val netWorkEnableLive = DifferentLiveData(false)
    private var netWorkEnable by LiveDataDelegate(netWorkEnableLive)

    val deviceNameLive = DifferentLiveData("")
    var deviceName: String by LiveDataDelegate(deviceNameLive)
        private set

    // ip地址
    val ipAddressLive = DifferentLiveData("")
    var ipAddress: String by LiveDataDelegate(ipAddressLive)

    // ssid名称
    val ssidNameLive = DifferentLiveData("")
    var ssidName: String by LiveDataDelegate(ssidNameLive)

    val qrCodeImgLive = DifferentLiveData<Bitmap?>(null)
    var qrCodeImg: Bitmap? by LiveDataDelegate(qrCodeImgLive)

    private val qrCodeFlow = MutableStateFlow("")

    // 多画面
    val multiScreenSizeLive = DifferentLiveData(MultiScreenMode.SINGLE)
    private var multiScreenSize: Int by LiveDataDelegate(multiScreenSizeLive)

    // 投屏前询问
    val askBeforeShareLive = DifferentLiveData(false)
    var askBeforeShare: Boolean by LiveDataDelegate(askBeforeShareLive)
        private set

    /**
     * airplay可见性
     */
    val airplayOpenLive = DifferentLiveData(false)
    var airplayOpen: Boolean by LiveDataDelegate(airplayOpenLive)
        private set

    /**
     * chromecast可见性
     */
    val chromecastOpenLive = DifferentLiveData(false)
    var chromecastOpen: Boolean by LiveDataDelegate(chromecastOpenLive)
        private set

    /**
     * 投屏信息悬浮窗
     */
    var eShareInfoWindowOpenLive = DifferentLiveData(false)
    var eShareInfoWindowOpen: Boolean by LiveDataDelegate(eShareInfoWindowOpenLive)
        private set

    /**
     * DLNA功能的开关
     */
    val dlnaOpenLive = DifferentLiveData(false)
    var dlnaOpen: Boolean by LiveDataDelegate(dlnaOpenLive)
        private set

    /**
     * hdmi混投是否开启
     */
    val hdmiOpenLive = DifferentLiveData(false)
    var hdmiOpen: Boolean by LiveDataDelegate(hdmiOpenLive)
        private set


    /**
     * miracast是否开启
     */
    val miracastOpenLive = DifferentLiveData(false)
    var miracastOpen: Boolean by LiveDataDelegate(miracastOpenLive)
        private set

    /**
     * miracast可用性
     */
    val miracastEnableLive = DifferentLiveData(false)
    var miracastEnable: Boolean by LiveDataDelegate(miracastEnableLive)
        private set

    /**
     * 智能满屏
     */
    val castFullscreenLive = DifferentLiveData(CastFullscreenValue.DISABLE)
    private var castFullscreen: CastFullscreenValue by LiveDataDelegate(castFullscreenLive)


    private val netUtil = NetStatusUtil(appContext)

    val loadEShareInfoFinishLive = DifferentLiveData(false)
    private var loadEShareInfoFinish by LiveDataDelegate(loadEShareInfoFinishLive)

    private val netService: IShareScreenService = HttpManager.getService<IShareScreenService>()
    val guideVideoEntityFlow = MutableStateFlow<GuideVideoEntity?>(null)

    val p2pInfoFlow = flow {
        emit(getStringSystemProp(KEY_ESHARE_SSID, "") to getStringSystemProp(KEY_ESHARE_PWD, ""))
    }.flowOn(Dispatchers.IO)


    private val eShareCallback: EShareCallback = object : SimpleEShareCallback() {
        override fun onSettingsChanged(key: String, newValue: Any?) {
            if (newValue == null) {
                return
            }
            logTagD(TAG, "key:${key}, value:${newValue}")
            when (key) {
                E_SHARE_CALLBACK_QRCODE -> {
                    refreshQRCodeInfo()
                }

                E_SHARE_CALLBACK_MULTI_SCREEN_MODE -> {
                    multiScreenSize = (newValue as? Int) ?: MultiScreenMode.SINGLE
                }

                E_SHARE_CALLBACK_AIR_PLAY_ENABLE -> {
                    airplayOpen = newValue as Boolean
                }

                E_SHARE_CALLBACK_CHROMECAST_ENABLE -> {
                    chromecastOpen = newValue as Boolean
                }

                E_SHARE_CALLBACK_DLNA_ENABLE -> {
                    dlnaOpen = newValue as Boolean
                }

                E_SHARE_CALLBACK_NETWORK -> {
                    refreshMiracast()
                }

                E_SHARE_CALLBACK_MIRACAST_ENABLE -> {
                    (newValue as? Boolean)?.let {
                        logTagV(TAG, "miracastOpen:$it")
                        miracastOpen = it
                    }
                }

                else -> {}
            }
        }
    }

    init {
        netUtil.startWatching()
        netUtil.netStatus.observeForever {
            launch {
                refreshNetInfo()
            }
        }

        launch {
            loadShareInfo()
        }

        launch {
            qrCodeFlow.debounce(100).collect {
                qrCodeImg = if (it.isNotEmpty()) {
                    // 只生成一张大图的二维码就行了
                    QrCodeUtil.generateQrCodeBmp(it) {
                        this.pointColor = 0xFF393939.toInt()
                        this.bgColor = Color.WHITE
                        this.edge = 400
                        this.logoConfig {
                            this.logoBmp = logoBitmapBK
                            delPadding = true
                        }
                    }
                } else {
                    null
                }

            }
        }

        launch {
            refreshGuideVideoEntity()
        }
    }

    suspend fun refreshGuideVideoEntity() = withContext(Dispatchers.IO) {
        val response = netService.getGuideVideoEntity()
        if (response.isSuccess) {
            val body = response.body
            if (body != null) {
                guideVideoEntityFlow.value = body
            }
        }
    }

    fun refreshMiracastEnable() {
        miracastEnable = wifiManager.isWifiEnabled.apply {
            logTagD(TAG, "wifiEnable:${this}")
        }
    }

    /**
     * 更新设备名称
     */
    fun updateDeviceName(newName: String) {
        eShareServerSDK.setDeviceName(newName)
        deviceName = newName
    }

    /**
     * 判断设备名称是否合法
     */
    fun isDeviceNameLegal(): Boolean {
        return deviceName.getShowLength() <= E_SHARE_DEVICE_NAME_MAX_LENGTH
    }

    /**
     * 修改airplay是否开启
     */
    fun changeAirplayOpen(open: Boolean) {
        eShareServerSDK.isClientAirPlayEnable = open
    }

    /**
     * 修改hdmi混投可用性
     */
    fun changeHDMIOpen(open: Boolean) {
        // 设置hdmi可投屏
        logTagD(TAG, "changeHDMIOpen:${open}")
        hdmiOpen = open
        setBooleanSystemProp(KEY_HDMI_AIRPLAY_OPEN, open)
    }

    /**
     * 修改Miracast可用性
     */
    fun changeMiracastOpen(open: Boolean) {
        // 设置miracast
        logTagD(TAG, "changeMiracastOpen:${open}")
        eShareServerSDK.miracastEnable = open
        miracastOpen = eShareServerSDK.miracastEnable
    }

    /**
     * 修改投屏码悬浮窗
     */
    suspend fun changeEShareInfoWindowOpen(open: Boolean) {
        logTagD(TAG, "changeEShareInfoWindowOpen:${open}")
        SettingUtil.ShareScreenSetting.setEnableNameAlertWin(open)
        if (open) {
            showDeviceNameAlertWindow(appContext)
        } else {
            stopDeviceNameAlertWindow(appContext)
        }
        eShareInfoWindowOpen = open
    }

    fun changeDLNAOpen(open: Boolean) {
        logTagD(TAG, "changeDLNAOpen:${open}")
        eShareServerSDK.isDlnaEnable = open
        dlnaOpen = open
    }

    /**
     * 修改chromecast开关
     */
    fun changeChromecastOpen(open: Boolean) {
        logTagD(TAG, "changeChromecastOpen:${open}")
        eShareServerSDK.isChromecastEnable = open
        chromecastOpen = open
    }

    /**
     * 修改智能满屏功能
     */
    fun changeCastFullscreen(value: CastFullscreenValue) {
        logTagD(TAG, "changeCastFullscreen:${value}")
        eShareServerSDK.castFullscreen = value.value
        castFullscreen = value
    }

    /**
     * 缩小多画面数量
     */
    fun subScreenSize() {
        val targetSize = when (multiScreenSize) {
            MultiScreenMode.MULTI_2 -> MultiScreenMode.SINGLE
            MultiScreenMode.MULTI_4 -> MultiScreenMode.MULTI_2
            MultiScreenMode.MULTI_9 -> MultiScreenMode.MULTI_4
            else -> multiScreenSize
        }
        eShareServerSDK.multiScreenMode = targetSize
    }

    /**
     * 增加多画面数量
     */
    fun addScreenSize() {
        if (multiScreenSize >= maxScreenCount) {
            return
        }
        val targetSize = when (multiScreenSize) {
            MultiScreenMode.SINGLE -> MultiScreenMode.MULTI_2
            MultiScreenMode.MULTI_2 -> MultiScreenMode.MULTI_4
            MultiScreenMode.MULTI_6,
            MultiScreenMode.MULTI_4 -> MultiScreenMode.MULTI_9
            else -> multiScreenSize
        }

        eShareServerSDK.multiScreenMode = targetSize
    }

    fun refreshPinCode() {
        // 应该是根据回调去刷新
        eShareServerSDK.notifyRefreshPinCode(appContext)

    }


     suspend fun loadShareInfo() = withContext(Dispatchers.IO) {
        eShareServerSDK.registerCallback(eShareCallback)
        // 设备名称
        deviceName = eShareServerSDK.deviceName ?: ""
        // 投屏码
        if (eShareServerSDK.pinCodeMode != PIN_CODE_MODE_DISABLE) {
            logTagW(TAG, "投屏码开启, 关闭投屏码!")
            eShareServerSDK.pinCodeMode = PIN_CODE_MODE_DISABLE
        } else {
            logTagV(TAG, "投屏码已经关闭")
        }

        refreshNetInfo()
        refreshQRCodeInfo()

        // 多画面
        multiScreenSize = eShareServerSDK.multiScreenMode

        // airplay 可见性
        airplayOpen = eShareServerSDK.isClientAirPlayEnable

        // hdmi混投
        hdmiOpen = getBooleanSystemProp(KEY_HDMI_AIRPLAY_OPEN, true)

        // Miracast可用性
        miracastOpen = eShareServerSDK.miracastEnable

        // chromecast可用性
        chromecastOpen = eShareServerSDK.isChromecastEnable

        // 智能满屏功能
        castFullscreen = CastFullscreenValue.create(eShareServerSDK.castFullscreen)

        // 投屏码悬浮窗
        val showAlertWindow = SettingUtil.ShareScreenSetting.isNameAlertWindowEnable()
        logTagV(TAG, "isShowPinWindow:${showAlertWindow}")
        eShareInfoWindowOpen = showAlertWindow

        // 投屏前询问
        askBeforeShare = SettingUtil.ShareScreenSetting.isAskBeforeCast().also {
            logTagD(TAG, "askBeforeShare:${it}")
        }

        // DLNA
        dlnaOpen = eShareServerSDK.isDlnaEnable

        loadEShareInfoFinish = true
    }

    /**
     * 刷新二维码信息
     */
    private fun refreshQRCodeInfo() {
        val qrcodeInfo = eShareServerSDK.czQRCodeInfo
        logTagD(TAG, "qrcode:${qrcodeInfo}")
        if ("0.0.0.0" in qrcodeInfo) {
            logTagW(TAG, "二维码是无网络的")
        }
        qrCodeFlow.value = reFormatEShareQRCodeInfo(qrcodeInfo)
    }

    /**
     * 刷新Miracast功能
     */
    private fun refreshMiracast() {
        miracastOpen = eShareServerSDK.miracastEnable
    }

    private suspend fun refreshNetInfo() = withContext(Dispatchers.IO) {
        val netInfo = netUtil.getNetInfo()
        refreshMiracastEnable()

        ipAddress = netInfo.ipAddr
        // 网络是否可用
        netWorkEnable = netInfo.netStatus != NetStatusUtil.NetStatus.NO_NET_WORK
        // ssid名称
        ssidName = when (netInfo.netStatus) {
            NetStatusUtil.NetStatus.NO_NET_WORK -> getString(R.string.str_no_network_ssid)
            NetStatusUtil.NetStatus.WIFI -> netInfo.ssid
            NetStatusUtil.NetStatus.ETHERNET -> getString(R.string.str_ethernet_ssid)
        }
    }

    fun getNetStatus(): NetStatusUtil.NetStatus {
        return netUtil.getNetInfo().netStatus
    }

    override fun onCleared() {
        eShareServerSDK.unregisterCallback(eShareCallback)
        netUtil.stopWatching()
        super.onCleared()
    }

    fun changeAskBeforeCasting(open: Boolean) {
        askBeforeShare = open
        SettingUtil.ShareScreenSetting.setAskBeforeCast(open)
    }

    fun canPlayGuideVideo(guidVideoItem: GuidVideoItem?): Boolean {
        return guidVideoItem != null && netWorkEnable
    }

}