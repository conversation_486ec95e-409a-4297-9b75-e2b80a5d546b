package com.czur.starry.device.sharescreen.esharelib

import com.czur.czurutils.log.logTagE
import com.eshare.serverlibrary.api.EShareCallback

private const val TAG = "SimpleEShareCallback"

open class SimpleEShareCallback : EShareCallback {
    /**
     * SettingsInfo 和请求投屏内容发生变化时的回调
     */
    override fun onSettingsChanged(key: String, newValue: Any?) {}

    /**
     * 有客户端请求连接
     */
    override fun showRequestAlert(clientIp: String?, clientName: String?, clientType: Int) {}

    /**
     * 客户端取消请求连接
     */
    override fun cancelRequestAlert(clientIp: String?, clientName: String?, clientType: Int) {}


    /**
     * 设备连接提示
     */
    override fun showClientConnectAlert(clientIp: String?, clientName: String?, param: Int) {}

    /**
     * 设备断开提示
     */
    override fun showClientDisconnectAlert(clientIp: String?, clientName: String?, param: Int) {}
    override fun showClientExceptionDisconnectAlert(p0: String?, p1: String?, p2: Int) {
        logTagE(TAG, "showClientExceptionDisconnectAlert")
    }

    /**
     * 设备开始投屏提示
     */
    override fun showClientStartCastAlert(clientIp: String?, clientName: String?, param: Int) {}

    /**
     * 设备停止投屏提示
     */
    override fun showClientStopCastAlert(clientIp: String?, clientName: String?, param: Int) {}
    override fun showClientExceptionStopCastAlert(p0: String?, p1: String?, param: Int) {
        logTagE(TAG, "showClientExceptionStopCastAlert")
    }

    /**
     * @param clientIp：客户端的IP地址。
     * @param type：请求设备类型 mic 或camera
     */
    override fun showClientByomRequest(clientIp: String?, type: Int) {}

    /**
     * 投屏器配对提示
     * @see[ESharePairState]
     */
    override fun notifyPairState(pairState: Int) {}

    override fun notifyEShareServerStart() {}

    override fun notifyUsbDetached() {}

    override fun showWebcastPasswordAlter(var1: String?, var2: String?, var3: String?){}

    override fun notifyWebcastConnect(){}

    override fun notifyAudioChange(var1: String?, var2: String?, var3: Int){}
}