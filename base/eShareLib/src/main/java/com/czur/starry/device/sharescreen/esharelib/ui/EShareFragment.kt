package com.czur.starry.device.sharescreen.esharelib.ui

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.core.widget.doOnTextChanged
import androidx.fragment.app.commit
import androidx.fragment.app.viewModels
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.common.BootParam
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.VersionIndustry
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.tips.setTextAndTip
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.baselib.utils.NetStatusUtil
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.addCharsFilter
import com.czur.starry.device.baselib.utils.closeDefChangeAnimations
import com.czur.starry.device.baselib.utils.doOnLoseFocus
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.invisible
import com.czur.starry.device.baselib.utils.keyboard.keyboardHide
import com.czur.starry.device.baselib.utils.keyboard.keyboardShow
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.setOnActionDoneListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.view.floating.common.SingleBtnCommonFloat
import com.czur.starry.device.baselib.widget.showTextLength
import com.czur.starry.device.sharescreen.esharelib.E_SHARE_DEVICE_NAME_MAX_LENGTH
import com.czur.starry.device.sharescreen.esharelib.R
import com.czur.starry.device.sharescreen.esharelib.ShareViewModel
import com.czur.starry.device.sharescreen.esharelib.databinding.FragmentEshareBinding
import com.czur.starry.device.sharescreen.esharelib.deviceNameBlockChars
import com.czur.starry.device.sharescreen.esharelib.ui.guide.GuideCategoryAdapter
import com.czur.starry.device.sharescreen.esharelib.ui.guide.GuideContentFragment
import com.czur.starry.device.sharescreen.esharelib.ui.guide.GuideViewModel
import com.czur.starry.device.sharescreen.esharelib.ui.guide.GuideViewModelFactory
import com.eshare.serverlibrary.settings.MultiScreenMode

/**
 * Created by 陈丰尧 on 2022/1/10
 */
private const val TAG = "EShareFragment"

class EShareFragment : CZViewBindingFragment<FragmentEshareBinding>() {

    private val shareVM: ShareViewModel by viewModels({ requireActivity() })

    private var settingFloat: SettingFloat? = null
    private var qrCodeFloat: QRCodeFloat? = null

    private var initDataTime = 0L

    private val inEditModeLive = DifferentLiveData(false)

    private val guideViewModel: GuideViewModel by viewModels {
        GuideViewModelFactory(shareVM)
    }
    private val guideCategoryAdapter = GuideCategoryAdapter()

    override fun FragmentEshareBinding.initBindingViews() {

        moreSettingLayer.setOnClickListener {
            settingFloat?.dismiss()
            settingFloat = SettingFloat().apply {
                setOnDismissListener {
                    settingFloat = null
                }
                show()
            }
            changeEditMode(false)    // 弹出对话框时, 关闭输入模式
        }

        qrCodeIv.setOnClickListener {
            qrCodeFloat?.dismiss()
            qrCodeFloat = QRCodeFloat().apply {
                setOnDismissListener { qrCodeFloat = null }
                show()
            }
            changeEditMode(false)    // 弹出对话框时, 关闭输入模式
        }

        airplaySwitch.setOnSwitchChange { isOn, fromClick ->
            if (fromClick) {
                shareVM.changeAirplayOpen(isOn)
                changeEditMode(false)
            }
        }

        miracastSwitch.setOnSwitchChange { isOn, fromClick ->
            if (fromClick) {
                shareVM.changeMiracastOpen(isOn)
                changeEditMode(false)
            }
        }
        dlnaSwitch.setOnSwitchChange { isOn, fromClick ->
            if (fromClick) {
                shareVM.changeDLNAOpen(isOn)
                changeEditMode(false)
            }
        }

        networkSettingIv.setOnClickListener {
            logTagD(TAG, "启动Setting")
            bootSettingNet(shareVM.getNetStatus())
        }

        deviceNameEditIv.setOnClickListener {
            changeEditMode(true)
        }
        // 确定按钮是否可点击
        deviceNameEt.doOnTextChanged { text, _, _, _ ->
            // 没有输入内容时,不可点击
            editConfirmIv.isEnabled = !text.isNullOrBlank()
        }

        deviceNameEt.doOnLoseFocus {
            deviceNameEt.keyboardHide()
        }
        deviceNameEt.showTextLength = E_SHARE_DEVICE_NAME_MAX_LENGTH // 最多输入19个字符(Miracast限制)
        deviceNameEt.addCharsFilter(deviceNameBlockChars)
        deviceNameEt.setOnActionDoneListener {
            if (editConfirmIv.isEnabled) {
                editConfirmIv.performClick()
            } else {
                deviceNameEt.keyboardHide()
            }
            true
        }

        editConfirmIv.setOnClickListener {
            val newName = deviceNameEt.text.toString()
            shareVM.updateDeviceName(newName)
            changeEditMode(false)
        }
        // 取消编辑
        editCancelIv.setOnClickListener {
            changeEditMode(false)
        }

        if (Constants.starryHWInfo.salesLocale != StarryDevLocale.Mainland) {
            p2pHintSummaryTv.visibility = View.GONE
        }

        // 支持的投屏数量
        when (shareVM.maxScreenCount) {
            MultiScreenMode.MULTI_9 -> {
                p2pHintTv.setText(R.string.str_device_count_hint_9)
            }
            else -> {
                p2pHintTv.setText(R.string.str_device_count_hint)
            }
        }

        guideCategoryRv.apply {
            closeDefChangeAnimations()
            adapter = guideCategoryAdapter
            // 这里使用它doOnItemClick 会导致hover的背景来回闪一下
            guideCategoryAdapter.onItemTouchDownListener = {
                val guideCategory =
                    it.guideCategory
                if (guideCategory != null) {
                    guideViewModel.updateSelGuideCategory(guideCategory)
                }
            }
        }

        childFragmentManager.commit {
            replace(binding.contentFl.id, GuideContentFragment())
        }
    }

    private fun changeEditMode(editMode: Boolean) {
        inEditModeLive.value = editMode
    }

    /**
     * 无网络/wifi     启动WiFi页面
     * 有线网络        启动有线网页面
     */
    private fun bootSettingNet(netStatus: NetStatusUtil.NetStatus) {
        when (netStatus) {
            NetStatusUtil.NetStatus.NO_NET_WORK,
            NetStatusUtil.NetStatus.WIFI -> bootAppByAction(BootParam.ACTION_BOOT_NOTICE_SETTING) {
                putExtra(BootParam.BOOT_KEY_SETTING_PAGE_MENU_KEY, "wifi")
            }

            NetStatusUtil.NetStatus.ETHERNET -> bootAppByAction(BootParam.ACTION_BOOT_NOTICE_SETTING) {
                putExtra(BootParam.BOOT_KEY_SETTING_PAGE_MENU_KEY, "wiredNet")
            }
        }
    }

    private fun bootAppByAction(action: String, block: Intent.() -> Unit = {}) {
        val intent = Intent(action)
        intent.block()
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        try {
            startActivity(intent)
        } catch (e: Exception) {
            logTagE(TAG, "启动设置页面失败", tr = e)
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        viewLifecycleOwner.lifecycle.addObserver(object : DefaultLifecycleObserver {
            override fun onStop(owner: LifecycleOwner) {
                super.onStop(owner)
                // 页面不可见时, 取消掉编辑模式
                inEditModeLive.value = false
            }
        })

        initDataTime = System.currentTimeMillis()
        // DeviceName
        shareVM.deviceNameLive.observe(this) {
            binding.deviceNameTv.text = it
        }

        // ip
        shareVM.ipAddressLive.observe(this) {
            val ipStr = if (!it.isNullOrEmpty() && it != "0.0.0.0") {
                getString(R.string.str_info_ip, it)
            } else {
                ""
            }
            binding.ipTv.text = ipStr
        }

        // ssid
        shareVM.ssidNameLive.observe(this) { ssidName ->
            if (ssidName == getString(R.string.str_no_network_ssid)) {
                binding.ssidNameTv.text = ""
                binding.noWifiTv.visibility = View.VISIBLE
            } else {
                binding.ssidNameTv.text = ssidName
                binding.noWifiTv.visibility = View.GONE
            }
        }

        // 二维码
        shareVM.qrCodeImgLive.observe(this) {
            if (it != null) {
                binding.qrCodeIv.setImageBitmap(it)
            }
        }

        // airPlay
        shareVM.airplayOpenLive.observe(this) { open ->
            // 页面刚启动时, airplay的开关要求看不见动画
            binding.airplaySwitch.setSwitchOn(
                open,
                System.currentTimeMillis() - initDataTime < ONE_SECOND
            )
        }

        // miracast
        shareVM.miracastOpenLive.observe(this) { open ->
            binding.miracastSwitch.setSwitchOn(
                open,
                System.currentTimeMillis() - initDataTime < ONE_SECOND
            )
        }
        if (Constants.versionIndustry == VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD) {
            binding.miracastDisableHintTv.gone()
        } else {
            shareVM.miracastEnableLive.observe(this) { enable ->
                if (Constants.versionIndustry == VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD) {
                    binding.miracastDisableHintTv.gone()
                } else {
                    binding.miracastDisableHintTv.gone(enable)
                }
            }
        }

        // dlna
        shareVM.dlnaOpenLive.observe(this) { open ->
            binding.dlnaSwitch.setSwitchOn(
                open,
                System.currentTimeMillis() - initDataTime < ONE_SECOND
            )
        }

        if (Constants.versionIndustry == VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD) {
            logTagI(TAG, "军队版")
            logTagV(TAG, "不显示Miracast,AirPlay")
            binding.miracastSwitch.gone()
            binding.miracastSwitchLabelTv.gone()

            binding.airplaySwitch.gone()
            binding.airplaySwitchLabelTv.gone()

            logTagV(TAG, "去掉引导按钮")
            binding.guideTv.invisible()
        }

        shareVM.loadEShareInfoFinishLive.observe(viewLifecycleOwner) {
            if (it) {
                // 加载完数据后, 判断设备名称是否合法
                checkDeviceName()
            }
        }
        val etParent = binding.deviceNameEt.parent as ViewGroup
        val etLayoutParam = binding.deviceNameEt.layoutParams
        // 编辑模式
        inEditModeLive.observe(viewLifecycleOwner) { inEditMode ->
            if (inEditMode) {
                etParent.addView(binding.deviceNameEt, etLayoutParam)
                binding.deviceNameTv.invisible()
                binding.editGroup.show()
                binding.deviceNameEditIv.gone()
                binding.deviceNameEt.setText(shareVM.deviceName)
                binding.deviceNameEt.requestFocus()
                binding.deviceNameEt.keyboardShow()
                binding.deviceNameEt.setSelection(binding.deviceNameEt.text.length)
            } else {
                binding.deviceNameTv.show()
                binding.editGroup.gone()
                binding.deviceNameEditIv.show()
                // 这里隐藏的的时候, 如果使用触控板的键盘, 还是会看见选词提示窗
                // 所以之间将这个EditText从父布局中移除
                etParent.removeView(binding.deviceNameEt)
            }
        }

        // P2PInfo
        repeatCollectOnResume(shareVM.p2pInfoFlow) { (ssid, pwd) ->
            logTagD(TAG, "P2PInfo: $ssid, $pwd")
            binding.p2pHintSSIDTv.text = getString(R.string.str_p2p_hint_wifi, ssid)
            binding.p2pHintPwdTv.text = getString(R.string.str_p2p_hint_pwd, pwd)
        }

        repeatCollectOnResume(guideViewModel.showGuideCategoryFlow) {
            guideCategoryAdapter.setData(it)
        }
    }


    private fun checkDeviceName() {
        // 判断设备名称是否合法
        if (!shareVM.isDeviceNameLegal()) {
            logTagW(TAG, "设备名称:${shareVM.deviceName}不合法")
            SingleBtnCommonFloat(
                content = getString(R.string.str_device_name_illegal),
            ) {
                it.dismiss()
            }.show()
        }
    }

    override fun onDestroyView() {
        doWithoutCatch {
            settingFloat?.dismiss()
            qrCodeFloat?.dismiss()
        }

        super.onDestroyView()
    }

}