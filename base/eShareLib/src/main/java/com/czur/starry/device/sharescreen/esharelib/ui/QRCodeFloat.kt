package com.czur.starry.device.sharescreen.esharelib.ui

import android.os.Bundle
import androidx.fragment.app.viewModels
import com.czur.starry.device.baselib.base.v2.fragment.floating.CZVBFloatingFragment
import com.czur.starry.device.sharescreen.esharelib.R
import com.czur.starry.device.sharescreen.esharelib.ShareViewModel
import com.czur.starry.device.sharescreen.esharelib.databinding.FloatQrCodeBinding

/**
 * Created by 陈丰尧 on 2021/12/27
 */
class QRCodeFloat : CZVBFloatingFragment<FloatQrCodeBinding>() {
    private val shareVM: ShareViewModel by viewModels({ requireActivity() })

    override fun FloatQrCodeBinding.initBindingViews() {
        closeBtn.setOnClickListener {
            dismiss()
        }

        qrCodeGuideTv.setText(R.string.str_qr_code_guide)
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        shareVM.qrCodeImgLive.observe(this) {
            it?.let {
                binding.qrCodeIv.setImageBitmap(it)
            }
        }
    }
}