package com.czur.starry.device.sharescreen.esharelib.ui.guide

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.sharescreen.esharelib.R

/**
 * Created by 陈丰尧 on 2023/8/17
 * 投屏引导类别
 */
enum class GuideCategoryGroup {
    System,     // 系统
    Software,   // 软件
    Hardware,   // 硬件
}

enum class GuideCategory(
    @StringRes val nameStr: Int,
    @DrawableRes val iconSelectedRes: Int,
    @DrawableRes val iconDefaultRes: Int,
    val use: Boolean = true
) {
    Apple(R.string.str_guide_category_apple, R.drawable.ic_category_apple, R.drawable.ic_category_apple_default),      // 苹果全系列
    Android(R.string.str_guide_category_android, R.drawable.ic_category_android, R.drawable.ic_category_android_default),    // 安卓手机/平板
    Windows(R.string.str_guide_category_windows, R.drawable.ic_category_windows, R.drawable.ic_category_windows_default),    // Windows系统
    Linux(
        R.string.str_guide_category_linux,
        R.drawable.ic_category_linux,
        R.drawable.ic_category_linux_default,
        use = Constants.starryHWInfo.salesLocale == StarryDevLocale.Overseas
    ),      // Linux系统
    VideoApp(R.string.str_guide_category_video_app, R.drawable.ic_category_video_app, R.drawable.ic_category_video_app_default),   // 视频播放应用
    CZURShare(R.string.str_guide_category_czur_share, R.drawable.ic_category_czur_share, R.drawable.ic_category_czur_share_default),  // CZUR Share
    P2P(R.string.str_guide_category_p2p, R.drawable.ic_category_p2p, R.drawable.ic_category_p2p_default),        // P2P投屏
    ClickDrop(R.string.str_guide_category_click_drop, R.drawable.ic_category_click_drop, R.drawable.ic_category_click_drop_default),  // 无线投屏器
}