<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="str_ping_code">Code de screencasting sans fil</string>
    <string name="str_use_guide">Comment effectuer un screencasting sans \nfil avec un téléphone portable ou un ordinateur ?</string>
    <string name="str_ethernet_ssid">Connexion par câble</string>
    <string name="str_info_ip">IP : %s</string>
    <string name="str_info_ssid_label">Réseau actuel</string>
    <string name="float_tip_close">Fermer</string>
    <string name="str_guide_float_title">Comment effectuer un screencasting sans fil avec un téléphone portable ou un ordinateur ?</string>
    <string name="str_guide_apple_1">Assurez que le portable ou l’ordinateur soit sur le même réseau que le StarryHub.</string>
    <string name="str_guide_apple_2">Ouvrez la fonction de mise en miroir de l’écran sur un iPhone, un iPad ou un mac (AirPlay est également pris en charge)</string>
    <string name="str_guide_apple_3">Choisissez %s pour démarrer le screencasting</string>
    <string name="str_czur_share_app_name">CZUR Share</string>
    <string name="str_czur_share_app_name_pc">CZUR Share</string>
    <string name="str_guide_android_download">Lisez le code QR pour télécharger l’appli \"%s\"</string>
    <string name="str_guide_android_1">Assurez que le téléphone soit sur le même réseau que le StarryHub.</string>
    <string name="str_guide_pc_1">Assurez que l’ordinateur soit sur le même WiFi que le StarryHub.</string>
    <string name="str_guide_pc_2">Appuyez sur WIN+K sur un clavier d’ordinateur Windows, sélectionnez %s, commencez le screencasting.</string>
    <string name="str_guide_pc_3">Si vous ne pouvez pas caster l’écran de cette manière, ou si vous ne trouvez pas le StarryHub, ou encore si votre ordinateur ne prend pas en charge Miracast, vus pouvez télécharger %1$s depuis www.czur.com et caster l’écran depuis %1$s.</string>
    <string name="str_guide_recommend">★ Les pus recommandés. </string>
    <string name="str_guide_click_drop_1">Insérez ClickDrop dans le port USB situé à l’arrière du StarryHub, et attendez que l’appariement se termine.</string>
    <string name="str_guide_click_drop_2">Connectez ClickDrop à l’ordinateur. Le voyant LED clignoter. Attendez qu’il reste allumé sans clignoter.</string>
    <string name="str_guide_click_drop_3">Appuyez sur le bouton du ClickDrop pour commencer le screencasting.</string>
    <string name="str_guide_click_drop_hint">Prêt à l\'emploi. Pas besoin de connexion Internet.</string>
    <string name="btn_guide_click_drop_buy">Acheter</string>
    <string name="title_setting">Paramètres</string>
    <string name="str_device_name">Nom de l’afficheur</string>
    <string name="item_setting_pin_code">Mot de passe</string>
    <string name="item_setting_multiple_display">Caster plusieurs écrans à la fois</string>
    <string name="item_setting_smart_full_screen">Plein écran </string>
    <string name="item_setting_ask_before_casting">Confirmation du screencasting </string>
    <string name="item_setting_airplay_visibility">AirPlay affiché</string>
    <string name="item_setting_miracast_visibility">Miracast</string>
    <string name="item_setting_pin_code_window">Fenêtre de mot de passe</string>
    <string name="item_setting_device_name_float_window">Fenêtre flottante du nom du StarryHub</string>
    <string name="setting_options_enable">Activer</string>
    <string name="setting_options_disable">Désactiver</string>
    <string name="title_miracast_hint">Notification</string>
    <string name="str_cancel">Annuler</string>
    <string name="str_miracast_open">Activer</string>
    <string name="str_no_network_ssid">Vous n\'êtes pas connecté à l\'Internet</string>
    <string name="str_qrcode_float_title">Lisez pour démarrer</string>
    <string name="str_miracast_disable_hint">La WiFi doit être allumée pour utiliser Miracast.</string>
    <string name="str_air_play_disable_hint">L’utilisation d’AirPlay exige une connexion Internet</string>
    <string name="str_dlna_disable_hint">L’utilisation de DLNA exige une connexion Internet</string>
    <string name="str_airplay_request">Vous venez de recevoir une demande de screencasting de %s. Voulez-vous l’accepter ?</string>
    <string name="str_airplay_request_reject">Non</string>
    <string name="str_airplay_request_agree">Oui</string>
    <string name="str_guide_category_apple">macOS et iOS</string>
    <string name="str_guide_category_android">Téléphone ou tablette Android</string>
    <string name="str_guide_category_windows">Ordinateur à SE Windows</string>
    <string name="str_guide_category_czur_share">CZUR Share</string>
    <string name="str_guide_category_click_drop">ClickDrop</string>
    <string name="str_guide_title_apple">macOS et iOS</string>
    <string name="str_guide_title_android">Téléphone ou tablette Android</string>
    <string name="str_guide_title_windows">Ordinateur à SE Windows</string>
    <string name="str_guide_title_czur_share">CZUR Share</string>
    <string name="str_guide_title_click_drop">ClickDrop</string>
    <string name="str_guide_sub_title_harmony">téléphone ou tablette</string>
    <string name="str_guide_item_click_drop_title">Passerelle de screencasting sans fil ClickDrop\n</string>
    <string name="str_qr_code_guide">Code QR de casting de l’appli\n de CZUR Share</string>
    <string name="str_qr_code_guide_overseas">Code QR de screencasting\n sans fil de l’appli CZUR</string>
    <string name="title_buy_click_drop">ClickDrop</string>
    <string name="str_device_count_hint">Vus pouvez caster un max. de 4 écrans.</string>
    <string name="str_float_tips_device_count_hint">Screencasting sans fil pour jusqu’à quatre appareils à la fois.</string>
    <string name="str_device_name_illegal">Ce nom de screencasting n’est pas valide. Modifiez-le dans les paramètres.</string>
    <string name="str_title_eshare_screen_projection">Téléchargez l’appli pour faire le screencasting.</string>
    <string name="str_title_direct_screen_projection">Faites le screencasting directement.</string>
    <string name="str_title_miracast">Miracast</string>
    <string name="str_title_airplay">AirPlay</string>
    <string name="str_miracast_device">Windows</string>
    <string name="str_airplay_device">iOS / macOS</string>
    <string name="str_more_setting">Plus de paramètres</string>
    <string name="str_qrcode_label">Lire pour télécharger ou\npour caster l’écran</string>
    <string name="str_airplay_hint">L’utilisation d’AirPlay exige une connexion Internet</string>
    <string name="str_ask_before_casting_hint">Demande de confirmation avant de recevoir du screencasting.</string>
    <string name="str_guide_no_network">Aucun réseau détecté. Lisez le code pour regarder.</string>
    <string name="title_guide_no_network_apple">Tutoriel de screencasting sans fil pour SE Apple</string>
    <string name="title_guide_no_network_windows">Tutoriel de screencasting sans fil pour SE Windows</string>
    <string name="title_guide_no_network_click_drop">Tutoriel de screencasting sans fil pour ClickDrop</string>
    <string name="title_guide_no_network_android">Tutoriel de casting d\'écran sans fil pour le système d\'exploitation Android</string>
    <string name="str_guide_android_2">Ouvrez le menu contextuel du téléphone ou accédez aux paramètres pour trouver l\'option « Screen Casting » (ex. : casting d\'écran sans fil, affichage sans fil, partage d\'écran, projection d\'écran, interaction multi-écran, etc.). Activez la fonction de casting d\'écran sans fil. </string>
    <string name="str_guide_android_3">Sélectionnez 【%s】 pour démarrer le casting d\'écran.</string>
    <string name="str_guide_android_4">Il est possible que les méthodes mentionnées ci-dessus ne puissent pas réaliser le casting d\'écran sans fil. Parce que votre téléphone ne prend peut-être pas en charge le protocole Miracast ou en raison d\'un problème de compatibilité. Si vous ne trouvez pas StarryHub via l\'une des méthodes ci-dessus, veuillez télécharger l\'application \'CZUR Share\' depuis www.czur.com et caster l\'écran depuis l\'application \'CZUR Share\'.</string>
    <string name="str_guide_linux_1">Assurez-vous que votre appareil et StarryHub sont sous le même réseau Wi-Fi.</string>
    <string name="str_guide_linux_2">Ouvrez le navigateur \'Google Chrome\', cliquez sur le bouton \' ⋮ \' dans le coin supérieur gauche du navigateur pour plus d\'options, puis sélectionnez \'Cast...\'.</string>
    <string name="str_guide_linux_3">Sélectionnez 【%s】 pour démarrer le casting d\'écran.</string>
    <string name="str_guide_video_app_1">Veuillez vous assurer que votre appareil et StarryHub sont sous le même réseau Wi-Fi.</string>
    <string name="str_guide_video_app_2">Cliquez sur le bouton [ICON] de l\'application vidéo.</string>
    <string name="str_guide_video_app_3">Sélectionnez 【%s（DLNA）】 pour démarrer le casting de l\'écran vidéo.</string>
    <string name="str_guide_video_app_3_oversea">Sélectionnez 【%s】 pour démarrer le casting d\'écran vidéo.</string>
    <string name="str_guide_czur_share_1">Veuillez vous assurer que votre appareil et StarryHub sont sous le même réseau Wi-Fi.</string>
    <string name="str_guide_czur_share_2">Ouvrez l\'application \'CZUR Share\', sélectionnez 【%s】, cliquez sur \'Connect\'.</string>
    <string name="str_guide_czur_share_3">Cliquez sur [ICON] pour démarrer le casting d\'écran.</string>
    <string name="str_guide_czur_share_4">  Vous pouvez télécharger %1$s depuis www.czur.com ou rechercher et installer « %1$s » depuis l\'App Store de votre téléphone, puis caster l\'écran.</string>
    <string name="str_guide_czur_share_4_store">APP Store</string>
    <string name="str_guide_p2p_1">Connectez votre téléphone mobile ou votre ordinateur au point d\'accès de StarryHub (nom du point d\'accès Wi-Fi %s, mot de passe changer007). </string>
    <string name="str_guide_p2p_2">"Méthodes prises en charge par téléphone mobile : Airplay, Miracast, CZUR Share, DLNA (fichiers locaux uniquement), passerelle de casting d\'écran sans fil. Méthodes prises en charge par ordinateur : Airplay, Miracast, CZUR Share, passerelle de casting d\'écran sans fil."</string>
    <string name="str_guide_remark_label">Remarque</string>
    <string name="str_guide_remark_czur_share">Vous pouvez télécharger l\'application « CZUR Share » sur www.czur.com ou rechercher l\'application « CZUR Share » depuis votre APP Store afin d\'utiliser « CZUR Share » pour le casting d\'écran.</string>
    <string name="str_guide_remark_p2p">Une connexion Wi-Fi est requise pour la première utilisation à des fins d\'activation.</string>
    <string name="str_eshare_quit_title">Souhaitez-vous arrêter le partage d’écran ?</string>
    <string name="dialog_normal_cancel">Non</string>
    <string name="dialog_normal_confirm">Oui</string>
    <string name="str_guide_category_linux">Système Linux</string>
    <string name="str_guide_category_video_app">Application vidéo</string>
    <string name="str_guide_category_p2p">Casting d’écran P2P.</string>
    <string name="str_guide_title_linux">Système Linux (Chromecast)</string>
    <string name="str_guide_title_video_app">Application vidéo</string>
    <string name="str_guide_title_p2p">Casting d’écran P2P.</string>
    <string name="str_buy_click_drop_hint">Scannez le code QR pour acheter</string>
    <string name="str_float_tips_device_content_hint">Sauf DLNA ou Airplay.</string>
    <string name="str_p2p_hint">Le casting d’écran P2P est plus stable.</string>
    <string name="str_p2p_hint_summary">Veuillez connecter votre ordinateur/téléphone au point d\'accès Wi-Fi suivant.</string>
    <string name="str_p2p_hint_pwd">Mot de passe %s</string>
    <string name="str_title_hdmi">Partage d\'écran HDMI</string>
    <string name="title_guide_no_network_linux">Tutoriel de casting d\'écran sans fil pour le système d\'exploitation Linux</string>
    <string name="title_guide_no_network_dlna">Tutoriel de casting d\'écran sans fil pour applications vidéo</string>
    <string name="title_guide_no_network_czur_share">Tutoriel de casting d\'écran sans fil pour l\'application CZUR Share</string>
    <string name="title_guide_no_network_p2p">Tutoriel de casting d\'écran sans fil pour P2P</string>
    <string name="str_float_tips_device_peripheral_mode_hint">Le mode périphérique et le partage d\'écran peuvent être appliqués en même temps.</string>
    <string name="str_guide_video_app_2_mainland">Cliquez sur l\'icône TV sur l\'application de visioconférence.</string>
    <string name="str_float_tips_device_content_hint_army">En plus du dlna</string>
</resources>
