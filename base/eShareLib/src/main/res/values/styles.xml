<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="tv_guide_item">
        <item name="android:layout_width">0px</item>
        <item name="android:maxWidth">550px</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginLeft">15px</item>
        <item name="android:textSize">20px</item>
        <item name="android:textColor">@color/text_common</item>
        <item name="android:lineSpacingExtra">5px</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">left</item>
    </style>

    <style name="tv_guide_item_recommend" >
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">16px</item>
        <item name="android:textColor">@color/bg_main_blue</item>
        <item name="android:layout_marginBottom">3px</item>
    </style>

    <style name="iv_guide_item">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginRight">20px</item>
    </style>

    <style name="iv_guide_item_other" parent="iv_guide_item">
        <item name="android:layout_marginTop">20px</item>
    </style>

    <style name="tv_setting_label">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">30px</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="tv_setting_content">
        <item name="android:layout_width">0px</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">30px</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="tv_guide_item_title">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">22px</item>
        <item name="android:textStyle">bold</item>
        <item name="android:layout_marginLeft">14px</item>
    </style>

    <style name="iv_guide_item_logo">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginLeft">44px</item>
    </style>

    <style name="tv_guide_icon">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">70px</item>
        <item name="android:layout_marginBottom">30px</item>
        <item name="android:gravity">center</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:paddingLeft">68px</item>
        <item name="android:paddingRight">68px</item>
        <item name="android:textColor">#FF5879fc</item>
        <item name="android:textSize">30px</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="tv_qrcode_label_Style">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">4px</item>
    </style>

</resources>