<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="str_ping_code">Stato di trasmissione wireless dello schermo</string>
    <string name="str_use_guide">Come eseguire la trasmissione wireless dello schermo\ncon un telefono cellulare o un computer?</string>
    <string name="str_ethernet_ssid">Collegamenti cavi</string>
    <string name="str_info_ip">IP: %s</string>
    <string name="str_info_ssid_label">Rete attuale</string>
    <string name="float_tip_close">Chiudi</string>
    <string name="str_guide_float_title">Come eseguire la trasmissione wireless dello schermo con un telefono cellulare o un computer?</string>
    <string name="str_guide_apple_1">Accertati che il telefono cellulare o il computer siano nella stessa rete di StarryHub.</string>
    <string name="str_guide_apple_2">Apri la funzione di mirroring dello schermo su iPhone/iPad/mac (supporta anche AirPlay)</string>
    <string name="str_guide_apple_3">Scegli %s per avviare la trasmissione dello schermo</string>
    <string name="str_czur_share_app_name">CZUR Share</string>
    <string name="str_czur_share_app_name_pc">CZUR Share</string>
    <string name="str_guide_android_download">Scansiona il codice QR per scaricare l\'APP \"%s\"</string>
    <string name="str_guide_android_1">Accertati che il telefono sia nella stessa rete di StarryHub.</string>
    <string name="str_guide_pc_1">Accertati il computer sia nella stessa Wi-Fi di StarryHub.</string>
    <string name="str_guide_pc_2">Premere WIN+K sulla tastiera del computer Windows, selezionare %s, avviare la trasmissione dello schermo.</string>
    <string name="str_guide_pc_3">Se non si riesce a trasmettere lo schermo seguendo queste operazioni, o non si riesce a trovare StarryHub, o il computer non supporta Miracast, è possibile scaricare %1$s da www.czur.com e trasmettere lo schermo da %1$s.</string>
    <string name="str_guide_recommend">★ Consigliatissimo. </string>
    <string name="str_guide_android_2">Aprire il menu di scelta rapida del telefono o andare nelle impostazioni per trovare l\'opzione "Screen Casting” (Trasmissione dello schermo) (ad esempio: trasmissione dello schermo in modalità wireless, display wireless, condivisione dello schermo, proiezione dello schermo, interazione multischermo, ecc.) Attivare la funzione di Trasmissione dello schermo in modalità wireless. </string>
    <string name="str_guide_android_3">Seleziona 【%s】 per avviare la trasmissione dello schermo.</string>
    <string name="str_guide_android_4">È possibile che i metodi sopra menzionati non siano in grado di realizzare la Trasmissione dello schermo in modalità wireless. Questo accade perché il telefono potrebbe non supportare il protocollo Miracast o per problemi di compatibilità. Se non si riesce a trovare il dispositivo StarryHub con uno dei metodi sopra indicati, scaricare l\'applicazione \'CZUR Share\' da www.czur.com e trasmettere lo schermo dall\'applicazione \'CZUR Share\'.</string>
    <string name="str_guide_linux_1">Verifica che il tuo dispositivo e il dispositivo StarryHub si trovino nella stessa rete Wi-Fi.</string>
    <string name="str_guide_linux_2">Aprire il browser \'Google Chrome\', fare clic sul pulsante \'⋮\' nell\'angolo in alto a sinistra del browser per visualizzare altre opzioni, quindi selezionare \'Cast...\' (Trasmetti).</string>
    <string name="str_guide_linux_3">Seleziona 【%s】 per avviare la trasmissione dello schermo.</string>
    <string name="str_guide_video_app_1">Verifica che il tuo dispositivo e il dispositivo StarryHub si trovino nella stessa rete Wi-Fi.</string>
    <string name="str_guide_video_app_2">Fare clic sul pulsante [ICON] dell’app video.</string>
    <string name="str_guide_video_app_3">Selezionare 【%s（DLNA）】 per avviare la trasmissione della schermata video.</string>
    <string name="str_guide_video_app_3_oversea">Selezionare 【%s】 per avviare la trasmissione della schermata video.</string>
    <string name="str_guide_czur_share_1">Verifica che il tuo dispositivo e il dispositivo StarryHub si trovino nella stessa rete Wi-Fi.</string>
    <string name="str_guide_czur_share_2">Aprire l’app \'CZUR Share\', selezionare 【%s】, fare clic su \'Connetti\'.</string>
    <string name="str_guide_czur_share_3">Fare clic su [ICON] per avviare la trasmissione dello schermo.</string>
    <string name="str_guide_czur_share_4">  È possibile scaricare %1$s da www.czur.com o cercare e installare "%1$s" dall\'App Store del telefono, quindi avviare la trasmissione dello schermo.</string>
    <string name="str_guide_czur_share_4_store">APP Store</string>
    <string name="str_guide_p2p_1">Collegare il cellulare o il computer all\'access point di StarryHub (nome dell’hotspot Wi-Fi: %s, password: changer007).</string>
    <string name="str_guide_p2p_2">"Metodi supportati dai telefoni cellulari: Airplay, Miracast, CZUR Share, DLNA (solo file locali), gateway di Trasmissione dello schermo in modalità wireless. Metodi supportati dal computer: Airplay, Miracast, CZUR Share, gateway di Trasmissione dello schermo in modalità wireless”</string>
    <string name="str_guide_remark_label">Nota</string>
    <string name="str_guide_remark_czur_share">Sarà possibile scaricare l\'applicazione \'CZUR Share\' dal sito www.czur.com o cercare l\'applicazione \'CZUR Share\' nell\'APP Store per utilizzare \'CZUR Share\' per la trasmissione dello schermo.</string>
    <string name="str_guide_remark_p2p">La connessione Wi-Fi è necessaria per il primo utilizzo ai fini dell\'attivazione.</string>
    <string name="str_eshare_quit_title">Volete interrompere la trasmissione dello schermo?</string>
    <string name="dialog_normal_cancel">No</string>
    <string name="dialog_normal_confirm">Sì</string>
    <string name="str_guide_category_linux">Sistema Linux</string>
    <string name="str_guide_category_video_app">App video</string>
    <string name="str_guide_category_p2p">Trasmissione schermo P2P</string>
    <string name="str_guide_title_linux">Sistema Linux (Chromecast)</string>
    <string name="str_guide_title_video_app">App video</string>
    <string name="str_guide_title_p2p">Trasmissione schermo P2P</string>
    <string name="str_buy_click_drop_hint">Scansionare il codice QR per acquistare</string>
    <string name="str_float_tips_device_content_hint">Tranne DLNA</string>
    <string name="str_p2p_hint">La trasmissione dello schermo P2P è più stabile.</string>
    <string name="str_p2p_hint_summary">Collegare il computer/telefono al seguente hotspot Wi-Fi.</string>
    <string name="str_p2p_hint_pwd">Password %s</string>
    <string name="str_title_hdmi">Condivisione dello schermo HDMI</string>
    <string name="title_guide_no_network_android">Tutorial per la Trasmissione dello schermo in modalità wireless per Android OS</string>
    <string name="title_guide_no_network_linux">Tutorial per la Trasmissione dello schermo in modalità wireless per Linux OS</string>
    <string name="title_guide_no_network_dlna">Tutorial per la Trasmissione dello schermo in modalità wireless per le app video</string>
    <string name="title_guide_no_network_czur_share">Tutorial per la Trasmissione dello schermo in modalità wireless per l’app CZUR Share</string>
    <string name="title_guide_no_network_p2p">Tutorial per la Trasmissione dello schermo in modalità wireless per P2P</string>
    <string name="str_float_tips_device_peripheral_mode_hint">La Modalità periferica e la Condivisione dello schermo possono essere applicate contemporaneamente.</string>
    <string name="str_guide_video_app_2_mainland">Clicca sull’icona TV nell\'app per riunioni video.</string>
    <string name="str_guide_click_drop_1">Inserire ClickDrop nella porta USB sul retro di StarryHub, attendere il completamento del processo di associazione.</string>
    <string name="str_guide_click_drop_2">Collegare ClickDrop al computer; la spia LED di ClickDrop lampeggia; attendere che la spia sia illuminata fissa.</string>
    <string name="str_guide_click_drop_3">Premere il pulsante su ClickDrop per avviare la trasmissione dello schermo.</string>
    <string name="str_guide_click_drop_hint">Plug &amp; play. Non è richiesta la connessione a Internet.</string>
    <string name="btn_guide_click_drop_buy">Acquista</string>
    <string name="title_setting">Impostazioni</string>
    <string name="str_device_name">Nome del display</string>
    <string name="item_setting_pin_code">Password</string>
    <string name="item_setting_multiple_display">Trasmetti più schermi\ncontemporaneamente</string>
    <string name="item_setting_smart_full_screen">Schermo intero </string>
    <string name="item_setting_ask_before_casting">Conferma della trasmissione \ndello schermo</string>
    <string name="item_setting_airplay_visibility">AirPlay visibile</string>
    <string name="item_setting_miracast_visibility">Miracast</string>
    <string name="item_setting_pin_code_window">Finestra password</string>
    <string name="item_setting_device_name_float_window">Finestra flottante di StarryHub Name</string>
    <string name="setting_options_enable">Attiva</string>
    <string name="setting_options_disable">Disattiva</string>
    <string name="title_miracast_hint">Notifica</string>
    <string name="str_cancel">Annulla</string>
    <string name="str_miracast_open">Attiva</string>
    <string name="str_no_network_ssid">Nessuna connessione a Internet</string>
    <string name="str_qrcode_float_title">Scansionare per avviare</string>
    <string name="str_miracast_disable_hint">Il Wi-Fi deve essere acceso per utilizzare Miracast.</string>
    <string name="str_air_play_disable_hint">L\'uso di AirPlay richiede una connessione a Internet</string>
    <string name="str_dlna_disable_hint">L\'uso di DLNA richiede una connessione a Internet</string>
    <string name="str_airplay_request">È stata appena ricevuta una richiesta di trasmissione dello schermo da %s. Vuoi accettare?</string>
    <string name="str_airplay_request_reject">No</string>
    <string name="str_airplay_request_agree">Sì</string>
    <string name="str_guide_category_apple">macOS e iOS</string>
    <string name="str_guide_category_android">Telefono/tablet Android</string>
    <string name="str_guide_category_windows">Computer con sistema operativo \nWindows</string>
    <string name="str_guide_category_czur_share">CZUR Share</string>
    <string name="str_guide_category_click_drop">ClickDrop</string>
    <string name="str_guide_title_apple">macOS e iOS</string>
    <string name="str_guide_title_android">Telefono/tablet Android</string>
    <string name="str_guide_title_windows">Computer con sistema operativo Windows</string>
    <string name="str_guide_title_czur_share">CZUR Share</string>
    <string name="str_guide_title_click_drop">ClickDrop</string>
    <string name="str_guide_sub_title_harmony">telefono / tablet</string>
    <string name="str_guide_item_click_drop_title">Gateway per ClickDrop\nTrasmissione dello schermo in modalità Wireless</string>
    <string name="str_qr_code_guide">APP CZUR Share\n Trasmissione del codice QR</string>
    <string name="str_qr_code_guide_overseas">Schermata wireless dell\'APP CZUR\n Trasmissione del codice QR</string>
    <string name="title_buy_click_drop">ClickDrop</string>
    <string name="str_device_count_hint">Trasmetti fino a un massimo di quattro schermi.</string>
    <string name="str_float_tips_device_count_hint">Trasmissione dello schermo in modalità wireless fino a quattro dispositivi contemporaneamente.</string>
    <string name="str_device_name_illegal">Il nome della trasmissione dello schermo non è valido. Modificare nelle impostazioni.</string>
    <string name="str_title_eshare_screen_projection">Scaricare l\'app per trasmettere lo schermo.</string>
    <string name="str_title_direct_screen_projection">Eseguire la trasmissione dello schermo direttamente.</string>
    <string name="str_title_miracast">Miracast</string>
    <string name="str_title_airplay">AirPlay</string>
    <string name="str_miracast_device">Windows</string>
    <string name="str_airplay_device">iOS / macOS</string>
    <string name="str_more_setting">Altre impostazioni</string>
    <string name="str_qrcode_label">Scansionare per scaricare \no trasmettere lo schermo</string>
    <string name="str_airplay_hint">L\'uso di AirPlay richiede una connessione a Internet</string>
    <string name="str_ask_before_casting_hint">Richiede una conferma prima di ricevere le schermate di proiezione.</string>
    <string name="str_guide_no_network">Nessuna rete rilevata, scansionare il codice per guardare.</string>
    <string name="title_guide_no_network_apple">Tutorial sulla trasmissione dello schermo wireless per Apple OS</string>
    <string name="title_guide_no_network_windows">Tutorial sulla trasmissione dello schermo in modalità wireless per Windows OS</string>
    <string name="title_guide_no_network_click_drop">Tutorial sulla trasmissione dello schermo in modalità wireless per ClickDrop</string>
    <string name="str_float_tips_device_content_hint_army">Tranne DLNA</string>
</resources>
