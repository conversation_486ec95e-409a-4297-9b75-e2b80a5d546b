<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="800px"
    android:layout_height="wrap_content"
    app:bl_solid_color="@color/dialog_bg_color"
    app:bl_corners_radius="10px"
    tools:ignore="PxUsage,MissingPrefix,RtlHardcoded,ContentDescription">

    <View
        android:id="@+id/bgTopView"
        android:layout_width="match_parent"
        android:layout_height="85px"
        app:bl_corners_topLeftRadius="10px"
        app:bl_corners_topRightRadius="10px"
        app:bl_solid_color="#4f6ce3"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="#4f6ce3" />

    <ImageView
        android:id="@+id/closeBtn"
        android:layout_width="60px"
        android:layout_height="60px"
        android:layout_marginRight="20px"
        android:src="@drawable/ic_dialog_close"
        app:float_tips="@string/float_tip_close"
        app:layout_constraintBottom_toBottomOf="@id/bgTopView"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/bgTopView" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="58px"
        android:includeFontPadding="false"
        android:text="@string/title_setting"
        android:textColor="@color/white"
        android:textSize="48px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/bgTopView"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/bgTopView" />

    <!--  通用设置项  -->

    <androidx.constraintlayout.helper.widget.Flow
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="60px"
        android:layout_marginTop="55px"
        android:orientation="vertical"
        app:constraint_referenced_ids="labelMultipleDisplayTv,labelHdmi,labelDeviceNameWindowTv,labelSmartFullTv,labelAskBeforeCastingTv"
        app:flow_horizontalAlign="start"
        app:flow_verticalGap="45px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/bgTopView" />

    <TextView
        android:id="@+id/labelMultipleDisplayTv"
        style="@style/tv_setting_label"
        android:text="@string/item_setting_multiple_display" />


    <ImageView
        android:id="@+id/multipleDisplayPreIv"
        android:layout_width="30px"
        android:layout_height="30px"
        android:layout_marginRight="75px"
        android:src="@drawable/ic_setting_decrease"
        app:layout_constraintBottom_toBottomOf="@id/multipleDisplayNextIv"
        app:layout_constraintRight_toLeftOf="@id/multipleDisplayNextIv"
        app:layout_constraintTop_toTopOf="@id/multipleDisplayNextIv" />

    <TextView
        android:id="@+id/multipleDisplayContentTv"
        style="@style/tv_setting_content"
        app:layout_constraintBottom_toBottomOf="@id/multipleDisplayPreIv"
        app:layout_constraintLeft_toRightOf="@id/multipleDisplayPreIv"
        app:layout_constraintRight_toLeftOf="@id/multipleDisplayNextIv"
        app:layout_constraintTop_toTopOf="@id/multipleDisplayPreIv"
        tools:text="4" />


    <ImageView
        android:id="@+id/multipleDisplayNextIv"
        android:layout_width="30px"
        android:layout_height="30px"
        android:layout_marginRight="60px"
        android:src="@drawable/ic_setting_increase"
        app:layout_constraintBottom_toBottomOf="@id/labelMultipleDisplayTv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/labelMultipleDisplayTv" />

    <TextView
        android:id="@+id/labelHdmi"
        style="@style/tv_setting_label"
        android:text="@string/str_title_hdmi" />

    <com.czur.starry.device.baselib.widget.toggle.SwitchIcon
        android:id="@+id/hdmiSwitch"
        android:layout_width="94px"
        android:layout_height="44px"
        app:layout_constraintBottom_toBottomOf="@id/labelHdmi"
        app:layout_constraintRight_toRightOf="@id/multipleDisplayNextIv"
        app:layout_constraintTop_toTopOf="@id/labelHdmi" />

    <TextView
        android:id="@+id/labelDeviceNameWindowTv"
        style="@style/tv_setting_label"
        android:text="@string/item_setting_device_name_float_window" />

    <com.czur.starry.device.baselib.widget.toggle.SwitchIcon
        android:id="@+id/deviceNameWindowSwitch"
        android:layout_width="94px"
        android:layout_height="44px"
        app:layout_constraintBottom_toBottomOf="@id/labelDeviceNameWindowTv"
        app:layout_constraintRight_toRightOf="@id/multipleDisplayNextIv"
        app:layout_constraintTop_toTopOf="@id/labelDeviceNameWindowTv" />

    <TextView
        android:id="@+id/labelSmartFullTv"
        style="@style/tv_setting_label"
        android:text="@string/item_setting_smart_full_screen" />

    <com.czur.starry.device.baselib.widget.toggle.SwitchIcon
        android:id="@+id/smartFullSwitch"
        android:layout_width="94px"
        android:layout_height="44px"
        app:layout_constraintBottom_toBottomOf="@id/labelSmartFullTv"
        app:layout_constraintRight_toRightOf="@id/multipleDisplayNextIv"
        app:layout_constraintTop_toTopOf="@id/labelSmartFullTv" />

    <TextView
        android:id="@+id/labelAskBeforeCastingTv"
        style="@style/tv_setting_label"
        android:text="@string/item_setting_ask_before_casting" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:alpha="0.5"
        android:text="@string/str_ask_before_casting_hint"
        android:textColor="@color/white"
        android:textSize="20px"
        app:layout_constraintLeft_toLeftOf="@id/labelAskBeforeCastingTv"
        app:layout_constraintTop_toBottomOf="@id/labelAskBeforeCastingTv" />

    <com.czur.starry.device.baselib.widget.toggle.SwitchIcon
        android:id="@+id/askBeforeCastingSwitch"
        android:layout_width="94px"
        android:layout_height="44px"
        app:layout_constraintBottom_toBottomOf="@id/labelAskBeforeCastingTv"
        app:layout_constraintRight_toRightOf="@id/multipleDisplayNextIv"
        app:layout_constraintTop_toTopOf="@id/labelAskBeforeCastingTv" />

    <!--  分割线  -->
    <View
        android:id="@+id/settingDividingLine"
        android:layout_width="0px"
        android:layout_height="1px"
        android:layout_marginTop="60px"
        android:alpha="0.3"
        android:background="@color/white"
        app:layout_constraintLeft_toLeftOf="@id/labelMultipleDisplayTv"
        app:layout_constraintRight_toRightOf="@id/multipleDisplayNextIv"
        app:layout_constraintTop_toBottomOf="@id/askBeforeCastingSwitch" />

    <!--  Miracast AirPlay DLNA  -->

    <androidx.constraintlayout.helper.widget.Flow
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="60px"
        android:layout_marginTop="40px"
        android:orientation="vertical"
        app:constraint_referenced_ids="labelChromeCast,labelMiracast,labelAirPlay,labelDLNA,bottomSpace"
        app:flow_horizontalAlign="start"
        app:flow_verticalGap="45px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/settingDividingLine" />

    <TextView
        android:id="@+id/labelChromeCast"
        style="@style/tv_setting_label"
        android:text="@string/item_setting_chromecast" />

    <com.czur.starry.device.baselib.widget.toggle.SwitchIcon
        android:id="@+id/chromecastSwitch"
        android:layout_width="94px"
        android:layout_height="44px"
        app:layout_constraintBottom_toBottomOf="@id/labelChromeCast"
        app:layout_constraintRight_toRightOf="@id/multipleDisplayNextIv"
        app:layout_constraintTop_toTopOf="@id/labelChromeCast" />

    <TextView
        android:id="@+id/labelMiracast"
        style="@style/tv_setting_label"
        android:text="@string/item_setting_miracast" />

    <TextView
        android:id="@+id/miracastDisableHintTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:alpha="0.5"
        android:text="@string/str_miracast_disable_hint"
        android:textColor="@color/white"
        android:textSize="20px"
        app:layout_constraintLeft_toLeftOf="@id/labelMiracast"
        app:layout_constraintTop_toBottomOf="@id/labelMiracast" />

    <com.czur.starry.device.baselib.widget.toggle.SwitchIcon
        android:id="@+id/miracastSwitch"
        android:layout_width="94px"
        android:layout_height="44px"
        app:layout_constraintBottom_toBottomOf="@id/labelMiracast"
        app:layout_constraintRight_toRightOf="@id/multipleDisplayNextIv"
        app:layout_constraintTop_toTopOf="@id/labelMiracast" />

    <TextView
        android:id="@+id/labelAirPlay"
        style="@style/tv_setting_label"
        android:text="@string/item_setting_airplay" />

    <com.czur.starry.device.baselib.widget.toggle.SwitchIcon
        android:id="@+id/airPlaySwitch"
        android:layout_width="94px"
        android:layout_height="44px"
        app:layout_constraintBottom_toBottomOf="@id/labelAirPlay"
        app:layout_constraintRight_toRightOf="@id/multipleDisplayNextIv"
        app:layout_constraintTop_toTopOf="@id/labelAirPlay" />

    <TextView
        android:id="@+id/labelDLNA"
        style="@style/tv_setting_label"
        android:text="@string/item_setting_dlna" />

    <com.czur.starry.device.baselib.widget.toggle.SwitchIcon
        android:id="@+id/dlnaSwitch"
        android:layout_width="94px"
        android:layout_height="44px"
        app:layout_constraintBottom_toBottomOf="@id/labelDLNA"
        app:layout_constraintRight_toRightOf="@id/multipleDisplayNextIv"
        app:layout_constraintTop_toTopOf="@id/labelDLNA" />

    <Space
        android:id="@+id/bottomSpace"
        android:layout_width="0px"
        android:layout_height="0px"/>
</androidx.constraintlayout.widget.ConstraintLayout>