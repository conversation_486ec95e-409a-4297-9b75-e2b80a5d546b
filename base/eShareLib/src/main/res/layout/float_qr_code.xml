<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="800px"
    android:layout_height="800px"
    tools:ignore="PxUsage,RtlHardcoded">

    <!--Dialog 的背景-->
    <View
        android:id="@+id/bgTopView"
        android:layout_width="match_parent"
        android:layout_height="100px"
        app:bl_corners_topLeftRadius="10px"
        app:bl_corners_topRightRadius="10px"
        app:bl_solid_color="#4f6ce3"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="#4f6ce3" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0px"
        app:bl_corners_bottomLeftRadius="10px"
        app:bl_corners_bottomRightRadius="10px"
        app:bl_solid_color="@color/dialog_bg_color"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/bgTopView"
        tools:background="@color/dialog_bg_color" />

    <ImageView
        android:id="@+id/closeBtn"
        android:layout_width="60px"
        android:layout_height="60px"
        android:layout_marginRight="20px"
        android:src="@drawable/ic_dialog_close"
        app:float_tips="@string/float_tip_close"
        app:layout_constraintBottom_toBottomOf="@id/bgTopView"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/bgTopView" />


    <TextView
        android:id="@+id/qrCodeTitleTv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="58px"
        android:layout_marginRight="20px"
        android:ellipsize="none"
        android:includeFontPadding="false"
        android:minWidth="642px"
        android:paddingTop="1px"
        android:text="@string/str_qrcode_float_title"
        android:textColor="@color/white"
        android:textSize="48px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/bgTopView"
        app:layout_constraintLeft_toLeftOf="@id/bgTopView"
        app:layout_constraintRight_toLeftOf="@+id/closeBtn"
        app:layout_constraintTop_toTopOf="@id/bgTopView" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/qrCodeIv"
        android:layout_width="400px"
        android:layout_height="400px"
        android:background="@color/white"
        android:padding="15px"
        android:scaleType="fitXY"
        app:layout_constraintBottom_toTopOf="@id/qrCodeGuideTv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/bgTopView"
        app:layout_constraintVertical_chainStyle="packed"
        app:round="10px" />

    <TextView
        android:id="@+id/qrCodeGuideTv"
        android:layout_width="500px"
        android:layout_height="wrap_content"
        android:layout_marginTop="51px"
        android:gravity="center"
        android:lineSpacingMultiplier="1.2"
        android:text="@string/str_qr_code_guide"
        android:textColor="@color/white"
        android:textSize="28px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/qrCodeIv" />


</androidx.constraintlayout.widget.ConstraintLayout>