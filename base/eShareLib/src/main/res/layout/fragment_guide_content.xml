<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/base_bg_color"
    tools:ignore="PxUsage,RtlHardcoded">


    <!--  引导视频  -->
    <com.czur.starry.device.sharescreen.esharelib.widget.GuideImageView
        android:id="@+id/guideVideoView"
        android:layout_width="360px"
        android:layout_height="250px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/guideVideoLl"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/guideVideoIv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"/>

            <TextView
                android:id="@+id/guideVideoTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingTop="5px"
                android:paddingLeft="5px"
                android:paddingRight="5px"
                android:gravity="center"
                android:textStyle="bold"
                android:textColor="@color/bg_main_blue"
                android:textSize="16px"/>
        </LinearLayout>
    </com.czur.starry.device.sharescreen.esharelib.widget.GuideImageView>


    <ImageView
        android:id="@+id/subTitleIconIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="13px"
        android:layout_marginTop="28px"
        app:layout_constraintTop_toBottomOf="@id/guideVideoView"
        app:layout_constraintLeft_toLeftOf="@id/guideVideoView"
        tools:src="@drawable/logo_harmony" />

    <TextView
        android:id="@+id/subTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15px"
        android:textColor="@color/text_common"
        android:textSize="20px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/subTitleIconIv"
        app:layout_constraintLeft_toRightOf="@id/subTitleIconIv"
        app:layout_constraintTop_toTopOf="@id/subTitleIconIv"
        tools:text="@string/str_guide_sub_title_harmony" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/subTitleGroup"
        android:layout_width="0px"
        android:layout_height="0px"
        app:constraint_referenced_ids="subTitleIconIv,subTitleTv" />


    <!--  Guide文字  -->
    <androidx.constraintlayout.helper.widget.Flow
        android:id="@+id/guideFlow"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginLeft="91px"
        android:orientation="vertical"
        app:constraint_referenced_ids="guideTv1,guideTv2,guideTv3,guideTv4"
        app:flow_horizontalAlign="start"
        app:flow_verticalGap="32px"
        app:layout_constraintLeft_toRightOf="@id/guideVideoView"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/guideVideoView" />

    <TextView
        android:id="@+id/guideTv1"
        style="@style/tv_guide_item"
        tools:text="AAAAA" />

    <TextView
        android:id="@+id/guideTv2"
        style="@style/tv_guide_item"
        tools:text="AAAAA" />

    <TextView
        android:id="@+id/guideTv3"
        style="@style/tv_guide_item"
        tools:text="AAAAA" />

    <TextView
        android:id="@+id/guideTv4"
        style="@style/tv_guide_item"
        tools:text="AAAAA" />

    <ImageView
        android:id="@+id/guideIv1"
        style="@style/iv_guide_item"
        android:src="@drawable/guide_item_1"
        app:layout_constraintRight_toLeftOf="@id/guideTv1"
        app:layout_constraintTop_toTopOf="@id/guideTv1" />

    <ImageView
        android:id="@+id/guideIv2"
        style="@style/iv_guide_item"
        android:src="@drawable/guide_item_2"
        app:layout_constraintRight_toLeftOf="@id/guideTv2"
        app:layout_constraintTop_toTopOf="@id/guideTv2" />

    <ImageView
        android:id="@+id/guideIv3"
        style="@style/iv_guide_item"
        android:src="@drawable/guide_item_3"
        app:layout_constraintRight_toLeftOf="@id/guideTv3"
        app:layout_constraintTop_toTopOf="@id/guideTv3" />

    <ImageView
        android:id="@+id/guideIv4"
        style="@style/iv_guide_item"
        android:src="@drawable/guide_item_4"
        app:layout_constraintRight_toLeftOf="@id/guideTv4"
        app:layout_constraintTop_toTopOf="@id/guideTv4" />

    <!--  推荐  -->
    <TextView
        android:id="@+id/guideRecommendTv"
        style="@style/tv_guide_item_recommend"
        android:textStyle="bold"
        android:layout_marginTop="56px"
        android:text="@string/str_guide_recommend"
        app:layout_constraintTop_toBottomOf="@id/guideFlow"
        app:layout_constraintLeft_toLeftOf="@id/guideIv1"/>

    <TextView
        android:id="@+id/guideRecommendContentTv"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:maxWidth="600px"
        android:layout_marginRight="8px"
        android:textColor="@color/text_common"
        android:textSize="20px"
        android:textStyle="bold"
        android:lineSpacingExtra="5px"
        app:layout_constraintLeft_toLeftOf="@id/guideRecommendTv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/guideRecommendTv"
        tools:text="@string/str_guide_remark_czur_share" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/recommendGroup"
        android:layout_width="0px"
        android:layout_height="0px"
        app:constraint_referenced_ids="guideRecommendTv,guideRecommendContentTv" />

    <!--  备注  -->
    <TextView
        android:id="@+id/remarkLabelTv"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:text="@string/str_guide_remark_label"
        android:textColor="@color/text_common"
        android:textSize="20px"
        android:textStyle="bold"
        android:layout_marginTop="56px"
        app:layout_constraintTop_toBottomOf="@id/guideFlow"
        app:layout_constraintLeft_toLeftOf="@id/guideIv1"/>

    <TextView
        android:id="@+id/remarkContentTv"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:maxWidth="550px"
        android:layout_marginRight="50px"
        android:textColor="@color/text_common"
        android:textSize="20px"
        android:textStyle="bold"
        android:lineSpacingExtra="5px"
        app:layout_constraintLeft_toRightOf="@id/remarkLabelTv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/remarkLabelTv"
        tools:text="@string/str_guide_remark_czur_share" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/remarkGroup"
        android:layout_width="0px"
        android:layout_height="0px"
        app:constraint_referenced_ids="remarkLabelTv,remarkContentTv" />
    <!--扩展信息-->

    <ImageView
        android:id="@+id/extraIv"
        android:layout_width="152px"
        android:layout_height="152px"
        android:layout_marginRight="30px"
        android:layout_marginBottom="30px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/extraTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4px"
        android:textColor="@color/text_common"
        android:textSize="16px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="@id/extraIv"
        app:layout_constraintRight_toRightOf="@id/extraIv"
        app:layout_constraintTop_toBottomOf="@id/extraIv" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/extraGroup"
        android:layout_width="0px"
        android:layout_height="0px"
        app:constraint_referenced_ids="extraIv,extraTv" />
</androidx.constraintlayout.widget.ConstraintLayout>