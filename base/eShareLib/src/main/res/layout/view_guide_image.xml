<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/guideImageFl"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:bl_corners_radius="10px"
    app:bl_solid_color="#ECEFF6"
    app:bl_stroke_color="@color/white"
    app:bl_stroke_width="1px"
    tools:ignore="PxUsage,RtlHardcoded">

    <LinearLayout
        android:id="@+id/videoDurationLl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="left|bottom"
        android:layout_margin="10px"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:visibility="gone">

        <ImageView
            android:id="@+id/videoPlayIv"
            android:layout_width="32px"
            android:layout_height="32px"
            android:src="@drawable/ic_play" />

        <TextView
            android:id="@+id/videoDurationTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/text_common"
            android:textSize="20px"
            tools:text="23S"
            android:layout_marginLeft="8px"/>
    </LinearLayout>

    <ImageView
        android:id="@+id/guideMaskIv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="1px"
        android:src="@drawable/img_guide_image_view_mask"
        android:visibility="gone" />
</FrameLayout>