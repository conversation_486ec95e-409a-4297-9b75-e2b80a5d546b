<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="20px"
    tools:background="@color/base_bg_color"
    tools:ignore="PxUsage,RtlHardcoded">

    <View
        android:id="@+id/guideCategorySelView"
        android:layout_width="280px"
        android:layout_height="1px"
        android:layout_marginLeft="30px"
        android:alpha="0.3"
        android:background="@color/text_common"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>