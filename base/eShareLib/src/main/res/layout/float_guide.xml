<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="1800px"
    android:layout_height="1000px"
    tools:ignore="PxUsage">

    <!--Dialog 的背景-->
    <View
        android:id="@+id/bgTopView"
        android:layout_width="match_parent"
        android:layout_height="100px"
        app:bl_corners_topLeftRadius="10px"
        app:bl_corners_topRightRadius="10px"
        app:bl_solid_color="#4f6ce3"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="#4f6ce3" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0px"
        app:bl_corners_bottomLeftRadius="10px"
        app:bl_corners_bottomRightRadius="10px"
        app:bl_solid_color="#5879FC"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/bgTopView"
        tools:background="#5879FC" />

    <ImageView
        android:id="@+id/closeBtn"
        android:layout_width="60px"
        android:layout_height="60px"
        android:layout_marginRight="20px"
        android:src="@drawable/ic_share_dialog_close"
        app:float_tips="@string/float_tip_close"
        app:layout_constraintBottom_toBottomOf="@id/bgTopView"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/bgTopView" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="58px"
        android:includeFontPadding="false"
        android:paddingTop="1px"
        android:text="@string/str_guide_float_title"
        android:textColor="@color/white"
        android:textSize="@dimen/guideFloatTitleSize"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/bgTopView"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/bgTopView" />

    <View
        android:id="@+id/line"
        android:layout_width="1px"
        android:layout_height="0px"
        android:layout_marginVertical="75px"
        android:layout_marginLeft="478px"
        android:background="#D8D8D8"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/bgTopView" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/guideCategoryRv"
        android:layout_width="0px"
        android:layout_height="0px"
        android:layout_marginTop="50px"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/line"
        app:layout_constraintTop_toBottomOf="@id/bgTopView" />

    <FrameLayout
        android:id="@+id/contentFl"
        android:layout_width="0px"
        android:layout_height="0px"
        app:layout_constraintLeft_toRightOf="@id/line"
        app:layout_constraintTop_toBottomOf="@id/bgTopView"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"/>

    <!--    <View-->
    <!--        android:id="@+id/line1"-->
    <!--        android:layout_width="1px"-->
    <!--        android:layout_height="0px"-->
    <!--        android:background="@color/white" />-->

    <!--    <View-->
    <!--        android:id="@+id/line2"-->
    <!--        android:layout_width="1px"-->
    <!--        android:layout_height="0px"-->
    <!--        android:background="@color/white" />-->

    <!--    <View-->
    <!--        android:id="@+id/line3"-->
    <!--        android:layout_width="1px"-->
    <!--        android:layout_height="0px"-->
    <!--        android:background="@color/white" />-->


    <!--    <androidx.constraintlayout.helper.widget.Flow-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="751px"-->
    <!--        android:layout_marginBottom="74px"-->
    <!--        app:constraint_referenced_ids="line1,line2,line3"-->
    <!--        app:flow_horizontalStyle="spread"-->
    <!--        app:layout_constraintBottom_toBottomOf="parent" />-->


    <!--    <ImageView-->
    <!--        android:id="@+id/logoAppleIv"-->
    <!--        style="@style/iv_guide_item_logo"-->
    <!--        android:layout_marginTop="80px"-->
    <!--        android:src="@drawable/logo_apple"-->
    <!--        app:layout_constraintLeft_toLeftOf="parent"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/bgTopView" />-->

    <!--    <TextView-->
    <!--        style="@style/tv_guide_item_title"-->
    <!--        android:text="@string/str_guide_category_apple"-->
    <!--        app:layout_constraintBottom_toBottomOf="@id/logoAppleIv"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/logoAppleIv"-->
    <!--        app:layout_constraintTop_toTopOf="@id/logoAppleIv" />-->

    <!--    <ImageView-->
    <!--        android:id="@+id/logoAndroidIv"-->
    <!--        style="@style/iv_guide_item_logo"-->
    <!--        android:src="@drawable/logo_android"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/line1"-->
    <!--        app:layout_constraintTop_toTopOf="@id/logoAppleIv" />-->

    <!--    <TextView-->
    <!--        style="@style/tv_guide_item_title"-->
    <!--        android:text="@string/str_guide_category_android"-->
    <!--        app:layout_constraintBottom_toBottomOf="@id/logoAndroidIv"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/logoAndroidIv"-->
    <!--        app:layout_constraintTop_toTopOf="@id/logoAndroidIv" />-->

    <!--    <ImageView-->
    <!--        android:id="@+id/logoHarmonyIv"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layout_marginTop="20px"-->
    <!--        android:src="@drawable/logo_harmony"-->
    <!--        app:layout_constraintLeft_toLeftOf="@id/logoAndroidIv"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/logoAndroidIv" />-->

    <!--    <TextView-->
    <!--        style="@style/tv_guide_item_title"-->
    <!--        android:layout_marginLeft="15px"-->
    <!--        android:layout_marginBottom="10px"-->
    <!--        android:text="@string/str_guide_sub_title_harmony"-->
    <!--        app:layout_constraintBottom_toBottomOf="@id/logoHarmonyIv"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/logoHarmonyIv"-->
    <!--        app:layout_constraintTop_toTopOf="@id/logoHarmonyIv" />-->

    <!--    <ImageView-->
    <!--        android:id="@+id/logoWindowsIv"-->
    <!--        style="@style/iv_guide_item_logo"-->
    <!--        android:src="@drawable/logo_windows"-->
    <!--        app:layout_constraintBottom_toBottomOf="@id/logoAppleIv"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/line2"-->
    <!--        app:layout_constraintTop_toTopOf="@id/logoAppleIv" />-->

    <!--    <TextView-->
    <!--        style="@style/tv_guide_item_title"-->
    <!--        android:text="@string/str_guide_category_windows"-->
    <!--        app:layout_constraintBottom_toBottomOf="@id/logoWindowsIv"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/logoWindowsIv"-->
    <!--        app:layout_constraintTop_toTopOf="@id/logoWindowsIv" />-->

    <!--    <TextView-->
    <!--        android:id="@+id/titleClickDropTv"-->
    <!--        style="@style/tv_guide_item_title"-->
    <!--        android:layout_marginLeft="44px"-->
    <!--        android:text="@string/str_guide_item_click_drop_title"-->
    <!--        app:layout_constraintBottom_toBottomOf="@id/logoAppleIv"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/line3"-->
    <!--        app:layout_constraintTop_toTopOf="@id/logoAppleIv" />-->

    <!--    <TextView-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layout_marginLeft="5px"-->
    <!--        android:text="@string/str_guide_item_click_drop_title_slogan"-->
    <!--        android:textColor="@color/white"-->
    <!--        android:textSize="48px"-->
    <!--        android:textStyle="bold"-->
    <!--        app:layout_constraintBottom_toBottomOf="@id/titleClickDropTv"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/titleClickDropTv"-->
    <!--        app:layout_constraintTop_toTopOf="@id/titleClickDropTv" />-->

    <!--    <com.czur.starry.device.sharescreen.esharelib.widget.GuideImageView-->
    <!--        android:id="@+id/appleGuideImageView"-->
    <!--        android:layout_width="360px"-->
    <!--        android:layout_height="250px"-->
    <!--        android:layout_marginTop="190px"-->
    <!--        app:layout_constraintLeft_toLeftOf="parent"-->
    <!--        app:layout_constraintRight_toLeftOf="@id/line1"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/bgTopView">-->

    <!--        <ImageView-->
    <!--            android:layout_width="wrap_content"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:layout_gravity="center"-->
    <!--            android:src="@drawable/img_apple_device" />-->

    <!--    </com.czur.starry.device.sharescreen.esharelib.widget.GuideImageView>-->

    <!--    <com.czur.starry.device.sharescreen.esharelib.widget.GuideImageView-->
    <!--        android:id="@+id/androidGuideImageView"-->
    <!--        android:layout_width="360px"-->
    <!--        android:layout_height="250px"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/line1"-->
    <!--        app:layout_constraintRight_toLeftOf="@id/line2"-->
    <!--        app:layout_constraintTop_toTopOf="@id/appleGuideImageView">-->

    <!--        <LinearLayout-->
    <!--            android:layout_width="wrap_content"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:layout_gravity="center"-->
    <!--            android:gravity="center_horizontal"-->
    <!--            android:orientation="vertical">-->

    <!--            <ImageView-->
    <!--                android:id="@+id/downloadQrIv"-->
    <!--                android:layout_width="wrap_content"-->
    <!--                android:layout_height="wrap_content"-->
    <!--                android:src="@drawable/img_android_download_qr" />-->

    <!--            <TextView-->
    <!--                android:id="@+id/guideAndroidDownloadTv"-->
    <!--                android:layout_width="wrap_content"-->
    <!--                android:layout_height="wrap_content"-->
    <!--                android:layout_marginTop="10px"-->
    <!--                android:text="@string/str_guide_android_download"-->
    <!--                android:textColor="@color/white"-->
    <!--                android:textSize="16px"-->
    <!--                android:textStyle="bold" />-->
    <!--        </LinearLayout>-->
    <!--    </com.czur.starry.device.sharescreen.esharelib.widget.GuideImageView>-->


    <!--    <com.czur.starry.device.sharescreen.esharelib.widget.GuideImageView-->
    <!--        android:id="@+id/windowsGuideImageView"-->
    <!--        android:layout_width="360px"-->
    <!--        android:layout_height="250px"-->
    <!--        app:layout_constraintBottom_toBottomOf="@id/appleGuideImageView"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/line2"-->
    <!--        app:layout_constraintRight_toRightOf="@id/line3"-->
    <!--        app:layout_constraintTop_toTopOf="@id/appleGuideImageView">-->

    <!--        <ImageView-->
    <!--            android:layout_width="wrap_content"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:layout_gravity="center"-->
    <!--            android:src="@drawable/img_device_pc" />-->
    <!--    </com.czur.starry.device.sharescreen.esharelib.widget.GuideImageView>-->

    <!--    <com.czur.starry.device.sharescreen.esharelib.widget.GuideImageView-->
    <!--        android:id="@+id/clickDropGuideImageView"-->
    <!--        android:layout_width="360px"-->
    <!--        android:layout_height="250px"-->
    <!--        app:layout_constraintBottom_toBottomOf="@id/appleGuideImageView"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/line3"-->
    <!--        app:layout_constraintRight_toRightOf="parent"-->
    <!--        app:layout_constraintTop_toTopOf="@id/appleGuideImageView">-->

    <!--        <ImageView-->
    <!--            android:id="@+id/clickDropIv"-->
    <!--            android:layout_width="wrap_content"-->
    <!--            android:layout_height="wrap_content"-->
    <!--            android:layout_gravity="right"-->
    <!--            android:layout_marginTop="20px"-->
    <!--            android:layout_marginRight="20px"-->
    <!--            android:src="@drawable/img_click_drop" />-->
    <!--    </com.czur.starry.device.sharescreen.esharelib.widget.GuideImageView>-->


    <!--    &lt;!&ndash;  以下是文言  &ndash;&gt;-->


    <!--    <ImageView-->
    <!--        android:id="@+id/appleItem1Iv"-->
    <!--        style="@style/iv_guide_item"-->
    <!--        android:layout_marginTop="470px"-->
    <!--        android:src="@drawable/guide_item_1"-->
    <!--        app:layout_constraintHorizontal_chainStyle="packed"-->
    <!--        app:layout_constraintLeft_toLeftOf="parent"-->
    <!--        app:layout_constraintRight_toLeftOf="@id/appleItem1Tv"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/bgTopView" />-->

    <!--    <TextView-->
    <!--        android:id="@+id/appleItem1Tv"-->
    <!--        style="@style/tv_guide_item"-->
    <!--        android:text="@string/str_guide_apple_1"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/appleItem1Iv"-->
    <!--        app:layout_constraintRight_toLeftOf="@id/line1"-->
    <!--        app:layout_constraintTop_toTopOf="@id/appleItem1Iv" />-->


    <!--    <ImageView-->
    <!--        android:id="@+id/appleItem2Iv"-->
    <!--        style="@style/iv_guide_item_other"-->
    <!--        android:src="@drawable/guide_item_2"-->
    <!--        app:layout_constraintHorizontal_chainStyle="packed"-->
    <!--        app:layout_constraintLeft_toLeftOf="parent"-->
    <!--        app:layout_constraintRight_toLeftOf="@id/appleItem2Tv"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/appleItem1Tv" />-->

    <!--    <TextView-->
    <!--        android:id="@+id/appleItem2Tv"-->
    <!--        style="@style/tv_guide_item"-->
    <!--        android:text="@string/str_guide_apple_2"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/appleItem2Iv"-->
    <!--        app:layout_constraintRight_toLeftOf="@id/line1"-->
    <!--        app:layout_constraintTop_toTopOf="@id/appleItem2Iv" />-->

    <!--    <ImageView-->
    <!--        android:id="@+id/appleItem3Iv"-->
    <!--        style="@style/iv_guide_item_other"-->
    <!--        android:src="@drawable/guide_item_3"-->
    <!--        app:layout_constraintHorizontal_chainStyle="packed"-->
    <!--        app:layout_constraintLeft_toLeftOf="parent"-->
    <!--        app:layout_constraintRight_toLeftOf="@id/appleItem3Tv"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/appleItem2Tv" />-->

    <!--    <TextView-->
    <!--        android:id="@+id/appleItem3Tv"-->
    <!--        style="@style/tv_guide_item"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/appleItem3Iv"-->
    <!--        app:layout_constraintRight_toLeftOf="@id/line1"-->
    <!--        app:layout_constraintTop_toTopOf="@id/appleItem3Iv"-->
    <!--        tools:text="选择Starry,开始投屏" />-->

    <!--    <ImageView-->
    <!--        android:id="@+id/androidItem1Iv"-->
    <!--        style="@style/iv_guide_item"-->
    <!--        android:src="@drawable/guide_item_1"-->
    <!--        app:layout_constraintHorizontal_chainStyle="packed"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/line1"-->
    <!--        app:layout_constraintRight_toLeftOf="@id/androidItem1Tv"-->
    <!--        app:layout_constraintTop_toTopOf="@id/appleItem1Iv" />-->

    <!--    <TextView-->
    <!--        android:id="@+id/androidItem1Tv"-->
    <!--        style="@style/tv_guide_item"-->
    <!--        android:text="@string/str_guide_android_1"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/androidItem1Iv"-->
    <!--        app:layout_constraintRight_toLeftOf="@id/line2"-->
    <!--        app:layout_constraintTop_toTopOf="@id/androidItem1Iv" />-->

    <!--    <ImageView-->
    <!--        android:id="@+id/androidItem2Iv"-->
    <!--        style="@style/iv_guide_item_other"-->
    <!--        android:src="@drawable/guide_item_2"-->
    <!--        app:layout_constraintHorizontal_chainStyle="packed"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/line1"-->
    <!--        app:layout_constraintRight_toLeftOf="@id/androidItem2Tv"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/androidItem1Tv" />-->

    <!--    <TextView-->
    <!--        android:id="@+id/androidItem2Tv"-->
    <!--        style="@style/tv_guide_item"-->
    <!--        android:text="@string/str_guide_android_2"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/androidItem2Iv"-->
    <!--        app:layout_constraintRight_toLeftOf="@id/line2"-->
    <!--        app:layout_constraintTop_toTopOf="@id/androidItem2Iv" />-->

    <!--    <ImageView-->
    <!--        android:id="@+id/androidItem3Iv"-->
    <!--        style="@style/iv_guide_item_other"-->
    <!--        android:src="@drawable/guide_item_3"-->
    <!--        app:layout_constraintHorizontal_chainStyle="packed"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/line1"-->
    <!--        app:layout_constraintRight_toLeftOf="@id/androidItem3Tv"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/androidItem2Tv" />-->

    <!--    <TextView-->
    <!--        android:id="@+id/androidItem3Tv"-->
    <!--        style="@style/tv_guide_item"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/androidItem3Iv"-->
    <!--        app:layout_constraintRight_toLeftOf="@id/line2"-->
    <!--        app:layout_constraintTop_toTopOf="@id/androidItem3Iv"-->
    <!--        tools:text="使用“无线投屏”功能，选择 StarryHub ，开始投屏" />-->

    <!--    <ImageView-->
    <!--        android:id="@+id/pcItem1Iv"-->
    <!--        style="@style/iv_guide_item"-->
    <!--        android:src="@drawable/guide_item_1"-->
    <!--        app:layout_constraintHorizontal_chainStyle="packed"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/line2"-->
    <!--        app:layout_constraintRight_toLeftOf="@id/pcItem1Tv"-->
    <!--        app:layout_constraintTop_toTopOf="@id/appleItem1Iv" />-->

    <!--    <TextView-->
    <!--        android:id="@+id/pcItem1Tv"-->
    <!--        style="@style/tv_guide_item"-->
    <!--        android:text="@string/str_guide_pc_1"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/pcItem1Iv"-->
    <!--        app:layout_constraintRight_toLeftOf="@id/line3"-->
    <!--        app:layout_constraintTop_toTopOf="@id/pcItem1Iv" />-->

    <!--    <ImageView-->
    <!--        android:id="@+id/pcItem2Iv"-->
    <!--        style="@style/iv_guide_item_other"-->
    <!--        android:src="@drawable/guide_item_2"-->
    <!--        app:layout_constraintHorizontal_chainStyle="packed"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/line2"-->
    <!--        app:layout_constraintRight_toLeftOf="@id/pcItem2Tv"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/pcItem1Tv" />-->

    <!--    <TextView-->
    <!--        android:id="@+id/pcItem2Tv"-->
    <!--        style="@style/tv_guide_item"-->
    <!--        android:text="@string/str_guide_pc_2"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/pcItem2Iv"-->
    <!--        app:layout_constraintRight_toLeftOf="@id/line3"-->
    <!--        app:layout_constraintTop_toTopOf="@id/pcItem2Iv" />-->


    <!--    <ImageView-->
    <!--        android:id="@+id/pcItem3Iv"-->
    <!--        style="@style/iv_guide_item_other"-->
    <!--        android:layout_marginTop="40px"-->
    <!--        android:src="@drawable/guide_item_3"-->
    <!--        app:layout_constraintHorizontal_chainStyle="packed"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/line2"-->
    <!--        app:layout_constraintRight_toLeftOf="@id/pcItem3Tv"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/pcItem2Tv" />-->

    <!--    <TextView-->
    <!--        android:id="@+id/pcItem3Tv"-->
    <!--        style="@style/tv_guide_item"-->
    <!--        android:autoLink="none"-->
    <!--        android:text="@string/str_guide_pc_3"-->
    <!--        android:lineSpacingExtra="4px"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/pcItem3Iv"-->
    <!--        app:layout_constraintRight_toLeftOf="@id/line3"-->
    <!--        app:layout_constraintTop_toTopOf="@id/pcItem3Iv" />-->

    <!--    <TextView-->
    <!--        android:layout_width="316px"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:textColor="@color/white"-->
    <!--        android:textSize="16px"-->
    <!--        android:layout_marginLeft="0dp"-->
    <!--        android:gravity="left"-->
    <!--        android:autoLink="all"-->
    <!--        android:text="@string/str_guide_pc_recommend"-->
    <!--        app:layout_constraintBottom_toTopOf="@id/pcItem3Tv"-->
    <!--        app:layout_constraintLeft_toLeftOf="@id/pcItem3Tv"-->
    <!--        app:layout_constraintRight_toLeftOf="@id/line3"/>-->

    <!--    <ImageView-->
    <!--        android:id="@+id/clickDropItem1Iv"-->
    <!--        style="@style/iv_guide_item"-->
    <!--        android:src="@drawable/guide_item_1"-->
    <!--        app:layout_constraintHorizontal_chainStyle="packed"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/line3"-->
    <!--        app:layout_constraintRight_toLeftOf="@id/clickDropItem1Tv"-->
    <!--        app:layout_constraintTop_toTopOf="@id/appleItem1Iv" />-->

    <!--    <TextView-->
    <!--        android:id="@+id/clickDropItem1Tv"-->
    <!--        style="@style/tv_guide_item"-->
    <!--        android:text="@string/str_guide_click_drop_1"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/clickDropItem1Iv"-->
    <!--        app:layout_constraintRight_toRightOf="parent"-->
    <!--        app:layout_constraintTop_toTopOf="@id/clickDropItem1Iv" />-->

    <!--    <ImageView-->
    <!--        android:id="@+id/clickDropItem2Iv"-->
    <!--        style="@style/iv_guide_item_other"-->
    <!--        android:src="@drawable/guide_item_2"-->
    <!--        app:layout_constraintHorizontal_chainStyle="packed"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/line3"-->
    <!--        app:layout_constraintRight_toLeftOf="@id/clickDropItem2Tv"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/clickDropItem1Tv" />-->

    <!--    <TextView-->
    <!--        android:id="@+id/clickDropItem2Tv"-->
    <!--        style="@style/tv_guide_item"-->
    <!--        android:text="@string/str_guide_click_drop_2"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/clickDropItem2Iv"-->
    <!--        app:layout_constraintRight_toRightOf="parent"-->
    <!--        app:layout_constraintTop_toTopOf="@id/clickDropItem2Iv" />-->

    <!--    <ImageView-->
    <!--        android:id="@+id/clipDropItem3Iv"-->
    <!--        style="@style/iv_guide_item_other"-->
    <!--        android:src="@drawable/guide_item_3"-->
    <!--        app:layout_constraintHorizontal_chainStyle="packed"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/line3"-->
    <!--        app:layout_constraintRight_toLeftOf="@id/clipDropItem3Tv"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/clickDropItem2Tv" />-->

    <!--    <TextView-->
    <!--        android:id="@+id/clipDropItem3Tv"-->
    <!--        style="@style/tv_guide_item"-->
    <!--        android:text="@string/str_guide_click_drop_3"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/clipDropItem3Iv"-->
    <!--        app:layout_constraintRight_toRightOf="parent"-->
    <!--        app:layout_constraintTop_toTopOf="@id/clipDropItem3Iv" />-->

    <!--    <TextView-->
    <!--        android:id="@+id/clickDropItemHintTv"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:text="@string/str_guide_click_drop_hint"-->
    <!--        android:textColor="@color/white"-->
    <!--        android:textSize="18px"-->
    <!--        android:textStyle="bold"-->
    <!--        app:layout_constraintBottom_toTopOf="@id/clickDropItemBuyBtn"-->
    <!--        app:layout_constraintLeft_toRightOf="@id/line3"-->
    <!--        app:layout_constraintRight_toRightOf="parent"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/clipDropItem3Tv"-->
    <!--        app:layout_constraintVertical_chainStyle="packed" />-->

    <!--    <com.noober.background.view.BLTextView-->
    <!--        android:id="@+id/clickDropItemBuyBtn"-->
    <!--        android:layout_width="wrap_content"-->
    <!--        android:layout_height="50px"-->
    <!--        android:layout_marginTop="16px"-->
    <!--        android:gravity="center"-->
    <!--        android:paddingHorizontal="40px"-->
    <!--        android:text="@string/btn_guide_click_drop_buy"-->
    <!--        android:textColor="#6089EF"-->
    <!--        android:textSize="24px"-->
    <!--        android:textStyle="bold"-->
    <!--        app:bl_corners_radius="45px"-->
    <!--        app:bl_solid_color="@color/white"-->
    <!--        app:layout_constraintBottom_toBottomOf="parent"-->
    <!--        app:layout_constraintLeft_toLeftOf="@id/clickDropItemHintTv"-->
    <!--        app:layout_constraintRight_toRightOf="@id/clickDropItemHintTv"-->
    <!--        app:layout_constraintTop_toBottomOf="@id/clickDropItemHintTv" />-->

</androidx.constraintlayout.widget.ConstraintLayout>