<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="800px"
    android:layout_height="800px">

    <!--Dialog 的背景-->
    <View
        android:layout_width="match_parent"
        android:layout_height="100px"
        app:bl_corners_topLeftRadius="10px"
        app:bl_corners_topRightRadius="10px"
        app:bl_solid_color="#5879FC"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="#5879FC" />

    <View
        android:id="@+id/bgTopView"
        android:layout_width="match_parent"
        android:layout_height="100px"
        app:bl_corners_topLeftRadius="10px"
        app:bl_corners_topRightRadius="10px"
        app:bl_solid_color="#1A000000"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="#1A000000" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0px"
        app:bl_corners_bottomLeftRadius="10px"
        app:bl_corners_bottomRightRadius="10px"
        app:bl_solid_color="#5879FC"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/bgTopView"
        tools:background="#5879FC" />

    <ImageView
        android:id="@+id/closeBtn"
        android:layout_width="60px"
        android:layout_height="60px"
        android:layout_marginRight="20px"
        android:src="@drawable/ic_dialog_close"
        app:float_tips="@string/float_tip_close"
        app:layout_constraintBottom_toBottomOf="@id/bgTopView"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/bgTopView" />


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="58px"
        android:includeFontPadding="false"
        android:paddingTop="1px"
        android:text="@string/title_buy_click_drop"
        android:textColor="@color/white"
        android:textSize="48px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/bgTopView"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/bgTopView" />

    <ImageView
        android:id="@+id/qrCodeIv"
        android:layout_width="400px"
        android:layout_height="400px"
        android:scaleType="fitXY"
        android:src="@drawable/img_click_drop_qrcode"
        app:layout_constraintBottom_toTopOf="@id/qrCodeHintTv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/bgTopView"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/qrCodeHintTv"
        android:layout_width="500px"
        android:layout_height="wrap_content"
        android:layout_marginTop="53px"
        android:gravity="center"
        android:lineSpacingMultiplier="1.2"
        android:text="@string/str_buy_click_drop_hint"
        android:textColor="@color/white"
        android:textSize="28px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/qrCodeIv" />


</androidx.constraintlayout.widget.ConstraintLayout>