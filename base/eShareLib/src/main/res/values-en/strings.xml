<resources xmlns:tools="http://schemas.android.com/tools">
    <string name="str_ping_code">Wireless Screen-casting Code</string>
    <string name="str_ethernet_ssid">Cable connection</string>
    <string name="str_info_ip">IP: %s</string>
    <string name="str_info_ssid_label">Current Network</string>
    <string name="float_tip_close">Close</string>
    <string name="str_guide_apple_3">Choose %s to start screen casting.</string>
    <string name="title_setting">Settings</string>
    <string name="str_device_name">Display Name</string>
    <string name="item_setting_pin_code">Password</string>
    <string name="item_setting_smart_full_screen">Full Screen </string>
    <string name="item_setting_airplay_visibility">AirPlay Visible</string>
    <string name="item_setting_miracast_visibility">Miracast</string>
    <string name="item_setting_pin_code_window">Password Window</string>
    <string name="setting_options_enable">Enable</string>
    <string name="setting_options_disable">Disable</string>
    <string name="title_miracast_hint">Notification</string>
    <string name="str_cancel">Cancel</string>
    <string name="str_miracast_open">Enable</string>
    <string name="str_no_network_ssid">No Internet connection</string>
    <string name="str_qrcode_float_title">Scan to Start</string>
    <string name="str_miracast_disable_hint">Wi-Fi needs to be switched on to use Miracast.</string>
    <string name="str_air_play_disable_hint">Use AirPlay requires Internet connection</string>
    <string name="str_dlna_disable_hint">Use DLNA requires Internet connection</string>
    <string name="str_airplay_request">You just received a screen-casting request from %s.Would you like to accept?</string>
    <string name="str_airplay_request_reject">No</string>
    <string name="str_airplay_request_agree">Yes</string>

    <string name="str_guide_item_click_drop_title">ClickDrop\nWireless Screen-casting Gateway</string>
    <string name="str_guide_android_download">Scan QR code to download \"%s\" APP.</string>

    <string name="str_guide_category_apple">macOS &amp; iOS</string>
    <string name="str_guide_category_windows">Windows OS Computer</string>
    <string name="str_guide_category_click_drop">ClickDrop</string>
    <string name="str_guide_category_android">Android Phone/Tablet</string>
    <string name="str_guide_category_czur_share">CZUR Share</string>

    <string name="str_guide_title_apple">macOS &amp; iOS</string>
    <string name="str_guide_title_android">Android</string>
    <string name="str_guide_title_windows">Windows OS Computer</string>
    <string name="str_guide_title_czur_share">CZUR Share</string>
    <string name="str_guide_title_click_drop">ClickDrop</string>

    <string name="str_guide_click_drop_3">Press the button on the ClickDrop to start screen casting.</string>

    <string name="str_guide_click_drop_hint">Plug &amp; play. Internet connection is not required.</string>
    <string name="btn_guide_click_drop_buy">Purchase</string>

    <string name="title_buy_click_drop">ClickDrop</string>

    <string name="str_device_count_hint">Cast four screens max.</string>
    <string name="str_float_tips_device_count_hint">Wireless screen-casting for up to four devices simultaneously.</string>
    <string name="str_title_miracast">Miracast</string>
    <string name="str_title_airplay">AirPlay</string>
    <string name="str_miracast_device">Windows</string>
    <string name="str_airplay_device">iOS / macOS</string>

    <string name="str_guide_apple_2">Open Screen Mirroring function on iPhone/iPad/mac (supports AirPlay as well).</string>
    <string name="str_guide_pc_2">Press WIN+K on Windows computer keyboard, select %s, start to cast screen.</string>
    <string name="str_guide_pc_3">If you cannot cast screen following these operation, or cannot find StarryHub, or your computer doesn\'t support Miracast, you can download %1$s from www.czur.com and cast screen from %1$s.</string>
    <string name="str_guide_recommend">★ Most recommended</string>
    <string name="str_guide_click_drop_1">Insert ClickDrop to the USB port on the back side of StarryHub, wait for the paring to complete.</string>
    <string name="str_guide_click_drop_2">Connect ClickDrop to the computer, the LED indicator on ClickDrop will flash, wait until the indicator is on constantly.</string>
    <string name="str_guide_sub_title_harmony">Phone/Tablet</string>
    <string name="str_guide_item_click_drop_title_slogan"></string>
    <string name="str_device_name_illegal">This screen casting name is invalid. Please modify in settings.</string>
    <string name="str_title_eshare_screen_projection">Download app to cast screen.</string>
    <string name="str_title_direct_screen_projection">Cast screen directly.</string>
    <string name="str_airplay_hint">Use AirPlay requires Internet connection</string>

    <string name="str_czur_share_app_name">CZUR Share</string>
    <string name="str_czur_share_app_name_pc">CZUR Share</string>
    <string name="str_qr_code_guide">CZUR Share APP Casting QR Code</string>
    <string name="str_qr_code_guide_overseas">CZUR Share Wireless Screen \n Casting QR Code</string>
    <string name="item_setting_multiple_display">Cast Multiple Screens Simultaneously</string>
    <string name="item_setting_ask_before_casting">Screen Casting Confirmation</string>
    <string name="item_setting_device_name_float_window">Floating Window of StarryHub Name</string>
    <string name="str_more_setting">More Settings</string>
    <string name="str_qrcode_label">Scan to download\n\tor cast screen</string>
    <string name="str_ask_before_casting_hint">Require confirmation before receiving projection screens.</string>
    <string name="str_video_duration">%dS</string>
    <string name="str_guide_no_network">No network detected, please scan the code to watch.</string>
    <string name="title_guide_no_network_apple">Wireless Screen Casting Tutorial for Apple OS</string>
    <string name="title_guide_no_network_android">Wireless Screen Casting Tutorial for Android OS</string>
    <string name="title_guide_no_network_windows">Wireless Screen Casting Tutorial for Windows OS</string>
    <string name="title_guide_no_network_click_drop">Wireless Screen Casting Tutorial for ClickDrop</string>

    <string name="str_guide_android_2">Open the phone\'s shortcut menu or go to settings to find the "Screen Casting" option (such as: wireless screen casting, wireless display, screen sharing, screen projection, multi-screen interaction, etc.). Enable on the wireless screen casting function. </string>
    <string name="str_guide_android_3">Select 【%s】 to start casting screen.</string>
    <string name="str_guide_android_4">It\'s possible that the above mentioned methods cannot realize wireless screen casting. Because your phone might not support Miracast protocol, or because of compatibility problem. If you cannot find StarryHub via any of the above methods, please download \'CZUR Share\' app from www.czur.com and cast screen from \'CZUR Share\' app instead.</string>
    <string name="str_guide_linux_1">Make sure your device and StarryHub are in the same Wi-Fi network.</string>
    <string name="str_guide_linux_2">Open \'Google Chrome\' browser, click the \' ⋮ \' button on the upper left corner of the browser for more options, then select \'Cast...\'.</string>
    <string name="str_guide_linux_3">Select 【%s】 to start casting screen.</string>
    <string name="str_guide_video_app_1">Make sure your device and StarryHub are in the same Wi-Fi network.</string>
    <string name="str_guide_apple_1">Make sure your device and StarryHub are in the same Wi-Fi network.</string>
    <string name="str_guide_android_1">Make sure your device and StarryHub are in the same Wi-Fi network.</string>
    <string name="str_guide_pc_1">Make sure your device and StarryHub are in the same Wi-Fi network.</string>
    <string name="str_guide_czur_share_1">Make sure your device and StarryHub are in the same Wi-Fi network.</string>
    <string name="str_guide_video_app_2">Click the [ICON] button of the video app.</string>
    <string name="str_guide_video_app_3">Select 【%s（DLNA）】 to start casting video screen.</string>
    <string name="str_guide_video_app_3_oversea">Select 【%s】 to start casting video screen.</string>
    <string name="str_guide_czur_share_2">Open \'CZUR Share\' app, select 【%s】, click \'Connect\'.</string>
    <string name="str_guide_czur_share_3">Click [ICON] to start casting screen.</string>
    <string name="str_guide_czur_share_4">  You can download %1$s from www.czur.com or search and install “%1$s” from your phone\'s App Store, then cast screen.</string>
    <string name="str_guide_czur_share_4_store">APP Store</string>
    <string name="str_guide_p2p_1">Connect your mobile phone or computer to StarryHub\'s access point (Wi-Fi hotspot name %s, password changer007).</string>
    <string name="str_guide_p2p_2">"Mobile phone supported methods: Airplay, Miracast, CZUR Share, DLNA (local files only), wireless screen casting gateway.Computer supported methods: Airplay, Miracast, CZUR Share, wireless screen casting gateway."</string>
    <string name="str_guide_remark_label">Remark</string>
    <string name="str_guide_remark_czur_share">You can download \'CZUR Share\' app from www.czur.com, or search \'CZUR Share\' app from your APP Store in order to use \'CZUR Share\' for screen casting.</string>
    <string name="str_guide_remark_p2p">Wi-Fi connection is required for the first time use for activation purpose.</string>
    <string name="str_guide_category_linux">Linux System</string>
    <string name="str_guide_category_video_app">Video App</string>
    <string name="str_guide_category_p2p">P2P screen casting.</string>
    <string name="str_guide_title_linux">Linux System (Chromecast)</string>
    <string name="str_guide_title_video_app">Video App</string>
    <string name="str_guide_title_p2p">P2P screen casting.</string>
    <string name="str_buy_click_drop_hint">Scan QR code to purchase</string>
    <string name="str_float_tips_device_content_hint">Except DLNA</string>
    <string name="str_p2p_hint">P2P screen casting is more stable.</string>
    <string name="str_p2p_hint_summary">Please connect your computer/phone to the following Wi-Fi hotspot.</string>
    <string name="str_p2p_hint_pwd">Password %s</string>
    <string name="title_guide_no_network_dlna">Wireless Screen Casting Tutorial for Video Apps</string>
    <string name="title_guide_no_network_czur_share">Wireless Screen Casting Tutorial for CZUR Share App</string>
    <string name="title_guide_no_network_p2p">Wireless Screen Casting Tutorial for P2P</string>
    <string name="title_guide_no_network_linux">Wireless Screen Casting Tutorial for Linux OS</string>
    <string name="str_use_guide">How to cast screen wirelessly from \na mobile phone or a computer?</string>
    <string name="str_guide_float_title">How to cast screen wirelessly from a mobile phone or a computer?</string>
    <string name="str_eshare_quit_title">Would you like to stop casting screen?</string>
    <string name="dialog_normal_cancel">No</string>
    <string name="dialog_normal_confirm">Yes</string>

    <string name="str_title_hdmi">HDMI Screen Sharing</string>
    <string name="str_float_tips_device_peripheral_mode_hint">Peripheral Mode and Screen Sharing can be applied at the same time.</string>
    <string name="str_guide_video_app_2_mainland">Click the TV icon on the video meeting app.</string>
    <string name="str_float_tips_device_content_hint_army">Except DLNA</string>
</resources>
