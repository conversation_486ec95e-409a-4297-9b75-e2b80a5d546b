package com.czur.starry.device.wallpaperdisplay.view.activity

import android.content.Context
import android.content.Intent
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.PowerManager
import android.os.SystemClock
import android.view.Gravity
import android.view.KeyEvent
import android.view.MotionEvent
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.bumptech.glide.signature.ObjectKey
import com.czur.czurutils.log.logStackTrace
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.VersionIndustry
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.invisible
import com.czur.starry.device.baselib.utils.keyboard.keyboardHide
import com.czur.starry.device.baselib.utils.keyboard.keyboardShow
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.moveSelectionToLast
import com.czur.starry.device.baselib.utils.setCursorColor
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.baselib.view.dialog.LoadingDialog
import com.czur.starry.device.baselib.widget.CommonButton
import com.czur.starry.device.noticelib.hwconn.HwConnHelper
import com.czur.starry.device.wallpaperdisplay.App
import com.czur.starry.device.wallpaperdisplay.R
import com.czur.starry.device.wallpaperdisplay.bean.FileEntity
import com.czur.starry.device.wallpaperdisplay.bean.FileMode
import com.czur.starry.device.wallpaperdisplay.databinding.ActivityPreviewBinding
import com.czur.starry.device.wallpaperdisplay.util.CUSTOM_ASSETS
import com.czur.starry.device.wallpaperdisplay.util.DEFAULT_TEXT_BLACK
import com.czur.starry.device.wallpaperdisplay.util.DEFAULT_TEXT_BROWN
import com.czur.starry.device.wallpaperdisplay.util.DEFAULT_TEXT_BROWN_LIGHT
import com.czur.starry.device.wallpaperdisplay.util.DEFAULT_TEXT_YELLOW
import com.czur.starry.device.wallpaperdisplay.util.LEFT_LAYOUT
import com.czur.starry.device.wallpaperdisplay.util.MIDDLE_LAYOUT
import com.czur.starry.device.wallpaperdisplay.util.NULL_LAYOUT
import com.czur.starry.device.wallpaperdisplay.util.RECENT_ASSETS
import com.czur.starry.device.wallpaperdisplay.util.TOP_LAYOUT
import com.czur.starry.device.wallpaperdisplay.util.addDataLocal
import com.czur.starry.device.wallpaperdisplay.util.deleteLocalFile
import com.czur.starry.device.wallpaperdisplay.util.getName
import com.czur.starry.device.wallpaperdisplay.util.getSystemProp
import com.czur.starry.device.wallpaperdisplay.util.getTimeSystemProp
import com.czur.starry.device.wallpaperdisplay.util.getValueToList
import com.czur.starry.device.wallpaperdisplay.util.px2dp
import com.czur.starry.device.wallpaperdisplay.util.removeDataLocal
import com.czur.starry.device.wallpaperdisplay.util.removeRecentData
import com.czur.starry.device.wallpaperdisplay.util.saveRecentData
import com.czur.starry.device.wallpaperdisplay.util.setPlaySystemProp
import com.czur.starry.device.wallpaperdisplay.util.setSystemProp
import com.czur.starry.device.wallpaperdisplay.util.setTimeOver
import com.czur.starry.device.wallpaperdisplay.util.templateContainSize
import com.czur.starry.device.wallpaperdisplay.util.updateDataLocal
import com.czur.starry.device.wallpaperdisplay.view.dialog.NoticeDialog
import com.czur.starry.device.wallpaperdisplay.view.vm.DisplayVM.Companion.rootDir
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.atomic.AtomicBoolean


/**
 * created by wangh 22.0810
 */


class PreviewActivity : CZViewBindingAty<ActivityPreviewBinding>(), View.OnClickListener {


    private val loadingDialog by lazy { LoadingDialog() }


    private val mHandler: Handler by lazy {
        Handler(Looper.getMainLooper())
    }

    //播放的图片信息
    private lateinit var fileEntity: FileEntity

    /**保留重置的文本*/
    private lateinit var recoveryEntity: FileEntity

    //图片绝对路径
    private lateinit var fileAbsPath: String

    //启动播放后一秒内不响应按键和滑动
    private var first = AtomicBoolean(true)

    //判断当前页面状态
    private var wallPaperState = false

    //上一次点击事件时间
    private var clickTime = 0L
    private var interactionCount = 0


    companion object {
        private const val TAG = "PreviewActivity"
        private val whiteTheme: CommonButton.Theme = CommonButton.Theme.WHITE
        private val blueTheme: CommonButton.Theme = CommonButton.Theme.BLUE
        private val white2Theme: CommonButton.Theme = CommonButton.Theme.WHITE2
        private val darkTheme: CommonButton.Theme = CommonButton.Theme.DARK3

        // 双击毫秒数
        private const val CLICK_DEBOUNCE_TIME = 350L

        private const val EXTRA_BUNDLE = "extra_bundle"
        private const val EXTRA_FILE_ENTITY = "file_entity"
        private const val ASSETS_PATH = "file:///android_asset/"
        val btbLeaveFlow = MutableSharedFlow<Boolean>()

        //团建图片1，团建图片2 路径标记
        private const val TEAM_BUILD_PIC1_FLAG = "1team"
        private const val TEAM_BUILD_PIC2_FLAG = "2team"
        private const val TEAM_BUILD_PIC3_FLAG = "3team"
        private const val TEAM_BUILD_PIC4_FLAG = "4team"
        private const val CELEBRATE_PIC6_FLAG = "6birthday"

        private var fileMode = FileMode.CUSTOM
        private const val TIME_OUT_VISIBLE = 3000L
        var wallpaperIsPlaying = false


        fun startWithFilePath(
            context: Context = CZURAtyManager.currentActivity(),
            file: FileEntity,
            mode: FileMode,
            isPlay: Boolean = false
        ) {
            wallpaperIsPlaying = isPlay
            val intent = Intent(context, PreviewActivity::class.java)
            val bundle = Bundle()
            bundle.putParcelable(EXTRA_FILE_ENTITY, file)
            intent.putExtra(EXTRA_BUNDLE, bundle)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
            fileMode = mode
        }

    }

    /**
     * 编辑文本位置
     */
    enum class EditMode {
        TITLE_MODE,
        CONTENT_MODE,
        DATA_MODE
    }

    // 模板类型
    private var displayTag: Int = -1

    override fun handlePreIntent(preIntent: Intent) {
        super.handlePreIntent(preIntent)

        val bundle = intent.getBundleExtra(EXTRA_BUNDLE)

        //当使用状态，多任务或者三只滑动切换时候
        if (bundle == null) {
            launch {
                logTagD(TAG, "==fileEntity == null==")
                val list = getValueToList(RECENT_ASSETS)
                fileEntity = list[list.size - 1]
                wallpaperIsPlaying = getSystemProp()
                fileMode = FileMode.RECENT
                recoveryEntity = fileEntity.copy()
                fileAbsPath = fileEntity.absPath
            }
        } else {
            val file = bundle.getParcelable(EXTRA_FILE_ENTITY) as? FileEntity
            fileEntity = file as FileEntity
            recoveryEntity = fileEntity.copy()
            fileAbsPath = fileEntity.absPath
        }


    }

    override fun ActivityPreviewBinding.initBindingViews() {
        launch {
            //当重启后多任务或者三指滑动切换页面，fileEntity为null，需要等待获取数据
            while (::fileAbsPath.isInitialized.not()) {
                delay(50)
            }
            logTagD(TAG, "=-====fileAbsPath=$fileAbsPath")
            loadingDialog.show()
            etTitle.setOnClickListener(this@PreviewActivity)
            etContent.setOnClickListener(this@PreviewActivity)
            etDate.setOnClickListener(this@PreviewActivity)
            layoutBt.btMiddle.setOnClickListener(this@PreviewActivity)
            layoutBt.btLeft.setOnClickListener(this@PreviewActivity)
            layoutBt.btReset.setOnClickListener(this@PreviewActivity)
            btDelete.setOnClickListener(this@PreviewActivity)
            btSave.setOnClickListener(this@PreviewActivity)
            btSaveCustom.setOnClickListener(this@PreviewActivity)
            titleBarPre.setOnClickListener(this@PreviewActivity)
            previewLayout.setOnClickListener(this@PreviewActivity)
            btPlayOrStop.setOnDebounceClickListener {
                //使用或停止
                if (wallpaperIsPlaying) {
                    stopPlaying()
                } else {
                    hideETSaveBT()
                    updateEtData()
                    showNoticeDialog()
                }
            }

            //非官方模板的自定义图片无文本及文本位置
            if (fileEntity.textMode == NULL_LAYOUT) {
                layoutBt.previewLayout.visibility = View.INVISIBLE
                btSaveCustom.gone()
                disableEdit()
                binding.btPlayOrStop.changeTheme(white2Theme)
            } else { //模板；模板保存的自定义；最近使用的模板
                //官方模板不可删除，官方模板保存的自定义可删除
                if (fileMode == FileMode.MEETING) {
                    btDelete.gone()
                } else {
                    if (fileMode == FileMode.CUSTOM) {
                        btSave.show()
                    }

                    //非官方模板无保存自定义模板功能
                    btSaveCustom.gone()
                    //非官方模板确定使用颜色调整
                    binding.btPlayOrStop.changeTheme(white2Theme)
                }

                displayTag = fileEntity.texTag

                //初始化文本布局
                loadConfiguration(fileEntity.textMode)
                etTitle.setText(fileEntity.textFist)
                etContent.setText(fileEntity.textSecond)
                etDate.setText(fileEntity.textThird)
                if (isTeamPic1Or2()) {
                    etDate.invisible()
                }
                //初始化编辑文本
                changeEditTextBolder(true)
            }
        }

        launch {
            //触控板抬起
            btbLeaveFlow.collect {
                logTagD(TAG, "===collect==")
                if (wallPaperState) {
                    finish()
                }
            }
        }


    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        launch {
            try {
                while (::fileAbsPath.isInitialized.not()) {
                    delay(50)
                }
                var path = "$ASSETS_PATH$fileAbsPath"
                if (fileAbsPath.contains(rootDir.absolutePath)) {
                    path = fileAbsPath
                }
                Glide.with(this@PreviewActivity)
                    .load(
                        path!!
                    )
                    .signature(ObjectKey(System.currentTimeMillis()))
                    .listener(object : RequestListener<Drawable> {
                        override fun onLoadFailed(
                            e: GlideException?,
                            model: Any?,
                            target: Target<Drawable>,
                            isFirstResource: Boolean
                        ): Boolean {
                            return false
                        }

                        override fun onResourceReady(
                            resource: Drawable,
                            model: Any,
                            target: Target<Drawable>?,
                            dataSource: DataSource,
                            isFirstResource: Boolean
                        ): Boolean {
                            loadingDialog.dismiss()
                            return false
                        }

                    })
                    .into(binding.imDisView)


            } catch (e: Exception) {
                e.printStackTrace()
            }

            if (wallpaperIsPlaying) {
                playAll()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        logTagD(TAG, "=====onResume==")
        launch {
            while (::fileAbsPath.isInitialized.not()) {
                delay(50)
            }
            //当使用状态，判断当前file是否是播放文件一致
            if (!wallpaperIsPlaying && getSystemProp()) {
                val list = getValueToList(RECENT_ASSETS).sortedByDescending {
                    it.lastModifyTime
                }
                if (list.isNotEmpty()) {
                    if (fileEntity == list[0]) {
                        playAll()
                    }
                }
            }
        }
    }

    override fun onClick(view: View) {
        if (System.currentTimeMillis() - clickTime < CLICK_DEBOUNCE_TIME) {
            return
        }
        when (view.id) {
            R.id.bt_middle -> {
                fileEntity.textMode = MIDDLE_LAYOUT
                //目前团建1，和2两张图是Top布局
                if (isTeamPic1Or2()) {
                    fileEntity.textMode = TOP_LAYOUT
                }
                loadConfiguration(fileEntity.textMode)
                updateEtData()
            }

            R.id.bt_left -> {
                fileEntity.textMode = LEFT_LAYOUT
                loadConfiguration(LEFT_LAYOUT)
                updateEtData()
            }

            R.id.bt_delete -> delImage(fileEntity)
            R.id.bt_save -> {
                //自定义模板保存文本
                hideETSaveBT()
                updateEtData(true)
                toast(R.string.str_et_save_success)
            }

            R.id.et_title -> if (!wallpaperIsPlaying) editText(EditMode.TITLE_MODE) else showBackDelay()
            R.id.et_content -> if (!wallpaperIsPlaying) editText(EditMode.CONTENT_MODE) else showBackDelay()
            R.id.et_date -> if (!wallpaperIsPlaying) editText(EditMode.DATA_MODE) else showBackDelay()
            R.id.bt_reset -> recoveryETData()
            R.id.title_bar_pre -> finish()
            //点击空白处
            R.id.preview_layout -> {
                if (wallpaperIsPlaying) {
                    showBackDelay()
                } else {
                    hideETSaveBT()
                    updateEtData()
                }
            }

            R.id.bt_save_custom -> {
                //标准模板保存为自定义模板
                saveDataToCustom()
            }
        }
        clickTime = System.currentTimeMillis()
    }

    private fun isTeamPic1Or2(): Boolean {
        return fileAbsPath.contains(TEAM_BUILD_PIC1_FLAG) || fileAbsPath.contains(
            TEAM_BUILD_PIC2_FLAG
        )
    }

    private fun isEditBgColorBlue(): Boolean {
        return fileAbsPath.contains(TEAM_BUILD_PIC3_FLAG) || fileAbsPath.contains(
            TEAM_BUILD_PIC4_FLAG
        ) || fileAbsPath.contains(CELEBRATE_PIC6_FLAG)
    }


    private fun showNoticeDialog() {
        NoticeDialog.Builder().setConfirmClickListener {
            logTagD(TAG, "====time==$it")
            launch {
                setTimeOver(it)
                setPlayState()
                if (Constants.versionIndustry == VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD) {
                    logTagI(TAG, "军工版本 直接播放")
                    playAll()
                } else {
                    HwConnHelper.showHwConnAlert(
                        this@PreviewActivity,
                        HwConnHelper.HwConnType.SCREEN_SAVER
                    )
                    CZURAtyManager.finishAllAty()
                }
            }
        }.build().show()
    }

    private fun loadConfiguration(textMode: Int) {

        val titleLayout = binding.etTitle.layoutParams as ConstraintLayout.LayoutParams
        val contentLayout = binding.etContent.layoutParams as ConstraintLayout.LayoutParams
        val dateLayout = binding.etDate.layoutParams as ConstraintLayout.LayoutParams
        titleLayout.dimensionRatio = "h,16:9"
        when (textMode) {
            MIDDLE_LAYOUT -> {//居中
                titleLayout.leftToLeft = R.id.preview_layout
                titleLayout.rightToRight = R.id.preview_layout
                titleLayout.leftMargin = 0
                titleLayout.topMargin = px2dp(this, 480)
                val width = resources.displayMetrics.widthPixels // 计算宽度避免显示不全
                titleLayout.matchConstraintMaxWidth = width
                binding.etTitle.textSize = px2dp(this, 100).toFloat()
                binding.etTitle.gravity = Gravity.CENTER

                contentLayout.leftToLeft = R.id.preview_layout
                contentLayout.rightToRight = R.id.preview_layout
                contentLayout.leftMargin = 0
                contentLayout.topMargin = px2dp(this, 20)
                contentLayout.matchConstraintMaxWidth = width
                binding.etContent.textSize = px2dp(this, 50).toFloat()
                binding.etContent.gravity = Gravity.CENTER

                dateLayout.leftToLeft = R.id.preview_layout
                dateLayout.rightToRight = R.id.preview_layout
                dateLayout.leftMargin = 0
                dateLayout.topMargin = px2dp(this, 20)
                dateLayout.matchConstraintMaxWidth = width
                binding.etDate.textSize = px2dp(this, 50).toFloat()
                binding.etDate.gravity = Gravity.CENTER
                middleLayout()
            }

            LEFT_LAYOUT -> {//左上
                titleLayout.rightToRight = -1
                titleLayout.leftMargin = px2dp(this, 120)
                titleLayout.topMargin = px2dp(this, 240)
                val width = resources.displayMetrics.widthPixels - px2dp(this, 120)
                titleLayout.matchConstraintMaxWidth = width
                binding.etTitle.textSize = px2dp(this, 100).toFloat()
                binding.etTitle.gravity = Gravity.START

                contentLayout.rightToRight = -1
                contentLayout.leftMargin = px2dp(this, 120)
                contentLayout.topMargin = px2dp(this, 20)
                contentLayout.matchConstraintMaxWidth = width
                binding.etContent.textSize = px2dp(this, 50).toFloat()
                binding.etContent.gravity = Gravity.START


                dateLayout.rightToRight = -1
                dateLayout.leftMargin = px2dp(this, 120)
                dateLayout.topMargin = px2dp(this, 20)
                dateLayout.matchConstraintMaxWidth = width
                binding.etDate.textSize = px2dp(this, 50).toFloat()
                binding.etDate.gravity = Gravity.START
                leftLayout()
            }

            TOP_LAYOUT -> {
                titleLayout.leftToLeft = R.id.preview_layout
                titleLayout.rightToRight = R.id.preview_layout
                titleLayout.leftMargin = 0
                titleLayout.topMargin = px2dp(this, 100)
                val width = resources.displayMetrics.widthPixels // 计算宽度避免显示不全
                titleLayout.matchConstraintMaxWidth = width
                binding.etTitle.textSize = px2dp(this, 100).toFloat()
                binding.etTitle.gravity = Gravity.CENTER

                contentLayout.leftToLeft = R.id.preview_layout
                contentLayout.rightToRight = R.id.preview_layout
                contentLayout.leftMargin = 0
                contentLayout.topMargin = px2dp(this, 20)
                contentLayout.matchConstraintMaxWidth = width
                binding.etContent.textSize = px2dp(this, 50).toFloat()
                binding.etContent.gravity = Gravity.CENTER

                dateLayout.leftToLeft = R.id.preview_layout
                dateLayout.rightToRight = R.id.preview_layout
                dateLayout.leftMargin = 0
                dateLayout.topMargin = px2dp(this, 20)
                dateLayout.matchConstraintMaxWidth = width
                binding.etDate.textSize = px2dp(this, 50).toFloat()
                binding.etDate.gravity = Gravity.CENTER
                middleLayout()
            }
        }
        when (displayTag) {
            DEFAULT_TEXT_YELLOW -> {
                binding.etTitle.setTextColor(getColor(R.color.yellow))
                binding.etTitle.setCursorColor(0xFF5879FC.toInt())
                binding.etContent.setTextColor(getColor(R.color.yellow))
                binding.etContent.setCursorColor(0xFF5879FC.toInt())
                binding.etDate.setTextColor(getColor(R.color.yellow))
                binding.etDate.setCursorColor(0xFF5879FC.toInt())
            }

            DEFAULT_TEXT_BLACK -> {
                binding.etTitle.setTextColor(getColor(R.color.black))
                binding.etTitle.setCursorColor(0xFF5879FC.toInt())
                binding.etContent.setTextColor(getColor(R.color.black))
                binding.etContent.setCursorColor(0xFF5879FC.toInt())
                binding.etDate.setTextColor(getColor(R.color.black))
                binding.etDate.setCursorColor(0xFF5879FC.toInt())
            }

            DEFAULT_TEXT_BROWN -> {
                binding.etTitle.setTextColor(getColor(R.color.brown))
                binding.etTitle.setCursorColor(0xFF5879FC.toInt())
                binding.etContent.setTextColor(getColor(R.color.brown))
                binding.etContent.setCursorColor(0xFF5879FC.toInt())
                binding.etDate.setTextColor(getColor(R.color.brown))
                binding.etDate.setCursorColor(0xFF5879FC.toInt())
            }

            DEFAULT_TEXT_BROWN_LIGHT -> {
                binding.etTitle.setTextColor(getColor(R.color.brown_light))
                binding.etTitle.setCursorColor(0xFF5879FC.toInt())
                binding.etContent.setTextColor(getColor(R.color.brown_light))
                binding.etContent.setCursorColor(0xFF5879FC.toInt())
                binding.etDate.setTextColor(getColor(R.color.brown_light))
                binding.etDate.setCursorColor(0xFF5879FC.toInt())
            }
        }

        binding.etTitle.layoutParams = titleLayout
        binding.etContent.layoutParams = contentLayout
        binding.etDate.layoutParams = dateLayout
    }


    private fun stopPlaying() {
        if (fileEntity.textMode != NULL_LAYOUT) {
            enableEdit()
        }
        mHandler.removeCallbacks(visibleRunnable)
        wallpaperIsPlaying = false
        wallPaperState = false
        setSystemProp(false)
        setPlaySystemProp(false)
        binding.btPlayOrStop.changeTheme(white2Theme)

        binding.btPlayOrStop.text = getString(R.string.str_bt_play)
        binding.btPlayOrStop.show()
        binding.titleBarPre.show()
        binding.btDelete.show()
        if (fileEntity.textMode != NULL_LAYOUT)
            binding.layoutBt.previewLayout.show()
        logTagV(TAG, "取消保持屏幕常亮")
        changeKeepScreenOn(false)
    }


    //常亮播放
    private fun playAll() {
        hideETSaveBT()
        disableEdit()   // 播放时禁用EditText,防止选中
        wallpaperIsPlaying = true
        wallPaperState = true
        setPlaySystemProp(true)

        binding.btPlayOrStop.changeTheme(whiteTheme)
        binding.btPlayOrStop.text = getString(R.string.str_stop_play)
        binding.btSaveCustom.gone()
        binding.btSave.gone()
        binding.btPlayOrStop.gone()
        binding.titleBarPre.gone()
        binding.layoutBt.previewLayout.gone()
        binding.btDelete.gone()
        logTagV(TAG, "保持屏幕常亮")
        changeKeepScreenOn(true)
        launch {//1秒后开始生效
            delay(1000)
            first.set(false)
        }
        launch(Dispatchers.IO) {
            //计时开始
            val endTime = getTimeSystemProp().toLong()
            while (isActive) {
                if (System.currentTimeMillis() >= endTime) {
                    setSystemProp(false)
                    makeScreenOff()
                }
            }
        }
    }

    //保存播放状态
    private suspend fun setPlayState() {
        //设置prop
        setSystemProp(true)
        //保存
        fileEntity.lastModifyTime = System.currentTimeMillis()
        //播放模式切换到最近使用
        fileMode = FileMode.RECENT
        recoveryEntity = fileEntity.copy()
        saveRecentData(fileEntity)?.let {
            deleteLastRecent(it)
        }

    }

    /**
     * 最近使用保留4个，最早的一个判断是否有本地图片一起删除
     */
    private suspend fun deleteLastRecent(entity: FileEntity) = withContext(Dispatchers.IO) {
        if (entity.textMode == NULL_LAYOUT) {
            if (templateContainSize(entity, CUSTOM_ASSETS) == 0
                && templateContainSize(entity, RECENT_ASSETS) == 0
            ) {
                logTagD(TAG, "==deleteLocalFile==${entity.absPath}")
                deleteLocalFile(entity.absPath)
            }
        }
    }

    //息屏
    private fun makeScreenOff() {
        val powerManager = App.app.getSystemService(PowerManager::class.java) as PowerManager
        try {
            powerManager.javaClass.getMethod(
                "goToSleep",
                *arrayOf<Class<*>?>(Long::class.javaPrimitiveType)
            ).invoke(powerManager, SystemClock.uptimeMillis())
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    //编辑文本
    private fun editText(mode: EditMode) {
        //去除edit焦点
        binding.imDisView.isFocusableInTouchMode = true

        binding.etDate.isFocusable = true
        binding.etDate.isFocusableInTouchMode = true
        binding.etContent.isFocusable = true
        binding.etContent.isFocusableInTouchMode = true
        binding.etTitle.isFocusable = true
        binding.etTitle.isFocusableInTouchMode = true
        when (mode) {
            EditMode.TITLE_MODE -> {
                binding.etTitle.requestFocus()
                binding.etTitle.keyboardShow()
            }

            EditMode.CONTENT_MODE -> {
                binding.etContent.requestFocus()
                binding.etContent.keyboardShow()
            }

            EditMode.DATA_MODE -> {
                binding.etDate.requestFocus()
                binding.etDate.keyboardShow()
            }
        }
        changeEditTextBolder(true)
    }


    private fun changeEditTextBolder(showBolder: Boolean) {
        val borderDrawable = if (isEditBgColorBlue()) {
            R.drawable.et_border_blue // 生日等
        } else {
            R.drawable.et_border
        }
        if (showBolder) {
            binding.etTitle.setBackgroundResource(borderDrawable)
            binding.etContent.setBackgroundResource(borderDrawable)
            binding.etDate.setBackgroundResource(borderDrawable)
        } else {
            binding.etTitle.setBackgroundColor(0)
            binding.etContent.setBackgroundColor(0)
            binding.etDate.setBackgroundColor(0)
        }
    }

    //更新/保存编辑文本
    private fun updateEtData(isSave: Boolean = false) {
        fileEntity.textFist = binding.etTitle.text.toString()
        fileEntity.textSecond = binding.etContent.text.toString()
        fileEntity.textThird = binding.etDate.text.toString()
        if (isSave) {
            updateFileEntity(fileEntity)
            recoveryEntity = fileEntity.copy()
        }
    }

    /**
     * 保存至自定义模板
     */
    private fun saveDataToCustom() {
        lifecycleScope.launch {
            val customList = getValueToList(CUSTOM_ASSETS)
            val customCount = customList.size
            if (customCount > 19) {
                toast(R.string.str_create_temp_filed)
                return@launch
            }
            updateEtData()
            var entity = fileEntity.copy()
            entity.apply {
                name = getName(customList)
                lastModifyTime = System.currentTimeMillis()
            }
            logTagD(TAG, "==saveDataToCustom==${entity.name}")
            addDataLocal(entity)
            toast(R.string.str_create_temp_success)
        }

    }

    //恢复默认编辑文本
    private fun recoveryETData() {

        binding.etTitle.apply {
            setText(recoveryEntity.textFist)
            moveSelectionToLast()
        }
        binding.etContent.apply {
            setText(recoveryEntity.textSecond)
            moveSelectionToLast()
        }
        binding.etDate.apply {
            setText(recoveryEntity.textThird)
            moveSelectionToLast()
        }

        fileEntity.textMode = recoveryEntity.textMode
        updateEtData()
        loadConfiguration(fileEntity.textMode)
    }


    //删除图片
    private fun delImage(entity: FileEntity = fileEntity) {
        launch {
            //非标准模板图片
            if (entity.textMode == NULL_LAYOUT) {
                if (fileMode == FileMode.RECENT) {
                    //最近使用当前一张，自定义模板不存在可删除
                    if (templateContainSize(entity, CUSTOM_ASSETS) == 0
                        && templateContainSize(entity, RECENT_ASSETS) == 1
                    ) {
                        logTagD(TAG, "===deleteLocalFile==${entity.absPath}")
                        deleteLocalFile(entity.absPath)
                    }
                } else if (fileMode == FileMode.CUSTOM) {
                    //自定义模板当前一张，最近使用列表没有则可删除
                    if (templateContainSize(entity, RECENT_ASSETS) == 0) {
                        logTagD(TAG, "===deleteLocalFile==${entity.absPath}")
                        deleteLocalFile(entity.absPath)
                    }
                }
            }
            //自定义删除
            if (fileMode == FileMode.CUSTOM) {
                removeDataLocal(entity)
            }
            //最近删除
            if (fileMode == FileMode.RECENT) {
                removeRecentData(entity)
            }

            finish()
        }
    }

    //居中
    private fun middleLayout() {
        binding.layoutBt.btMiddle.changeTheme(white2Theme)
        binding.layoutBt.btLeft.changeTheme(darkTheme)
    }

    //左上
    private fun leftLayout() {
        binding.layoutBt.btMiddle.changeTheme(darkTheme)
        binding.layoutBt.btLeft.changeTheme(white2Theme)
    }

    //更新数据
    private fun updateFileEntity(fileEntity: FileEntity) {
        launch {
            updateDataLocal(data = fileEntity)
        }
    }


    private fun hideETSaveBT() {
        binding.etDate.isFocusable = false
        binding.etContent.isFocusable = false
        binding.etTitle.isFocusable = false
        binding.etTitle.keyboardHide()
        binding.etContent.keyboardHide()
        binding.etDate.keyboardHide()
        changeEditTextBolder(false)
    }

    private fun disableEdit() {
        binding.etDate.isEnabled = false
        binding.etContent.isEnabled = false
        binding.etTitle.isEnabled = false
        binding.etTitle.keyboardHide()
        binding.etContent.keyboardHide()
        binding.etDate.keyboardHide()
    }

    private fun enableEdit() {
        binding.etDate.isEnabled = true
        binding.etContent.isEnabled = true
        binding.etTitle.isEnabled = true
    }


    override fun onPause() {
        super.onPause()
        if (wallPaperState) {
            finish()
        }

    }

    override fun onDestroy() {
        wallpaperIsPlaying = false
        setPlaySystemProp(false)
        super.onDestroy()
        logTagV(TAG, "取消屏幕常量")
        changeKeepScreenOn(false)
    }

    override fun onUserInteraction() {
        super.onUserInteraction()
        interactionCount++
        if (interactionCount % 10 != 0) {
            return      // 防止操作过于频繁
        }
        if (!wallpaperIsPlaying) return
        if (first.get()) return
        logTagV(TAG, "onUserInteraction - showBackDelay")
        showBackDelay()
    }

    private fun showBackDelay() {
        mHandler.removeCallbacks(visibleRunnable)
        binding.btPlayOrStop.show()
        binding.titleBarPre.show()
        mHandler.postDelayed(visibleRunnable, TIME_OUT_VISIBLE)
    }

    private var visibleRunnable = Runnable {
        binding.btPlayOrStop.gone()
        binding.titleBarPre.gone()
        interactionCount = -1
    }


    override fun onInterceptKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        logTagD(TAG, "====Keycode=$keyCode")
        if (wallpaperIsPlaying && first.get()) return true
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (wallpaperIsPlaying) {
                if (binding.titleBarPre.isVisible) finish() else showBackDelay()
            } else {
                finish()
            }
            return true
        } else if (keyCode != 24 && keyCode != 25
            && keyCode != 313 && keyCode != 314 && keyCode != 315
            && keyCode != 317 && keyCode != 316
            && keyCode != 322 && keyCode != 325 && keyCode != 326
            && keyCode != 320
        ) {//屏蔽触控板上面一排功能键；其他键值都相应
            if (wallpaperIsPlaying) {
                if (binding.titleBarPre.isGone) showBackDelay()
            }
        }
        return super.onInterceptKeyDown(keyCode, event)
    }


}