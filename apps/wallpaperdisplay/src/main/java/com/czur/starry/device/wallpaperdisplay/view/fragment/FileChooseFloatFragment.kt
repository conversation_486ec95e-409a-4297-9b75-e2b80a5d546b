package com.czur.starry.device.wallpaperdisplay.view.fragment

import androidx.fragment.app.viewModels
import com.czur.starry.device.baselib.view.floating.KeyBackFloatFragment
import com.czur.starry.device.wallpaperdisplay.R
import com.czur.starry.device.wallpaperdisplay.view.vm.DisplayVM


/**
 *
 * 文件选择浮窗
 */

class FileChooseFloatFragment : KeyBackFloatFragment(bgDark = true) {
    override fun getLayoutId(): Int = R.layout.float_file_choose

    companion object {
        private const val TAG = "FileChooseFloatFragment"
        const val SHOW_TAG = "FileChooseFloatFragment"
    }

    private val listFragment = FileListFragment()
    override var floatTag: String? = SHOW_TAG
    private val shareFileVM: DisplayVM by viewModels({ requireActivity() })

    override fun initView() {
        super.initView()

        shareFileVM.startSelect()

        childFragmentManager.beginTransaction()
            .add(R.id.chooseFileFl, listFragment)
            .commitAllowingStateLoss()
    }


    fun showPreviewFragment(){
        childFragmentManager.beginTransaction()
            .hide(listFragment)
            .add(R.id.chooseFileFl, SelectImageFragment())
            .addToBackStack(null)
            .commitAllowingStateLoss()
    }

    override fun onDismiss() {
        super.onDismiss()
        shareFileVM.resetPath()
        shareFileVM.stopSelect() // 分享选择结束
    }

}