package com.czur.starry.device.wallpaperdisplay.task

import com.czur.czurutils.log.logTagD
import com.czur.starry.device.wallpaperdisplay.bean.FileEntity
import com.czur.starry.device.wallpaperdisplay.manager.del.IDeleter
import com.czur.starry.device.wallpaperdisplay.manager.inter.CloudComputeSumSize
import com.czur.starry.device.wallpaperdisplay.manager.inter.CopyTask
import com.czur.starry.device.wallpaperdisplay.manager.inter.IComputeSumSize
import com.czur.starry.device.wallpaperdisplay.manager.oss.OSSManager
import java.io.File

/**
 * base from 陈丰尧 on 3/24/21
 * 云盘到本地, 就是下载
 */
private const val TAG = "CloudToLocalTask"

class CloudToLocalTask : CopyTask, IComputeSumSize by CloudComputeSumSize(), IDeleter {


    /**
     * 检测是否有同名文件,耗时操作
     */
    override suspend fun checkExist(src: FileEntity, destDir: FileEntity): Boolean {
        // TODO 文件夹和文件的判断存在似乎有问题
        val dirFile = File(destDir.absPath)
        val destFile = File(dirFile, src.name) // 复制后的文件
        return destFile.exists()
    }

    /**
     * 执行复制操作
     * @param src       要复制的文件
     * @param destDir   目的地文件夹
     * @param size      src所占用的空间,为了子类计算进度使用
     * @param progressListener  用于更新进度,完成了的百分比 0-1之间
     */
    override suspend fun doCopy(
        src: FileEntity,
        destDir: FileEntity,
        size: Long,
        progressListener: (progress: Float) -> Unit
    ) {
        logTagD(TAG, "云端->本地开始复制:${src.name}->${destDir.name}")
        OSSManager.download(src, destDir) { finishSize ->
            val progress = finishSize.toFloat() / size
            progressListener(progress)
        }

        logTagD(TAG, "云端->本地传输完成")
    }


    override suspend fun del(src: FileEntity): Boolean {
        TODO("Not yet implemented")
    }

}