package com.czur.starry.device.wallpaperdisplay.manager.oss

import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.data.sp.SPHandler
import com.czur.starry.device.baselib.network.core.MiaoHttpEntity
import com.czur.starry.device.baselib.network.core.MiaoHttpGet
import com.czur.starry.device.baselib.network.core.MiaoHttpParam
import com.czur.starry.device.baselib.network.core.MiaoHttpPost
import com.czur.starry.device.temposs.OSSTokenEntity

/**
 * Created by 陈丰尧 on 1/6/21
 * 请求OSS相关数据
 */

interface IOSSService {

    @MiaoHttpPost("/api/deviceFileRest/oss/token")
    fun getFileToken(
        @MiaoHttpParam("sn") sn: String = Constants.SERIAL,
        @MiaoHttpParam("u_id") uId: String = UserHandler.accountNo.toString(),
        clazz: Class<OSSTokenEntity> = OSSTokenEntity::class.java
    ): MiaoHttpEntity<OSSTokenEntity>

    @MiaoHttpGet("/api/starry/share/getOssTempToken")
    fun getToken(
        @MiaoHttpParam("sn") sn: String = Constants.SERIAL,
        clazz: Class<OSSTokenEntity> = OSSTokenEntity::class.java
    ): MiaoHttpEntity<OSSTokenEntity>
}