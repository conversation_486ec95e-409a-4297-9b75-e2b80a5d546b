package  com.czur.starry.device.wallpaperdisplay.manager.inter




import com.czur.starry.device.wallpaperdisplay.bean.FileEntity

/**
 * Created by 陈丰尧 on 3/23/21
 * 复制任务
 */
interface CopyTask : IComputeSumSize {

    /**
     * 检测是否有同名文件,耗时操作
     */
    suspend fun checkExist(src: FileEntity, destDir: FileEntity): Boolean

    /**
     * 执行复制操作
     * @param src       要复制的文件
     * @param destDir   目的地文件夹
     * @param size      src所占用的空间,为了子类计算进度使用
     * @param delOnFinish   复制完成后是否删除源文件
     * @param progressListener  用于更新进度,完成了的百分比 0-1之间
     */
    suspend fun doCopy(
        src: FileEntity,
        destDir: FileEntity,
        size: Long,
        progressListener: (progress: Float) -> Unit
    )

    /**
     * 申请锁
     */
    suspend fun applyCloudLock(
        srcFiles: List<FileEntity>,
        destDir: FileEntity,
        isMove: Boolean
    ): Boolean = true

    /**
     * 释放锁
     */
    fun releaseCloudLock() {}
}