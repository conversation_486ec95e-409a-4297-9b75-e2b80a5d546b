package com.czur.starry.device.wallpaperdisplay.view.activity

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.noticelib.hwconn.HwConnHelper.dismissHwConnAlert
import com.czur.starry.device.wallpaperdisplay.bean.FileMode
import com.czur.starry.device.wallpaperdisplay.util.RECENT_ASSETS
import com.czur.starry.device.wallpaperdisplay.util.getPlaySystemProp
import com.czur.starry.device.wallpaperdisplay.util.getValueToList
import com.czur.starry.device.wallpaperdisplay.util.setSystemProp
import com.czur.starry.device.wallpaperdisplay.view.activity.PreviewActivity.Companion.btbLeaveFlow
import com.czur.starry.device.wallpaperdisplay.view.activity.PreviewActivity.Companion.startWithFilePath
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/**
 * created by wangh 22.0825
 */

private const val TAG = "PlayWallPaperReceiver"
private const val DISPLAY_WALLPAPER = "com.android.action.CZUR_DISPLAY_WALLPAPER"
private const val BTB_LEAVE = "com.android.action.CZUR.BTB.LEAVE"

class PlayWallPaperReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context?, intent: Intent?) {
        if (Constants.starryHWInfo.salesLocale == StarryDevLocale.Overseas) {
            logTagV(TAG, "海外版不使用欢庆屏保")
            return
        }
        if (Constants.starryHWInfo.model == StarryModel.StudioModel.StudioSPlus) {
            logTagV(TAG, "StudioSPlus不支持欢庆屏保")
            return
        }
        val action = intent?.action
        logTagD(TAG, "=?.action ==${action}")

        if (action == DISPLAY_WALLPAPER) {//定制广播 自动休眠前发送；触控板down发送
            if (getPlaySystemProp()) return
            try {
                MainScope().launch {
                    val list = getValueToList(RECENT_ASSETS).sortedByDescending {
                        it.lastModifyTime
                    }
                    if (list.isNotEmpty()) {
                        dismissHwConnAlert(context!!)
                        val file = list[0]
                        startWithFilePath(context, file, FileMode.RECENT, true)
                    } else {
                        setSystemProp(false)
                    }
                }
            } catch (e: Exception) {
                setSystemProp(false)
            }

        } else if (action == BTB_LEAVE) {
            MainScope().launch {
                btbLeaveFlow.emit(true)
            }

        }

    }
}