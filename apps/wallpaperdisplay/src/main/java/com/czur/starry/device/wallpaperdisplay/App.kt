package com.czur.starry.device.wallpaperdisplay

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.SharedPreferencesMigration
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStore
import com.czur.starry.device.baselib.base.listener.StarryApp
import com.czur.starry.device.baselib.handler.createDefCorruptionHandler
import kotlin.properties.Delegates

/**
 * created by wangh 22.0727
 */


class App : StarryApp() {
    companion object {
        var app: App by Delegates.notNull()

        private const val DATA_STORE_NAME = "WallPaperDispalyConfig"
        private const val OLD_PREFERENCE_NAME = "WallPaperDispaly"
    }

    //数据迁移
    val dataStore: DataStore<Preferences> by preferencesDataStore(
        name = DATA_STORE_NAME,
        corruptionHandler = createDefCorruptionHandler(DATA_STORE_NAME),
        produceMigrations = { context ->
            listOf(
                SharedPreferencesMigration(
                    context,
                    OLD_PREFERENCE_NAME
                )
            )
        }
    )


    override fun onCreate() {
        super.onCreate()
        app = this
    }


}