package com.czur.starry.device.wallpaperdisplay.view.dialog

import android.graphics.BitmapFactory
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.czur.czurutils.img.QrCodeUtil
import com.czur.starry.device.baselib.base.BaseDialog
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.view.findView
import com.czur.starry.device.wallpaperdisplay.R
import com.czur.starry.device.wallpaperdisplay.util.EncodingUtils

/**
 * created by wangh 22.0809
 */

class ScanQRCodeDialog : BaseDialog() {

    var confirmClickListener: (() -> Unit)? = null
    var cancelClickListener: (() -> Unit)? = null
    var selectClickListener: (() -> Unit)? = null
    var link: String? = null

    private val imView: ImageView by findView(R.id.im_view)
    private val titleTv: TextView by findView(R.id.titleTv)
    private val normalDialogConfirmBtn: View by findView(R.id.normalDialogConfirmBtn)
    private val normalDialogCancelBtn: View by findView(R.id.normalDialogCancelBtn)

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_qrcode_select, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        launch {
            val bitmap = BitmapFactory.decodeResource(resources, R.drawable.czur_logo)
            link?.let {

                val codeImage = QrCodeUtil.generateQrCodeBmp(it) {
                    edge = 320
                    pointColor = 0xFF5879FC.toInt()
                    bgColor = -0x1
                    delPadding = false
                    logoConfig {
                        logoBmp = bitmap
                    }
                }
                val drawable = EncodingUtils.RoundImageDrawable(codeImage, 12f)
                imView.setImageDrawable(drawable)
            }
        }

        titleTv.text = com.czur.starry.device.baselib.utils.getString(R.string.tv_qrcode_content)
        normalDialogConfirmBtn.setOnClickListener {
            confirmClickListener?.invoke()
            dismiss()
        }
        normalDialogCancelBtn.setOnClickListener {
            cancelClickListener?.invoke()
            dismiss()
        }

    }

    class Builder {
        private var link = ""
        private var confirmClickListener: (() -> Unit)? = null
        private var cancelClickListener: (() -> Unit)? = null
        private var selectClickListener: (() -> Unit)? = null


        fun setConfirmClickListener(listener: () -> Unit): Builder {
            confirmClickListener = listener
            return this
        }


        fun setSelectClickListener(listener: () -> Unit): Builder {
            selectClickListener = listener
            return this
        }

        fun setLink(link: String): Builder {
            this.link = link
            return this
        }


        fun build(): ScanQRCodeDialog {
            val dialog = ScanQRCodeDialog()
            dialog.link = link
            dialog.confirmClickListener = confirmClickListener
            dialog.cancelClickListener = cancelClickListener
            dialog.selectClickListener = selectClickListener
            return dialog
        }
    }
}