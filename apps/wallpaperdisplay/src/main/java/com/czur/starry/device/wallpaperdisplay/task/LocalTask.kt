package com.czur.starry.device.wallpaperdisplay.task

import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV

import com.czur.starry.device.baselib.utils.toSizeStr
import com.czur.starry.device.wallpaperdisplay.bean.FileEntity
import com.czur.starry.device.wallpaperdisplay.manager.del.IDeleter
import com.czur.starry.device.wallpaperdisplay.manager.del.LocalDeleter
import com.czur.starry.device.wallpaperdisplay.manager.inter.CopyTask
import com.czur.starry.device.wallpaperdisplay.manager.inter.IComputeSumSize
import com.czur.starry.device.wallpaperdisplay.manager.inter.LocalComputeSumSize
import com.czur.starry.device.wallpaperdisplay.util.copyTo
import java.io.File


private const val TAG = "LocalTask"

class LocalTask : CopyTask, IComputeSumSize by LocalComputeSumSize(), IDeleter by LocalDeleter() {


    /**
     * 检测是否有同名文件,耗时操作
     */
    override suspend fun checkExist(src: FileEntity, destDir: FileEntity): Boolean {
        val dirFile = File(destDir.absPath)
        val destFile = File(dirFile, src.name) // 复制后的文件
        return destFile.exists()
    }

    /**
     * 执行复制操作
     * @param src       要复制的文件
     * @param destDir   目的地文件夹
     * @param size      src所占用的空间,为了子类计算进度使用
     * @param progressListener  用于更新进度,完成了的百分比 0-1之间
     */
    override suspend fun doCopy(
        src: FileEntity,
        destDir: FileEntity,
        size: Long,
        progressListener: (progress: Float) -> Unit,
    ) {
        logTagD(TAG, "本地->本地开始复制:${src.name}->${destDir.name}")

        val destFile = File(destDir.absPath)
//        val destFile = File(dirFile, destDir.name) // 复制后的文件

        val theFile = File(src.absPath)
        logTagD(TAG, "==destDir.absPath===" + destDir.absPath)
        logTagD(TAG, "==src.absPath===" + src.absPath)
        logTagV(TAG, "sumSize${size.toSizeStr()}")
        var finishSize = 0L


        logTagV(TAG, "开始复制")
        // 正常复制
        theFile.copyTo(destFile) {
            // 屏蔽掉之前代码中0的情况
            finishSize += it
            // 更新进度信息
            progressListener(finishSize.toFloat() / size)

        }

        logTagD(TAG, "本地->本地复制完成")
    }

}