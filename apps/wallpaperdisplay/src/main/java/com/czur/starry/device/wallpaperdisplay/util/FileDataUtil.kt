package com.czur.starry.device.wallpaperdisplay.util

import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.common.KEY_MEETING_REJECT_WELCOME_MODE
import com.czur.starry.device.baselib.common.KEY_MEETING_REJECT_WELCOME_PLAY
import com.czur.starry.device.baselib.common.KEY_WELCOME_MODE_TIME_OVER
import com.czur.starry.device.baselib.utils.getString
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.baselib.utils.prop.getStringSystemProp
import com.czur.starry.device.baselib.utils.prop.setBooleanSystemProp
import com.czur.starry.device.baselib.utils.prop.setStringSystemProp
import com.czur.starry.device.baselib.utils.randomStr
import com.czur.starry.device.wallpaperdisplay.App
import com.czur.starry.device.wallpaperdisplay.R
import com.czur.starry.device.wallpaperdisplay.bean.FileEntity
import com.czur.starry.device.wallpaperdisplay.bean.FileMode
import com.czur.starry.device.wallpaperdisplay.view.activity.ASSETS_CELEBRATE
import com.czur.starry.device.wallpaperdisplay.view.activity.ASSETS_TEAM
import com.czur.starry.device.wallpaperdisplay.view.activity.ASSETS_WELCOME
import com.czur.starry.device.wallpaperdisplay.view.vm.DisplayVM
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * created by wangh 22.0831
 */
private const val TAG = "FileDataUtil"

private const val TEMPLATE_ALL = 0 //全部模板
private const val TEMPLATE_WELCOME = 1//欢迎模板
private const val TEMPLATE_CELEBRATE = 2//庆祝模板
private const val TEMPLATE_TEAM_BUILD = 3//团建


//enum class WallPaperType { FIXED, CUSTOM, RECENT }
data class WallPaperListItem(
    val fileType: FileMode,
    val wallPaperEntity: FileEntity? = null,
)

//自定义标签
//assets下模板路径
const val RECENT_ASSETS = "recent"
const val CUSTOM_ASSETS = "custom"
const val MEET_ASSETS = "meet"
const val MIDDLE_LAYOUT = 0 //居中
const val LEFT_LAYOUT = 1   //左上
const val NULL_LAYOUT = 2   //默认不可编辑
const val TOP_LAYOUT = 3    //居上

const val DEFAULT_TEXT_WHITE = 0//默认白色字体
const val DEFAULT_TEXT_BLACK = 1//黑色字体
const val DEFAULT_TEXT_YELLOW = 2//黄色字体
const val DEFAULT_TEXT_BROWN = 3//棕色字体
const val DEFAULT_TEXT_BROWN_LIGHT = 4//浅棕色字体


val welcomeTitleList = listOf(
    getString(R.string.welcome_title),
    getString(R.string.welcome_title),
    getString(R.string.welcome_title),
    getString(R.string.welcome_title),
    getString(R.string.welcome_title),
    getString(R.string.career_title)
)

val welcomeContentList = listOf(
    getString(R.string.welcome1_content),
    getString(R.string.welcome1_content),
    getString(R.string.welcome1_content),
    getString(R.string.welcome1_content),
    getString(R.string.welcome1_content),
    getString(R.string.career_content)
)
val welcomeContent2List = listOf(
    getString(R.string.welcome_content2),
    getString(R.string.welcome1_content2),
    getString(R.string.welcome1_content2),
    getString(R.string.welcome2_content2),
    getString(R.string.welcome1_content2),
    getString(R.string.career_content2)
)
val celebrateTitleList = listOf(
    getString(R.string.celebrate_title),
    getString(R.string.celebrate_title),
    getString(R.string.celebrate_title),
    getString(R.string.celebrate_job_title),
    getString(R.string.celebrate_meeting_title),
    getString(R.string.birthday_title)
)
val celebrateContentList = listOf(
    getString(R.string.celebrate_content),
    getString(R.string.celebrate_content),
    getString(R.string.celebrate_success_content),
    getString(R.string.celebrate_job_content),
    getString(R.string.celebrate_meeting_content),
    getString(R.string.birthday_content),
)
val celebrateContent2List = listOf(
    getString(R.string.celebrate_content2),
    getString(R.string.celebrate_content2),
    getString(R.string.celebrate_success_content2),
    getString(R.string.celebrate_job_content2),
    getString(R.string.celebrate_job_content2),
    getString(R.string.birthday_content2)
)
val teamTitleList = listOf(
    getString(R.string.team_build_title),
    getString(R.string.team_build2_title),
    getString(R.string.team_train_title),
    getString(R.string.team_train_title2)
)
val teamContentList = listOf(
    getString(R.string.team_build_content),
    getString(R.string.team_build2_content),
    getString(R.string.team_train_content),
    getString(R.string.team_train_content3)
)
val teamContent2List = listOf(
    "",
    "",
    getString(R.string.team_train_content2),
    getString(R.string.team_train_content2)
)


//最近使用数据
suspend fun getNormalRecentData() = withContext(Dispatchers.IO) {
    val recentList = getValueToList(RECENT_ASSETS)
    recentList.sortByDescending { it.lastModifyTime }
    recentList
}

//最近使用数据
suspend fun getRecentData() = withContext(Dispatchers.IO) {
    val list = getNormalRecentData()
    val recentList = mutableListOf<WallPaperListItem>()
    recentList.addAll(
        list.map {
            WallPaperListItem(
                fileType = FileMode.RECENT,
                wallPaperEntity = it
            )
        }
    )
    recentList
}

// 获取存储时的自定义数据(FileEntity类型)
suspend fun getNormalCustomData() = withContext(Dispatchers.IO) {
    val list = getValueToList(CUSTOM_ASSETS)
    list
}

// 获取类型转换后的数据(WallPaperListItem类型)
suspend fun getCustomData() = withContext(Dispatchers.IO) {
    val list = getNormalCustomData()
    val customList = mutableListOf<WallPaperListItem>()
    customList.addAll(
        list.map {
            WallPaperListItem(
                fileType = FileMode.CUSTOM,
                wallPaperEntity = it
            )
        }
    )
    customList
}

// 获取自定义tag的数据，因为需要在首位添加一个空数据。
suspend fun getHasEmptyCustomData() = withContext(Dispatchers.IO) {
    val list = getCustomData().toMutableList()
    list.add(
        0, WallPaperListItem(
            fileType = FileMode.CUSTOM,
            wallPaperEntity = null
        )
    )
    list
}

// 获取固定模版数据数据(FileEntity类型)
suspend fun getNormalFixedData() = withContext(Dispatchers.IO) {
    val list = getValueToList(MEET_ASSETS)
    list
}

// 获取固定模版数据数据(FileEntity类型)
suspend fun getFixedData() = withContext(Dispatchers.IO) {
    val list = getNormalFixedData()
    val fixedList = mutableListOf<WallPaperListItem>()
    fixedList.addAll(
        list.map {
            WallPaperListItem(
                fileType = FileMode.MEETING,
                wallPaperEntity = it
            )
        }
    )
    fixedList
}

suspend fun getAllData() = withContext(Dispatchers.IO) {
    val newEntityList = mutableListOf<WallPaperListItem>()
    newEntityList.addAll(getCustomData())
    newEntityList.addAll(getFixedData())
    newEntityList
}

//获取固定模板
suspend fun getCurrentTagWithData(index: Int = 0) = withContext(Dispatchers.IO) {
    val wallpaperList = getAllData()
    var list = mutableListOf<WallPaperListItem>()

    when (index) {
        TEMPLATE_ALL -> {
            list = wallpaperList.toMutableList()
        }

        TEMPLATE_WELCOME -> {//欢迎
            list = wallpaperList.filter {
                it.wallPaperEntity?.absPath!!.contains(ASSETS_WELCOME)
            }.toMutableList()
        }

        TEMPLATE_CELEBRATE -> {//庆祝
            list = wallpaperList.filter {
                it.wallPaperEntity?.absPath!!.contains(ASSETS_CELEBRATE)
            }.toMutableList()
        }

        TEMPLATE_TEAM_BUILD -> {//团建
            list = wallpaperList.filter {
                it.wallPaperEntity?.absPath!!.contains(ASSETS_TEAM)
            }.toMutableList()

        }

    }
    list
}

//删除指定图片信息
suspend fun removeDataLocal(fileEntity: FileEntity) = withContext(Dispatchers.IO) {
    val list = getValueToList(CUSTOM_ASSETS)
    val data = list.toMutableList()
    data.forEach {
        if (it.absPath == fileEntity.absPath && it.lastModifyTime == fileEntity.lastModifyTime) {
            list.remove(it)
        }
    }
    setValueToString(CUSTOM_ASSETS, list)
}

//添加最近使用
suspend fun saveRecentData(data: FileEntity) = withContext(Dispatchers.IO) {
    val list = getValueToList(RECENT_ASSETS)
    list.add(data)
    var entity: FileEntity? = null
    if (list.size > 4) {
        entity = list[0]
        list.removeAt(0)
    }
    setValueToString(RECENT_ASSETS, list)
    entity
}

//删除最近图片信息
suspend fun removeRecentData(data: FileEntity) = withContext(Dispatchers.IO) {
    val list = getNormalRecentData()
    val dataList = list.toMutableList()
    dataList.forEach {
        if (it.absPath == data.absPath && it.lastModifyTime == data.lastModifyTime) {
            list.remove(data)
        }
    }
    setValueToString(RECENT_ASSETS, list)
}

//更新指定图片信息
suspend fun updateDataLocal(tag: String = CUSTOM_ASSETS, data: FileEntity) =
    withContext(Dispatchers.IO) {
        val list = getValueToList(tag)
        list.forEach {
            if (data.name == it.name && data.lastModifyTime == it.lastModifyTime) {
                it.musicName = data.musicName
                it.musicPath = data.musicPath
                it.textFist = data.textFist
                it.textSecond = data.textSecond
                it.textThird = data.textThird
                it.textMode = data.textMode
            }
        }
        setValueToString(tag, list)
    }

//添加自定义模板
suspend fun addDataLocal(data: FileEntity) = withContext(Dispatchers.IO) {
    val list = getValueToList(CUSTOM_ASSETS)
    list.add(data)
    setValueToString(CUSTOM_ASSETS, list)
}

/**
 * 判断模板列表里是否存在被其他列表使用情况
 * 1.最近使用列表有自定义，则自定义删除，本地图片不能删除
 * 2.删除最近使用，自定义列表不存在则删除本地图片
 * 两种情况不包含自定义和最近使用中用的官方模板情况
 */
suspend fun templateContainSize(fileEntity: FileEntity, tempMode: String = RECENT_ASSETS) =
    withContext(Dispatchers.IO) {
        val list = getValueToList(tempMode)
        val current = list.filter {
            (it.absPath == fileEntity.absPath)
        }.toMutableList()
        current.size
    }


suspend fun deleteLocalFile(path: String) = withContext(Dispatchers.IO) {
    try {
        val file = File(path)
        if (file.exists())
            file.delete()
    } catch (e: Exception) {
        e.printStackTrace()
    }
}


//初始化本地模板
suspend fun initTemplateData(tag: String, templateList: MutableList<FileEntity>) =
    withContext(Dispatchers.IO) {
        val nameList = getAssetsFileNames(tag)
        when (tag) {
            ASSETS_WELCOME -> {
                val names = App.app.resources.getStringArray(R.array.welcome_name_items)
                for (i in 0 until nameList.size) {
                    var layoutMode = MIDDLE_LAYOUT
                    var textColor = DEFAULT_TEXT_WHITE
                    if (i == nameList.size - 1) layoutMode = LEFT_LAYOUT
                    if (i == 4) textColor = DEFAULT_TEXT_BLACK
                    templateList.add(
                        FileEntity(
                            absPath = "$tag/${nameList[i]}",
                            name = names[i],
                            textFist = welcomeTitleList[i],
                            textSecond = welcomeContentList[i],
                            textThird = welcomeContent2List[i],
                            textMode = layoutMode,
                            texTag = textColor,
                        )
                    )
                }
            }

            ASSETS_CELEBRATE -> {
                val names = App.app.resources.getStringArray(R.array.celebrate_name_items)
                for (i in 0 until nameList.size) {
                    var layoutMode = LEFT_LAYOUT
                    var textColor = DEFAULT_TEXT_WHITE
                    if (i == nameList.size - 1) layoutMode = MIDDLE_LAYOUT
                    if (i == 3) textColor = DEFAULT_TEXT_BLACK
                    if (i == nameList.size - 1) textColor = DEFAULT_TEXT_YELLOW
                    templateList.add(
                        FileEntity(
                            absPath = "$tag/${nameList[i]}",
                            name = names[i],
                            textFist = celebrateTitleList[i],
                            textSecond = celebrateContentList[i],
                            textThird = celebrateContent2List[i],
                            texTag = textColor,
                            textMode = layoutMode
                        )
                    )
                }
            }

            ASSETS_TEAM -> {
                val names = App.app.resources.getStringArray(R.array.team_name_items)
                for (i in 0 until nameList.size) {
                    var layoutMode = LEFT_LAYOUT
                    var textColor = DEFAULT_TEXT_WHITE
                    if (i <= 1) layoutMode = TOP_LAYOUT
                    if (i == 0) textColor = DEFAULT_TEXT_BROWN
                    if (i == 2) textColor = DEFAULT_TEXT_BLACK
                    if (i == 3) textColor = DEFAULT_TEXT_BROWN_LIGHT
                    templateList.add(
                        FileEntity(
                            absPath = "$tag/${nameList[i]}",
                            name = names[i],
                            textFist = teamTitleList[i],
                            textSecond = teamContentList[i],
                            textThird = teamContent2List[i],
                            texTag = textColor,
                            textMode = layoutMode
                        )
                    )
                }
            }
        }
        templateList
    }

suspend fun saveTempData(templateList: MutableList<FileEntity>) = withContext(Dispatchers.IO) {
    setValueToString(MEET_ASSETS, templateList)
}

//是否设置了屏保开启
fun getSystemProp(): Boolean {
    return getBooleanSystemProp(KEY_MEETING_REJECT_WELCOME_MODE, false)
}

fun setSystemProp(para: Boolean) {
    return setBooleanSystemProp(KEY_MEETING_REJECT_WELCOME_MODE, para)
}

//正在播放 手动对焦不可进入
fun setPlaySystemProp(para: Boolean) {
    return setBooleanSystemProp(KEY_MEETING_REJECT_WELCOME_PLAY, para)
}

//获取是否正在播放屏保
fun getPlaySystemProp(): Boolean {
    return getBooleanSystemProp(KEY_MEETING_REJECT_WELCOME_PLAY, false)
}

//设置结束时间
fun setTimeSystemProp(para: Long) {
    setStringSystemProp(KEY_WELCOME_MODE_TIME_OVER, para.toString())
}

fun getTimeSystemProp(): String {
    return getStringSystemProp(KEY_WELCOME_MODE_TIME_OVER, "")
}

fun getDisplayTimeFormat2(second: Long): String {
    val timeString = StringBuilder()
    val hh = (second / 3600)
    val mm = second % 3600 / 60
    val tmm: String = if (mm < 10) "0$mm" else mm.toString()
    val thh: String = if (hh < 10) "0$hh" else hh.toString()
    if (thh.toInt() > 0) {
        timeString.append("$thh${getString(R.string.str_palytime_hour)}")
    }
    if (tmm.toInt() > 0) {
        timeString.append("$tmm${getString(R.string.str_palytime_min)}")
    }
    if ((thh.toInt() <= 0) && (tmm.toInt() <= 0)) {
        timeString.append("$second${getString(R.string.str_palytime_sec)}")
    }
    timeString.append(getString(R.string.str_palytime_auto_stop))
    return timeString.toString()
}

suspend fun copyFileToLocal(it: FileEntity, localFilePath: String = DisplayVM.rootDir.path) =
    withContext(Dispatchers.IO) {
        logTagD(TAG, "copyToLocal:${it.absPath}")
        //复制图片
        val name = getName(getNormalCustomData())
        val cName =
            "${randomStr()}.${it.name.substringAfterLast('.', "")}"
        val desc =
            FileEntity(
                absPath = "${localFilePath}/$cName",
                name = name,
                fileType = it.fileType
            )
        logTagD(TAG, "======absPath==${desc.absPath}")
        logTagD(TAG, "======copyToLocal==${desc.name}")
        doCopy(it, desc)
    }

private suspend fun doCopy(
    srcFile: FileEntity,
    targetFile: FileEntity,
) = withContext(Dispatchers.IO) {
    val copyTask = copyTask(srcFile)
    val sumSizeMap = copyTask.computeSumSize(arrayListOf(srcFile))
    copyTask.doCopy(
        srcFile,
        targetFile,
        sumSizeMap[srcFile.absPath] ?: 0L,
    ) {
    }
    targetFile
}

