package com.czur.starry.device.wallpaperdisplay.view.fragment

import android.os.Bundle
import androidx.fragment.app.viewModels
import com.bumptech.glide.Glide
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.file.filelib.ShareFile
import com.czur.starry.device.wallpaperdisplay.databinding.SelectImageFragmentBinding
import com.czur.starry.device.wallpaperdisplay.view.vm.DisplayVM

/**
 * created by wangh 22.1229
 */


class SelectImageFragment : CZViewBindingFragment<SelectImageFragmentBinding>() {

    private val displayVM: DisplayVM by viewModels({ requireActivity() })
    private val floatParent: FileChooseFloatFragment by lazy {
        requireParentFragment() as FileChooseFloatFragment
    }
    companion object {
        private lateinit var shareFile: ShareFile
    }

    override fun SelectImageFragmentBinding.initBindingViews() {

        closeIv.setOnClickListener {
            floatParent.dismiss()
        }

        backIv.setOnClickListener {
            floatParent.childFragmentManager.popBackStack()
        }

        selectTv.setOnClickListener {
            try {
                floatParent.dismiss()
                displayVM.updateShareFile(shareFile)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        shareFile = displayVM.requireSelFile()
        displayVM.selFile.observe(this) { preFile ->
            preFile?.let {
                launch {
                    binding.imgProgress.show()
                    val uri = displayVM.getOpenUri(it)
                    Glide.with(this@SelectImageFragment)
                        .load(uri)
                        .into(binding.photoView)
                    binding.imgProgress.gone()
                }

            }
        }
    }

}