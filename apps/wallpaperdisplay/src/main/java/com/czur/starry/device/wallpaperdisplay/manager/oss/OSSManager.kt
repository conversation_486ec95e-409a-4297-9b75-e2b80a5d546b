package com.czur.starry.device.wallpaperdisplay.manager.oss

import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.czurutils.log.logTagE
import android.util.Log
import com.alibaba.sdk.android.oss.OSS
import com.alibaba.sdk.android.oss.model.GetObjectRequest
import com.alibaba.sdk.android.oss.model.ListObjectsRequest
import com.alibaba.sdk.android.oss.model.OSSObjectSummary
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.network.core.common.ResCode
import com.czur.starry.device.baselib.network.core.exception.MiaoHttpException

import com.czur.starry.device.wallpaperdisplay.bean.FileEntity
import com.czur.starry.device.wallpaperdisplay.util.OSSPathUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.isActive
import kotlinx.coroutines.withContext
import okhttp3.internal.closeQuietly
import java.io.File

/**
 * created by wangh 22.0322
 */


object OSSManager {
    private const val TAG = "OSSManager"
    private var _bucketName = UserHandler.bucketName
    private const val PART_COUNT = 100 * 1024L // 分片大小  阿里云下限是100k, 小一点比较不容易失败
    val ossService: IOSSService by lazy { HttpManager.getService() }

    private val ossInstance by lazy {
        OSSInstance()
    }
    private val fileOss: OSS?
        get() = ossInstance.getOSS()
    /**
     * 从OSS下载文件
     * @param src OSS 上的文件
     * @param destDir 本地路径
     * @param forCache 本次下载是否是为了数据缓存
     */
    suspend fun download(
        src: FileEntity,
        destDir: FileEntity,
        forCache: Boolean = false,
        listener: ((finishSize: Long) -> Unit)? = null,
    ): String = withContext(Dispatchers.IO) {
        logTagD(TAG, "下载OSS文件")
        val basePath = src.parentPath
        val baseLocalFile = File(destDir.absPath) // 这次要复制的本地路径

        // 获取要下载的文件列表
        val fileSummaries = getSubFileSummary(src.absPath)
        if (forCache && fileSummaries.size == 1) {
            Log.d(TAG,"======60")
            // 如果是forCache的, 只能是下载一个文件,不能是整个文件夹
            return@withContext downloadCache(fileSummaries[0], destDir, basePath)
        }

        // 下载非缓存文件
        val downloadKeys = fileSummaries.map { it.key }
        // 进度信息
      /*  val progressListener = listener?.let {
            SizeListener<GetObjectRequest>(it)
        }*/

        val taskList = downloadKeys.map { ossKey ->
            async {
                Log.d(TAG,"======74")
                val get = GetObjectRequest(getBucketName(), ossKey)
               /* progressListener?.let {
                    get.setProgressListener(it)
                }*/
                val getResult = fileOss!!.getObject(get)
                // 创建本地文件
                // 要创建文件的相对路径,如果是文件,则是文件名 如果是路径,则是文件夹名字并且后面还带/
                val relativePaths = ossKey.substringAfter(basePath)
                val file = File(baseLocalFile, relativePaths)
                if (relativePaths.endsWith("/")) {
                    // 这是一个文件夹
                    logTagD(TAG, "在本地创建文件夹:${file.absolutePath}")
                    file.mkdirs()
                } else {
                    // 这是一个文件
                    // 先创建父文件夹
                    val parent = file.parentFile
                    parent?.let { p ->
                        if (!p.exists()) {
                            logTagV(TAG, "创建父文件夹")
                            p.mkdirs()
                        }
                    }
                    logTagV(TAG, "复制文件")

                    // 下载
                    if (file.exists()) {
                        // 如果文件存在, 则将文件删除
                        // 防止写出错
                        file.delete()
                    }
                    // 获取输入流
                    val length = getResult.contentLength
                    val buffer = ByteArray(2048)
                    var readCount = 0
                    val fos = file.outputStream()
                    try {
                        while (readCount < length) {
                            if (isActive) {
                                val read = getResult.objectContent.read(buffer, 0, buffer.size)
                                fos.write(buffer, 0, read)
                                readCount += read
                            } else {
                                logTagD(TAG, "下载被取消")
                                file.deleteRecursively()
                                break
                            }
                        }
                    } catch (exp: Exception) {
                        logTagD(TAG, "出错后删除临时文件")
                        file.deleteRecursively()
                        fos.closeQuietly()
                        throw exp
                    }
                    fos.closeQuietly()

                }

                file.absolutePath
            }
        }

        // 等待下载完成
        taskList.forEach {
            // 等待所有协程完成
            val localPath = it.await()
            logTagV(TAG, "下载完成:${localPath}")
        }

        logTagD(TAG, "所有下载请求全部完成")
        // 可能下载的是文件夹,所以重新计算一次路径返回
        val destFile = File(baseLocalFile, src.absPath.substringAfter(basePath))
        if (!isActive) {
            // 删除掉所有的临时文件
            destFile.deleteRecursively()
            ""
        } else {
            destFile.absolutePath
        }
    }


    /**
     * 获取OSS上的信息
     * @param prefix 前缀, 就是文件路径
     */
    private suspend fun getSubFileSummary(
        prefix: String,
    ): List<OSSObjectSummary> {
        logTagD(TAG, "getSubFileSummary,prefix:${prefix}")
        val bucketName = getBucketName()
        val summaryList = mutableListOf<OSSObjectSummary>()
        var nextMarker: String? = null
        do {
            val listObjectsRequest = ListObjectsRequest(
                bucketName, prefix, nextMarker, null, null
            )
            logTagD(TAG,"====172=")
            val objectListing = fileOss!!.listObjects(listObjectsRequest)
            if (objectListing.objectSummaries.size > 0) {
                for (s in objectListing.objectSummaries) {
                    logTagD(TAG, "key name: " + s.key)
                    summaryList.add(s)
                }
            }
            nextMarker = objectListing.nextMarker
        } while (objectListing.isTruncated)
        return summaryList
    }

    private suspend fun getBucketName(): String {
        if (_bucketName.isEmpty()) {
            logTagW(TAG, "Bucket_name为空, 尝试重新获取BucketName")
            UserHandler.updateUserInfo()
            _bucketName = UserHandler.bucketName
            if (_bucketName.isEmpty()) {
                logTagE(TAG, "重新获取BUCKET_NAME,仍然为空!!")
                throw MiaoHttpException(ResCode.RESULT_CODE_NO_LOGIN, "bucketName为空, 可能是没有登录")
            }
        }
        Log.d(TAG,"=UserHandler.bucketName="+UserHandler.bucketName)
        return _bucketName
    }
    /**
     * 下载缓存文件
     */
    private suspend fun downloadCache(
        src: OSSObjectSummary,
        destDir: FileEntity,
        basePath: String,
    ): String {
        // eTag 会有引号
        val eTag = src.eTag.removeSurrounding("\"")
        val extension = src.key.substringAfter(basePath)
            .substringAfterLast('.', "")
        // 使用Etag作为缓存图片的文件名
        val cacheFile = File(destDir.absPath, "${eTag}.${extension}")
        if (cacheFile.exists() && cacheFile.length() == src.size) {
            // 缓存文件存在
            logTagV(TAG, "缓存文件:${cacheFile.name}存在, 跳过下载")
            return cacheFile.absolutePath
        }
        logTagV(TAG, "下载缓存文件")
        if (cacheFile.exists()) {
            logTagV(TAG, "删除之前未完成的缓存文件")
            cacheFile.delete()
        }
        return withContext(Dispatchers.IO) {
            val get = GetObjectRequest(getBucketName(), src.key)
            val getResult = fileOss!!.getObject(get)
            val inputStream = getResult.objectContent

            val cacheDir = File(destDir.absPath)
            if (!cacheDir.exists()) {
                cacheDir.mkdirs()
            }

            val fos = cacheFile.outputStream()
            logTagV(TAG, "开始下载缓存文件")
            inputStream.use {
                it.copyTo(fos)
            }
            fos.closeQuietly()
            cacheFile.absolutePath
        }
    }

    /**
     * 获取全部文件列表
     */
    suspend fun getAllCloudFilePath(fileEntities: List<FileEntity>): List<String> {
        val pathUtil = OSSPathUtil(fileOss!!, getBucketName())
        return pathUtil.getAllCloudFilePath(fileEntities)
    }

    /**
     * 获取文件大小
     */
    suspend fun getFileLength(fileEntity: FileEntity): Long = withContext(Dispatchers.IO) {
        logTagD(TAG, "getFileLength")
        if (!fileEntity.isDir()) {
            // 如果是文件,那么文件大小是直接写在fileEntity中了
            // 就不再去请求OSS了, 这样比较快
            fileEntity.fileSize
        } else {
            getSubFileSummary(fileEntity.absPath).sumOf {
                it.size
            }
        }

    }

}