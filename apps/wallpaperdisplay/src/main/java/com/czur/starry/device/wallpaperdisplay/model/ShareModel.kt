package com.czur.starry.device.wallpaperdisplay.model

import android.app.Application
import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import com.czur.starry.device.file.filelib.AccessType
import com.czur.starry.device.file.filelib.FileHandler

/**
 * Created by 陈丰尧 on 2022/1/4
 * 用来存储屏幕分享时的一些信息
 *
 * 1. 记录排序规则:不同文件类型(本地, 云端, USB)分别记录自己的排序规则,
 *               用户选择的排序规则会被记录下来,
 *               会议结束后, 记录的排序会重置为默认规则
 */
class ShareModel(private val app: Application) : Model() {
    companion object {
        private val DEF_SORT = FileHandler.SortType.TIME_DESC
    }

    /**
     * 排序的缓存
     */
    private val sortMap: MutableMap<AccessType, FileHandler.SortType> = mutableMapOf()

    /**
     * 获取排序规则
     */
    fun getSortType(belongTo: AccessType): FileHandler.SortType {
        return sortMap.getOrPut(belongTo) {
            DEF_SORT
        }
    }


    /**
     * 存储排序规则
     */
    fun setSortType(belongTo: AccessType, sortType: FileHandler.SortType) {
        sortMap[belongTo] = sortType
    }

    override fun doClear() {
        sortMap.clear()
    }
}