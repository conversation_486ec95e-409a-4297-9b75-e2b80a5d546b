package com.czur.starry.device.wallpaperdisplay.view.fragment

import android.os.Bundle
import android.view.View
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.utils.closeDefChangeAnimations
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.invisible
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.file.filelib.ShareFile
import com.czur.starry.device.wallpaperdisplay.adapter.FileChooseAdapter
import com.czur.starry.device.wallpaperdisplay.databinding.FloatFileListBinding
import com.czur.starry.device.wallpaperdisplay.util.showSortPop
import com.czur.starry.device.wallpaperdisplay.view.vm.DisplayVM


class FileListFragment : CZViewBindingFragment<FloatFileListBinding>() {

    companion object {
        private const val TAG = "FileListFragment"
    }

    private val shareFileVM: DisplayVM by viewModels({ requireActivity() })

    private val floatParent: FileChooseFloatFragment by lazy {
        requireParentFragment() as FileChooseFloatFragment
    }

    private val adapter = FileChooseAdapter()

    override fun FloatFileListBinding.initBindingViews() {
        shareFileVM.isSelectLive.observe(this@FileListFragment) {
            if (!it) {
                adapter.clearSel()
            }
        }
        // dismiss
        closeIv.setOnClickListener {
            floatParent.dismiss()
        }

        shareFileRv.layoutManager = LinearLayoutManager(requireContext())
        shareFileRv.adapter = adapter
        shareFileRv.closeDefChangeAnimations()
        adapter.currentShareFile = shareFileVM.shareFile

        shareFileRv.doOnItemClick { vh, view ->
            val pos = vh.bindingAdapterPosition
            val data = adapter.getData(pos)
            doClickItem(pos, data)
            true
        }

        // 离开当前文件夹
        shareFileBackIv.setOnClickListener {
            adapter.clearSel()
            shareFileVM.leaveFolder()
        }

        // 显示预览页面
        previewTv.setOnClickListener {
            floatParent.showPreviewFragment()
        }

        // 排序按钮
        shareFileSortIv.setOnClickListener {
            showSortPop(requireContext(), it, shareFileVM.currentSortType) { sortType ->
                shareFileVM.changeSortType(sortType) { newSelIndex ->
                    adapter.selPos = newSelIndex
                }
            }
        }


        // 选择
        selectTv.setOnClickListener {
            try {
                selectTv.isEnabled = false
                floatParent.dismiss()
                // 方法内部维护了分享的文件
                shareFileVM.updateShareFile(shareFileVM.requireSelFile())
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }


    }

    private fun doClickItem(pos: Int, data: ShareFile): Boolean {


        if (data.canEnter) {
            shareFileVM.enterFolder(data)
            adapter.clearSel()
            return true
        }

        shareFileVM.selFile(data)
        // 选中UI
        adapter.selPos = pos

        return true
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        shareFileVM.fileList.observe(this) {
            if (it.isEmpty()) {
                binding.emptyViewGroup.show()
            } else {
                binding.emptyViewGroup.invisible()
            }
            adapter.dataList = it
            binding.shareFileRv.scrollToPosition(0)

        }

        shareFileVM.title.observe(this) {
            binding.shareFileTitleTv.text = it
        }

        shareFileVM.folderActionGroupShow.observe(this) {
            binding.folderVisibilityGroup.visibility = if (it) View.VISIBLE else View.GONE
        }

        shareFileVM.showActionGroup.observe(this) {
            if (it) {
                binding.actionFlow.show()
            } else {
                binding.actionFlow.invisible()
            }
        }
    }

    override fun onResume() {
        super.onResume()
        adapter.clearSel()
        shareFileVM.updateFolder()
    }
}