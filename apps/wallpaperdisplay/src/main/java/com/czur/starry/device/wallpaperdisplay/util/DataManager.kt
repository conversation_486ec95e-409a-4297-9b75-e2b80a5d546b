package com.czur.starry.device.wallpaperdisplay.util

import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.doublePreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.floatPreferencesKey
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import com.czur.starry.device.baselib.NotImpl
import com.czur.starry.device.wallpaperdisplay.App
import com.czur.starry.device.wallpaperdisplay.bean.FileEntity
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext

/**
 * created by wangh 22.1205
 */


private val gson by lazy { Gson() }

/**
 * 设置值
 */
suspend inline fun <reified T> setDSValue(key: String, value: T) {
    val stringKey = getDSKey<T>(key)
    App.app.dataStore.edit {
        it[stringKey] = value
    }
}

/**
 * 获取值
 */
suspend inline fun <reified T> getDSValue(key: String, defValue: T = dsDefValue()): T {
    val dsKey = getDSKey<T>(key)
    return App.app.dataStore.data.map {
        it[dsKey] ?: defValue
    }.first()
}

inline fun <reified T> dsDefValue(): T {
    return when (T::class.java) {
        String::class.java -> ""
        Integer::class.java, java.lang.Integer::class.java -> 0
        Long::class.java, java.lang.Long::class.java -> 0L
        Float::class.java, java.lang.Float::class.java -> 0F
        Double::class.java, java.lang.Double::class.java -> .0
        Boolean::class.java, java.lang.Boolean::class.java -> false
        else -> NotImpl()
    } as T
}

inline fun <reified T> getDSKey(key: String): Preferences.Key<T> {
    return when (T::class.java) {
        String::class.java -> {
            stringPreferencesKey(key)
        }
        Integer::class.java, java.lang.Integer::class.java -> {
            intPreferencesKey(key)
        }
        Long::class.java, java.lang.Long::class.java -> {
            longPreferencesKey(key)
        }
        Float::class.java, java.lang.Float::class.java -> {
            floatPreferencesKey(key)
        }
        Double::class.java, java.lang.Double::class.java -> {
            doublePreferencesKey(key)
        }
        Boolean::class.java, java.lang.Boolean::class.java -> {
            booleanPreferencesKey(key)
        }
        else -> NotImpl()
    } as Preferences.Key<T>
}

suspend fun getValueToList(key:String) = withContext(Dispatchers.IO) {
    val jsonStr = getDSValue(key,"")
    if (jsonStr!!.isNotEmpty()) {
        val list = gson.fromJson<List<FileEntity>>(
            jsonStr,
            object : TypeToken<List<FileEntity>>() {}.type
        )
        list as MutableList<FileEntity>
    } else {
        mutableListOf()
    }

}

suspend fun setValueToString(key: String,list: MutableList<FileEntity>) = withContext(Dispatchers.IO) {
    val value = gson.toJson(list)
    setDSValue(key, value)
}


