package com.czur.starry.device.wallpaperdisplay.view.vm

import android.app.Application
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.map
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.appContext
import com.czur.starry.device.baselib.utils.data.LiveDataDelegate
import com.czur.starry.device.baselib.utils.data.NullableLiveDataDelegate
import com.czur.starry.device.baselib.utils.data.ReadOnlyLiveDataDelegate
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.getString
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.file.filelib.AccessType
import com.czur.starry.device.file.filelib.FileHandler
import com.czur.starry.device.file.filelib.FileHandler.getItemsByTarget
import com.czur.starry.device.file.filelib.FileHandler.getOpenUrl
import com.czur.starry.device.file.filelib.FileHandler.getRoots
import com.czur.starry.device.file.filelib.FileType
import com.czur.starry.device.file.filelib.ShareFile
import com.czur.starry.device.wallpaperdisplay.App
import com.czur.starry.device.wallpaperdisplay.R
import com.czur.starry.device.wallpaperdisplay.model.ModelManager
import com.czur.starry.device.wallpaperdisplay.model.ShareModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import java.io.File
import java.util.Stack
import kotlin.collections.set

/**
 * created by wangh 22.0727
 */


class DisplayVM(application: Application) : AndroidViewModel(application) {
    companion object {
        private const val TAG = "DisplayVM"

        //保存自定义图片根目录
        val rootDir = File(App.app.filesDir, "LocalImageRoot")
            get() {
                if (!field.exists()) {
                    field.mkdirs()
                }
                return field
            }
    }

    enum class ShareType(val needToken: Boolean, val shareDirect: Boolean) {
        FILE_DOCUMENT(true, false),// 分享文件(文档文件)
        FILE_LOCAL_PDF(true, false),// 分享文件(本地pdf)
        FILE_IMG(true, false),  // 分享文件(视频文件)
        NONE(false, true);// 没有分享
    }

    //格式图片或者音乐
    var listType = mutableListOf<FileType>()

    val isSelectLive = DifferentLiveData(false)
    private var isSelect by LiveDataDelegate(isSelectLive)
        private set

    private var shareUid: String = ""


    // 文件夹返回栈
    private val fileStackLive = MutableLiveData<Stack<ShareFile>>(Stack())
    private val fileStack: Stack<ShareFile> by ReadOnlyLiveDataDelegate(fileStackLive)

    // 文件列表
    val fileList = MediatorLiveData<List<ShareFile>>()


    // 标题
    val title = fileStackLive.map {
        if (it.isEmpty()) {
            getString(R.string.tv_choose_folder)
        } else {
            it.peek().name
        }
    }

    // 文件夹里的操作按钮的可见性
    val folderActionGroupShow = fileStackLive.map {
        it.isNotEmpty()
    }

    // 选中的文件
    val selFile: MutableLiveData<ShareFile?> = MutableLiveData()

    // 共享的图片
    val shareFileLive: LiveData<ShareFile> = MutableLiveData()
    var shareFile by NullableLiveDataDelegate(shareFileLive, null)
        private set

    // 选择的音乐
    val shareMusicLive: LiveData<ShareFile?> = MutableLiveData()
    var shareMusic by NullableLiveDataDelegate(shareMusicLive, null)
        private set


    val shareTypeLive = DifferentLiveData(ShareType.NONE)
    var shareType: ShareType by LiveDataDelegate(shareTypeLive, ShareType.NONE)
        private set

    val showActionGroup = selFile.map { it != null }


    /**
     * 记录分享数据
     */
    private val shareModel: ShareModel by lazy { ModelManager.shareModel }

    /**
     * 当前文件夹属于什么
     */
    private val currentAccessType: AccessType
        get() {
            val stack = fileStackLive.value!!
            return stack.peek().belongTo
        }

    /**
     * 当前文件夹的排序规则
     */
    val currentSortType: FileHandler.SortType
        get() = shareModel.getSortType(currentAccessType)


    val scanUSBJobMap = mutableMapOf<String, Job?>()


    /**
     * USB插拔广播
     */
    private val usbReceiver = USBReceiver()


    private var watchSelfSharingJob: Job? = null

    // 欢庆屏保只固定支持这种格式
    private val imageList = listOf("jpg", "png", "jpeg")


    init {
        fileList.addSource(fileStackLive) { loadData() }


        // 注册USB广播
        registerUSBReceiver()

        shareTypeLive.observeForever {
            if (it.needToken) {
//                registerSelfStatusObserver()
            } else {
                unRegisterSelfStatusObserver()
            }
        }
    }

    /**
     * 开始分享选择
     */
    fun startSelect() {
        isSelect = true
    }

    /**
     * 结束分享选择
     */
    fun stopSelect() {
        isSelect = false
    }

    /**
     * 获取格式过滤
     */
    fun getFileType(): List<Any> {

        return listType
    }


    /**
     * 设置格式过滤
     */
    fun setFileType(type: FileType) {
        listType = mutableListOf<FileType>(FileType.ROOT, type, type, FileType.FOLDER)
    }


    private fun unRegisterSelfStatusObserver() {
        watchSelfSharingJob?.cancel()
    }

    /**
     * 设置共享文件
     * @param shareFile: 要共享的文件
     */
    fun updateShareFile(shareFile: ShareFile) {
        logTagD(TAG, "updateShareFile:${shareFile}")
        if (shareFile.fileType == FileType.IMAGE) {
            this.shareFile = shareFile
        } else if (shareFile.fileType == FileType.AUDIO) {
            this.shareMusic = shareFile
        }
    }

    /**
     * 清除共享文件记录
     */
    fun clearShareFile() {
        this.shareFile = null
    }

    /**
     * 注册USB广播
     */
    private fun registerUSBReceiver() {
        logTagV(TAG, "注册USB插拔广播")
        val filter = IntentFilter().apply {
            addAction(Intent.ACTION_MEDIA_UNMOUNTED)
            addAction(Intent.ACTION_MEDIA_MOUNTED)
            addDataScheme("file")
        }
        appContext.registerReceiver(usbReceiver, filter)
    }

    /**
     * 取消注册USB广播
     */
    private fun unRegisterUSBReceiver() {
        doWithoutCatch {
            logTagV(TAG, "取消注册USB插拔广播")
            appContext.unregisterReceiver(usbReceiver)
        }
    }


    /**
     * 获取选中的文件
     * @return 当前分享的文件
     */
    fun requireSelFile(): ShareFile {
        return selFile.value!!
    }

    /**
     * 进入某一个文件夹
     * @param folder: 要进入的文件夹
     */
    fun enterFolder(folder: ShareFile) {
        val stack = fileStackLive.value!!
        stack.push(folder)
        fileStackLive.value = stack
    }

    /**
     * 更新当前目录下文件
     */
    fun updateFolder() {
        val stack = fileStackLive.value!!
        fileStackLive.value = stack
    }

    /**
     * 离开文件夹
     */
    fun leaveFolder() {
        val stack = fileStackLive.value!!
        if (stack.isNotEmpty()) {
            stack.pop()
            fileStackLive.value = stack
        }
    }

    /**
     * 设置选中的文件
     * @param file 选中的文件
     */
    fun selFile(file: ShareFile) {
        selFile.value = file
    }

    /**
     * 加载数据
     */
    private fun loadData(onLoadFinish: ((selPos: Int) -> Unit)? = null) {
        // 每次重新加载数据时都清空 选中的文件
        if (onLoadFinish == null) {
            selFile.value = null
        }
        launch {
            val stack = fileStackLive.value!!
            val shareFiles = if (stack.isEmpty()) {
                val list = getRoots()
                list.firstOrNull {
                    it.belongTo == AccessType.USB
                }?.let {
                    startScanUSBSpace(File(it.absPath))
                } ?: kotlin.run {
                    stopScanUSBSpace(null)
                }
                list
            } else {
                getItemsByTarget(stack.peek(), sortType = currentSortType)
            }
            logTagD(TAG, "shareFiles:${shareFiles}")

            //过滤图片格式
            // 2. 显示root,文件夹和图片文件
            // 3. 只显示png/jpg的图片文件, 其他不显示
            val showList = shareFiles.filter {
                (it.fileType == FileType.ROOT ||
                        it.fileType == FileType.FOLDER ||
                        (it.fileType == FileType.IMAGE && it.name.substringAfterLast(
                            '.',
                            ""
                        ).lowercase() in imageList))

            }
            fileList.value = showList

            onLoadFinish?.let {
                // 重新计算选中的位置
                val currentSelPath = selFile.value?.absPath ?: ""
                if (currentSelPath.isEmpty()) {
                    it(-1)
                } else {
                    val index = showList.indexOfFirst { showFile ->
                        showFile.absPath == currentSelPath
                    }
                    it(index)
                }
            }
        }
    }

    /**
     * 改变排序方式
     */
    fun changeSortType(sortType: FileHandler.SortType, onLoadFinish: ((selPos: Int) -> Unit)) {
        logTagD(TAG, "changeSortType")
        val belongTo = currentAccessType
        shareModel.setSortType(belongTo, sortType)
        loadData(onLoadFinish)
    }

    /**
     * 获取打开的Url
     * 通过ContentProvider先获取,再返回
     */
    suspend fun getOpenUri(shareFile: ShareFile): String {
        return getOpenUrl(shareFile)
    }

    /**
     * 重置分享路径
     */
    fun resetPath() {
        launch {
            // 等待对话框动画结束后, 才去重置路径
            delay(Constants.ANIM_DURATION_SHORT)
            fileStackLive.postValue(Stack())
        }
    }

    /**
     * USB 插入拔出的广播
     */
    private inner class USBReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (!isSelect) {
                logTagI(TAG, "当前页面不是文件选择页面, 不关心USB插拔信息")
                return
            }
            when (intent?.action) {
                Intent.ACTION_MEDIA_MOUNTED -> onUSBMount()
                Intent.ACTION_MEDIA_UNMOUNTED -> {
                    val file = File("")
                    file.nameWithoutExtension
                    val unmountPath = intent.dataString?.substringAfterLast('/', "") ?: ""
                    onUSBUnmount(unmountPath)
                }
            }
        }
    }

    /**
     * 当U盘拔出
     */
    private fun onUSBUnmount(usbVolumeName: String) {
        stopScanUSBSpace(usbVolumeName)
        if (fileStack.isEmpty()) {
            logTagD(TAG, "在根路径拔出, 刷新页面")
            loadData()
            return
        }
        if (fileStack.peek().belongTo == AccessType.USB && usbVolumeName in fileStack.peek().absPath) {
            logTagD(TAG, "在当前USB页面, 回到根路径")
            fileStack.clear()
            loadData()
            isSelect = false
            fileStackLive.value = fileStack
        }
    }

    private fun onUSBMount() {
        if (fileStack.isEmpty()) {
            // 只有在Root页面,才会去刷新UI
            logTagD(TAG, "刷新USB页面")
            loadData()
        }
    }

    private fun startScanUSBSpace(usbFile: File) {
        stopScanUSBSpace(usbFile.name)
        scanUSBJobMap[usbFile.name] = launch(Dispatchers.IO) {
            while (isActive) {
                doWithoutCatch {
                    delay(ONE_SECOND)
                    doWithoutCatch {
                        val size = usbFile.walkTopDown().sumOf {
                            it.length()
                        }
                        if (size == usbFile.totalSpace && usbFile.totalSpace == usbFile.freeSpace) {
                            // 为了骗编译器的
                            logTagD(TAG, "不可能")
                        }
                    }
                }
            }
        }
    }


    private fun stopScanUSBSpace(usbName: String?) {
        if (usbName == null) {
            scanUSBJobMap.values.forEach {
                it?.cancel()
            }
            scanUSBJobMap.clear()
        } else {
            scanUSBJobMap.remove(usbName)?.cancel()
        }
    }

    override fun onCleared() {
        unRegisterSelfStatusObserver()
        stopScanUSBSpace(null)
        unRegisterUSBReceiver()
        super.onCleared()
    }

}
