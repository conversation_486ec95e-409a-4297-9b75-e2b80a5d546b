package com.czur.starry.device.wallpaperdisplay.bean

import android.os.Parcel
import android.os.Parcelable
import com.czur.starry.device.file.filelib.AccessType
import com.czur.starry.device.file.filelib.FileType
import com.czur.starry.device.wallpaperdisplay.util.DEFAULT_TEXT_WHITE
import com.czur.starry.device.wallpaperdisplay.util.NULL_LAYOUT
import com.czur.starry.device.wallpaperdisplay.util.isTypeOf

/**
 * created by wangh 22.0810
 */


data class FileEntity(
    var absPath: String = "", //文件绝对路径
    val fileType: FileType = FileType.IMAGE, // 文件类型
    var name: String = "", // 文件名
    var lastModifyTime: Long = 2022L, // 设置时间
    val extension: String = "jpg", // 扩展名
    val belongTo: AccessType = AccessType.LOCAL, //属于哪个类别
    var fileSize: Long = 0L,//文件大小 (文件和size为0的文件,大小固定为1)
    var musicPath: String = "",
    var musicName: String = "",
    var textFist: String = "",
    var textSecond: String = "",
    var textThird: String = "",
    var texTag: Int = DEFAULT_TEXT_WHITE,//默认0白色
    var textMode: Int = NULL_LAYOUT//设置中间或左上模式
) : Parcelable {
    val parentPath: String
        get() = absPath.substringBeforeLast(name)


    fun isDir() = isTypeOf(FileType.FOLDER + FileType.ROOT)

    /**
     * 带斜杠的后缀
     */
    val absPathWithSuffix: String
        get() {
            return if (isDir()) {
                if (absPath.endsWith("/")) absPath else "${absPath}/"
            } else {
                absPath
            }
        }

    constructor(parcel: Parcel) : this(
        parcel.readString()!!,
        FileType.valueOf(parcel.readString()!!),
        parcel.readString()!!,
        parcel.readLong(),
        parcel.readString()!!,
        AccessType.valueOf(parcel.readString()!!),
        parcel.readLong(),
        parcel.readString()!!,
        parcel.readString()!!,
        parcel.readString()!!,
        parcel.readString()!!,
        parcel.readString()!!,
        parcel.readInt(),
        parcel.readInt()
    ) {
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(absPath)
        parcel.writeString(fileType.name)
        parcel.writeString(name)
        parcel.writeLong(lastModifyTime)
        parcel.writeString(extension)
        parcel.writeString(belongTo.name)
        parcel.writeLong(fileSize)
        parcel.writeString(musicPath)
        parcel.writeString(musicName)
        parcel.writeString(textFist)
        parcel.writeString(textSecond)
        parcel.writeString(textThird)
        parcel.writeInt(texTag)
        parcel.writeInt(textMode)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<FileEntity> {
        override fun createFromParcel(parcel: Parcel): FileEntity {
            return FileEntity(parcel)
        }

        override fun newArray(size: Int): Array<FileEntity?> {
            return arrayOfNulls(size)
        }
    }


}

enum class FileMode {
    RECENT,
    CUSTOM,     //自定义
    MEETING,       //会议
}

data class CustomImageEntity(
    val id: String,
    val sn: String,
    val directory: String,
    val createTime: String,
    val expireTime: String,
    val isReceived: Boolean

)
