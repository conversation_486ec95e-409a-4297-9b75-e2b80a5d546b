package com.czur.starry.device.wallpaperdisplay.view.dialog

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.czur.starry.device.baselib.base.BaseDialog
import com.czur.starry.device.baselib.utils.view.findView
import com.czur.starry.device.wallpaperdisplay.R
import com.czur.starry.device.wallpaperdisplay.util.getBitmap
import java.io.File

/**
 * created by wangh 22.0728
 */

class LocalSelectDialog : BaseDialog() {

    var confirmClickListener: (() -> Unit)? = null
    var cancelClickListener: (() -> Unit)? = null
    var selectClickListener: (() -> Unit)? = null
    var confBtDark = false

    lateinit var title: String
    lateinit var image: Bitmap
    var isMusic = false

    private val titleTv: TextView by findView(R.id.titleTv)
    private val imView: ImageView by findView(R.id.im_view)
    private val normalDialogConfirmBtn: View by findView(R.id.normalDialogConfirmBtn)
    private val normalDialogCancelBtn: View by findView(R.id.normalDialogCancelBtn)
    private val layoutImg: View by findView(R.id.layout_img)

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return if (isMusic) {
            inflater.inflate(R.layout.dialog_music_select, container, false)
        } else {
            inflater.inflate(R.layout.dialog_local_select, container, false)
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        titleTv.text = title
        imView.setImageBitmap(image)
        if (confBtDark) {
            normalDialogConfirmBtn.isEnabled = false
        }

        normalDialogConfirmBtn.setOnClickListener {
            confirmClickListener?.invoke()
            dismiss()
        }
        normalDialogCancelBtn.setOnClickListener {
            cancelClickListener?.invoke()
            dismiss()
        }
        layoutImg.setOnClickListener {
            selectClickListener?.invoke()
        }
    }

    class Builder {
        private var title = ""
        private var confirmClickListener: (() -> Unit)? = null
        private var cancelClickListener: (() -> Unit)? = null
        private var selectClickListener: (() -> Unit)? = null
        private var isMusic = false
        private var imageView: Bitmap? = null
        private var confBt = false


        fun setConfirmClickListener(listener: () -> Unit): Builder {
            confirmClickListener = listener
            return this
        }

        fun setSelectClickListener(listener: () -> Unit): Builder {
            selectClickListener = listener
            return this
        }

        fun setImageName(context: Context, name: String): Builder {
            title = if (name == "") {
                if (isMusic) {
                    context.getString(R.string.tv_local_music_content)
                } else {
                    context.getString(R.string.tv_local_content)
                }
            } else {
                name
            }

            return this
        }

        suspend fun setImageView(context: Context, path: String): Builder {
            val file = File(path)

            if (path.isNotEmpty() && file.exists()) {
                val bm = getBitmap(path, 400, 225)//BitmapFactory.decodeFile(path)
                imageView = bm
            } else {
                confBt = true
                imageView = BitmapFactory.decodeResource(
                    context.resources,
                    (R.drawable.ic_local_select)
                )
            }
            return this
        }


        fun build(): LocalSelectDialog {
            val dialog = LocalSelectDialog()
            dialog.title = title
            dialog.confirmClickListener = confirmClickListener
            dialog.cancelClickListener = cancelClickListener
            dialog.selectClickListener = selectClickListener
            dialog.image = imageView!!
            dialog.isMusic = isMusic
            dialog.confBtDark = confBt
            return dialog
        }
    }
}