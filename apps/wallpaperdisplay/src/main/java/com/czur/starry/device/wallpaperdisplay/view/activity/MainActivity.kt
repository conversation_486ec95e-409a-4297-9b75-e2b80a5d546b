package com.czur.starry.device.wallpaperdisplay.view.activity

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import androidx.activity.viewModels
import androidx.lifecycle.lifecycleScope
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.utils.isNetworkConnected
import com.czur.starry.device.wallpaperdisplay.R
import com.czur.starry.device.wallpaperdisplay.bean.FileEntity
import com.czur.starry.device.wallpaperdisplay.databinding.ActivityMainBinding
import com.czur.starry.device.wallpaperdisplay.util.CUSTOM_ASSETS
import com.czur.starry.device.wallpaperdisplay.util.RECENT_ASSETS
import com.czur.starry.device.wallpaperdisplay.util.getDSValue
import com.czur.starry.device.wallpaperdisplay.util.getValueToList
import com.czur.starry.device.wallpaperdisplay.util.initTemplateData
import com.czur.starry.device.wallpaperdisplay.util.saveTempData
import com.czur.starry.device.wallpaperdisplay.util.setDSValue
import com.czur.starry.device.wallpaperdisplay.util.setSystemProp
import com.czur.starry.device.wallpaperdisplay.util.setValueToString
import com.czur.starry.device.wallpaperdisplay.view.fragment.HomeFragment
import com.czur.starry.device.wallpaperdisplay.view.vm.PhotoVM
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

const val ASSETS_WELCOME = "welcome"
const val ASSETS_CELEBRATE = "celebrate"
const val ASSETS_TEAM = "team"
class MainActivity : CZViewBindingAty<ActivityMainBinding>() {
    companion object {
        private const val TAG = "wallpaper_display"
        private const val SP_VERSION_WALLPAPER = "wallpaper_version"
        private const val SP_VERSION_TEMPLATE = 7
    }

    private val tempList =
        listOf<String>(ASSETS_WELCOME, ASSETS_CELEBRATE, ASSETS_TEAM)

    private val homeFragment = HomeFragment()
    val model: PhotoVM by viewModels()

    private val netReceiver = NetReceiver()
    private val intentFilter = IntentFilter().apply {
        addAction("android.net.conn.CONNECTIVITY_CHANGE")        //监听网络状态
    }


    override fun ActivityMainBinding.initBindingViews() {
        lifecycleScope.launch {
            //初始化模板变更
            initTemplate()

            supportFragmentManager.beginTransaction()
                .replace(R.id.fragment_container, homeFragment)
                .commitAllowingStateLoss()
        }

        registerReceiver(netReceiver, intentFilter)
    }

    private suspend fun initTemplate() = withContext(Dispatchers.IO) {

        val wallPaperVersion = getDSValue(SP_VERSION_WALLPAPER, 1)
        logTagD(TAG, "===wallPaperVersion==${wallPaperVersion}")
        if (wallPaperVersion != SP_VERSION_TEMPLATE) {

            //如果最近使用第一个模板不存在，设置播放为false
            var recentList = getValueToList(RECENT_ASSETS)
            if (recentList.size > 0) {
                if (!File(recentList[0].absPath).exists()) {
                    setSystemProp(false)
                }
            }

            //删除最近使用路径不存在的数据
            val finalRecent = recentList.filter {
                File(it.absPath).exists()
            }.toMutableList()
            setValueToString(RECENT_ASSETS, finalRecent)

            //删除自定义路径不存在的数据
            var customList = getValueToList(CUSTOM_ASSETS)
            val finalCustom = customList.filter {
                File(it.absPath).exists()
            }.toMutableList()
            setValueToString(CUSTOM_ASSETS, finalCustom)

            //初始化模板数据
            val templateList = mutableListOf<FileEntity>()
            tempList.forEach {
                initTemplateData(it,templateList)
            }
            saveTempData(templateList)
            //版本号
            setDSValue(SP_VERSION_WALLPAPER, SP_VERSION_TEMPLATE)
        }

    }

    override fun onDestroy() {
        super.onDestroy()
        this.unregisterReceiver(netReceiver)
    }

    val connectivityManager: ConnectivityManager by lazy {
        val context = CZURAtyManager.getContext().applicationContext
        context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    }

    private inner class NetReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent) {
            val action = intent.action ?: ""
            logTagD(TAG, "receiver#action: $action")

            val netInfo = connectivityManager.activeNetworkInfo
            model.isNetworking.set((netInfo != null && netInfo.isConnected && isNetworkConnected()))
        }

    }
}