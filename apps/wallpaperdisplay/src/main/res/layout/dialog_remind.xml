<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/dialog_bg"
        android:layout_width="500px"
        android:layout_height="430px"
        android:layout_gravity="center"
        android:background="#5879FC"
        app:bl_corners_radius="10px"
        app:bl_solid_color="#5879FC">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="45px"
            android:gravity="center"
            android:textColor="@color/white"
            android:textSize="36px"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:text="@string/str_set_timeout" />

        <LinearLayout
            android:id="@+id/check_group_ly"
            android:layout_width="272px"
            android:layout_height="120px"
            android:orientation="horizontal"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvTitle">

            <LinearLayout
                android:id="@+id/check_group_sub1"
                android:layout_width="104px"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <RadioButton
                    android:id="@+id/cb_sel1"
                    style="@style/checkbox_feedback"
                    android:checked="true"
                    android:gravity="center"
                    android:text="@string/radio_bt_1_hours" />

                <RadioButton
                    android:id="@+id/cb_sel2"
                    style="@style/checkbox_feedback"
                    android:layout_marginTop="15px"
                    android:checked="false"
                    android:gravity="center"
                    android:text="@string/radio_bt_3_hours" />

                <RadioButton
                    android:id="@+id/cb_sel3"
                    style="@style/checkbox_feedback"
                    android:layout_marginTop="15px"
                    android:checked="false"
                    android:gravity="center"
                    android:text="@string/radio_bt_6_hours" />
            </LinearLayout>

            <View
                android:layout_width="60px"
                android:layout_height="match_parent" />

            <LinearLayout
                android:id="@+id/check_group_sub2"
                android:layout_width="104px"
                android:layout_height="match_parent"

                android:orientation="vertical">

                <RadioButton
                    android:id="@+id/cb_sel4"
                    style="@style/checkbox_feedback"
                    android:checked="false"
                    android:gravity="center"
                    android:text="@string/radio_bt_12_hours" />

                <RadioButton
                    android:id="@+id/cb_sel5"
                    style="@style/checkbox_feedback"
                    android:layout_marginTop="15px"
                    android:checked="false"
                    android:gravity="center"
                    android:text="@string/radio_bt_16_hours" />

                <RadioButton
                    android:id="@+id/cb_sel6"
                    style="@style/checkbox_feedback"
                    android:layout_marginTop="15px"
                    android:checked="false"
                    android:gravity="center"
                    android:text="@string/radio_bt_24_hours" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/doubleBtnFloatBtn"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="30px"
            android:gravity="center_horizontal"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent">

            <com.czur.starry.device.baselib.widget.CommonButton
                android:id="@+id/closeIv"
                android:layout_width="180px"
                android:layout_height="50px"
                app:baselib_theme="dark"
                android:text="@string/dialog_normal_cancel"
                android:textSize="20px"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="@id/confirmBtn" />

            <com.czur.starry.device.baselib.widget.CommonButton
                android:id="@+id/normalDialogConfirmBtn"
                android:layout_width="180px"
                android:layout_height="50px"
                android:layout_marginLeft="30px"
                android:text="@string/dialog_normal_confirm"
                android:textSize="20px"
                android:textStyle="bold"
                app:baselib_theme="white2"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/cancelBtn"
                app:layout_constraintRight_toRightOf="parent" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>


</FrameLayout>