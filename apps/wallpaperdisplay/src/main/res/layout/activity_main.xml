<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity"
    tools:ignore="PxUsage">

    <com.czur.uilib.CZTitleBar
        android:id="@+id/title_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:baselib_titlebar_title="@string/app_name"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ScrollView
        android:id="@+id/content_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="120px"
        android:paddingHorizontal="85px"
        android:scrollbars="none">

        <FrameLayout
            android:id="@+id/fragment_container"
            android:layout_width="match_parent"
            android:layout_height="0px"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="parent" />
    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>