<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:bl_corners_radius="10px"
    app:bl_solid_color="@color/white">

    <TextView
        android:id="@+id/fileTitleTv"
        style="@style/tv_share_file_float_title"
        android:text="@string/tv_choose_folder"
        app:layout_constraintBottom_toBottomOf="@id/closeIv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/closeIv" />

    <ImageView
        android:id="@+id/closeIv"
        android:layout_width="50px"
        android:layout_height="50px"
        android:layout_marginTop="20px"
        android:layout_marginRight="20px"
        android:src="@drawable/ic_float_close"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/backIv"
        android:layout_width="50px"
        android:layout_height="50px"
        android:layout_marginLeft="30px"
        android:layout_marginTop="20px"
        android:src="@drawable/ic_share_file_back"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="0px"
        android:layout_height="0px"
        android:background="@color/base_bg_color"
        app:layout_constraintBottom_toBottomOf="@id/photoView"
        app:layout_constraintLeft_toLeftOf="@id/photoView"
        app:layout_constraintRight_toRightOf="@id/photoView"
        app:layout_constraintTop_toTopOf="@id/photoView" />

    <com.github.chrisbanes.photoview.PhotoView
        android:id="@+id/photoView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="90px"
        android:layout_marginBottom="90px" />

    <ProgressBar
        android:id="@+id/imgProgress"
        android:layout_width="100px"
        android:layout_height="100px"
        android:indeterminateTint="@color/white"
        android:indeterminateTintMode="src_atop"
        app:layout_constraintBottom_toBottomOf="@id/photoView"
        app:layout_constraintLeft_toLeftOf="@id/photoView"
        app:layout_constraintRight_toRightOf="@id/photoView"
        app:layout_constraintTop_toTopOf="@id/photoView" />

    <TextView
        android:id="@+id/selectTv"
        style="@style/tv_share_file_bottom_choose"
        android:layout_marginBottom="20px"
        android:text="@string/btn_tv_ok"
        android:textColor="@color/white"
        app:bl_corners_radius="10px"
        app:bl_solid_color="@color/base_bg_color"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>