<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    app:bl_corners_radius="10px"
    app:bl_solid_color="@color/dialog_bg_color">

    <View
        android:layout_width="match_parent"
        android:layout_height="100px"
        app:bl_corners_topLeftRadius="10px"
        app:bl_corners_topRightRadius="10px"
        app:bl_solid_color="@color/dialog_title_color"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/shareFileTitleTv"
        style="@style/tv_share_file_float_title"
        android:ellipsize="end"
        android:lines="1"
        android:paddingHorizontal="270px"
        android:text="@string/tv_choose_folder"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/closeIv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/closeIv" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/closeIv"
        android:layout_width="60px"
        android:layout_height="60px"
        android:layout_marginTop="20px"
        android:layout_marginRight="20px"
        android:src="@drawable/ic_float_close"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:roundPercent="1" />

    <com.czur.starry.device.baselib.widget.VerticalRecyclerView
        android:id="@+id/shareFileRv"
        android:layout_width="match_parent"
        android:layout_height="0px"
        android:layout_marginTop="105px"
        app:layout_constraintBottom_toTopOf="@id/actionFlow"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/shareFileBackIv"
        android:layout_width="60px"
        android:layout_height="60px"
        android:layout_marginLeft="30px"
        android:layout_marginTop="20px"
        android:src="@drawable/ic_float_back"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:roundPercent="1" />


    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/shareFileSortIv"
        android:layout_width="60px"
        android:layout_height="60px"
        android:layout_marginTop="20px"
        android:layout_marginRight="40px"
        android:src="@drawable/ic_float_sort"
        app:layout_constraintRight_toLeftOf="@id/closeIv"
        app:layout_constraintTop_toTopOf="parent"
        app:roundPercent="1" />


    <com.czur.starry.device.baselib.widget.CommonButton
        android:id="@+id/previewTv"
        style="@style/tv_share_file_bottom_choose"
        android:layout_marginHorizontal="30px"
        android:text="@string/btn_tv_preview"
        app:baselib_theme="dark" />

    <com.czur.starry.device.baselib.widget.CommonButton
        android:id="@+id/selectTv"
        style="@style/tv_share_file_bottom_choose"
        android:text="@string/btn_tv_ok"
        app:baselib_theme="white2" />

    <androidx.constraintlayout.helper.widget.Flow
        android:id="@+id/actionFlow"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="30px"
        app:constraint_referenced_ids="previewTv,selectTv"
        app:flow_horizontalGap="30px"
        app:flow_horizontalStyle="packed"
        app:flow_wrapMode="chain"
        app:layout_constraintBottom_toBottomOf="parent" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/emptyIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_file_empty"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/emptyTv"
        android:layout_marginTop="25px"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/tv_empty_file"
        android:textColor="@color/color_tv_file_empty"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/emptyIv" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/emptyViewGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="emptyIv,emptyTv" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/folderVisibilityGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="shareFileBackIv,shareFileSortIv" />
</androidx.constraintlayout.widget.ConstraintLayout>