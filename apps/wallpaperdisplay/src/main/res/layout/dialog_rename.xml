<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="500px"
    android:layout_height="360px"
    app:bl_corners_radius="10px"
    app:bl_solid_color="#5879FC"
    tools:background="#5879FC">

    <TextView
        android:id="@+id/doubleBtnFloatTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="25px"
        android:includeFontPadding="false"
        android:textColor="@color/white"
        android:textSize="36px"
        android:textStyle="bold"
        android:text="@string/dialog_rename_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <EditText
        android:id="@+id/inputDialogEt"
        android:layout_width="440px"
        android:layout_height="60px"
        android:layout_gravity="center"
        android:background="@drawable/bg_edittext"
        android:imeOptions="actionDone"
        android:includeFontPadding="false"
        android:nextFocusDown="@id/inputDialogEt"
        android:paddingStart="12px"
        android:paddingEnd="12px"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textColorHint="@color/colorWhiteInputHint"
        android:textCursorDrawable="@drawable/dialog_input_cursor"
        android:textSize="30px"
        android:textStyle="bold"
        android:hint="@string/dialog_rename_hint"
        app:layout_constraintBottom_toTopOf="@id/doubleBtnFloatBtn"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/doubleBtnFloatTitleTv"
        >
        <requestFocus/>
    </EditText>

    <LinearLayout
        android:id="@+id/doubleBtnFloatBtn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="30px"
        android:gravity="center_horizontal"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <com.czur.starry.device.baselib.widget.CommonButton
            android:id="@+id/cancelBtn"
            android:layout_width="180px"
            android:layout_height="50px"
            android:text="@string/dialog_normal_cancel"
            android:textSize="20px"
            android:textStyle="bold"
            app:baselib_theme="dark"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="@id/confirmBtn" />

        <com.czur.starry.device.baselib.widget.CommonButton
            android:id="@+id/confirmBtn"
            android:layout_width="180px"
            android:layout_height="50px"
            android:layout_marginLeft="30px"
            android:text="@string/dialog_normal_confirm"
            android:textSize="20px"
            android:textStyle="bold"
            app:baselib_theme="white2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/cancelBtn"
            app:layout_constraintRight_toRightOf="parent" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>