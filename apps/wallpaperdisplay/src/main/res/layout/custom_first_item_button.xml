<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:descendantFocusability="afterDescendants">


    <LinearLayout
        android:id="@+id/layout_local"
        android:layout_width="400px"
        android:layout_height="96px"
        android:orientation="horizontal"
        app:bl_corners_radius="10px"
        app:bl_solid_color="#1AFFFFFF"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"></LinearLayout>

    <LinearLayout
        android:id="@+id/layout_qrcode"
        android:layout_width="400px"
        android:layout_height="96px"
        android:layout_marginTop="33px"
        android:orientation="horizontal"
        app:bl_corners_radius="10px"
        app:bl_solid_color="#1AFFFFFF"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/layout_local"></LinearLayout>

    <ImageView
        android:id="@+id/im_local"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/ic_upload"
        app:layout_constraintBottom_toBottomOf="@+id/layout_local"
        app:layout_constraintLeft_toLeftOf="@+id/im_qrcode"
        app:layout_constraintRight_toLeftOf="@+id/tv_local"
        app:layout_constraintTop_toTopOf="@+id/layout_local" />

    <ImageView
        android:id="@+id/im_qrcode"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="105px"
        android:clickable="false"
        android:src="@drawable/ic_upload"
        app:layout_constraintBottom_toBottomOf="@+id/layout_qrcode"
        app:layout_constraintLeft_toLeftOf="@+id/layout_qrcode"
        app:layout_constraintRight_toLeftOf="@+id/tv_qrcode"
        app:layout_constraintTop_toTopOf="@+id/layout_qrcode" />

    <TextView
        android:id="@+id/tv_local"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15px"
        android:text="@string/bt_upload_local"
        android:textSize="24px"
        app:layout_constraintBottom_toBottomOf="@+id/layout_local"
        app:layout_constraintLeft_toRightOf="@+id/im_local"
        app:layout_constraintTop_toTopOf="@+id/layout_local" />

    <TextView
        android:id="@+id/tv_qrcode"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15px"
        android:clickable="false"
        android:text="@string/bt_upload_qrcode"
        android:textSize="24px"
        app:layout_constraintBottom_toBottomOf="@+id/layout_qrcode"
        app:layout_constraintLeft_toRightOf="@+id/im_qrcode"
        app:layout_constraintTop_toTopOf="@+id/layout_qrcode" />
</androidx.constraintlayout.widget.ConstraintLayout>