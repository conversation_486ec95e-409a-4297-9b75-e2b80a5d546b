<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/preview_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/im_dis_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitXY"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />




    <EditText
        android:id="@+id/et_title"
        style="@style/edit_shadow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="320px"
        android:background="@android:color/transparent"
        android:focusable="false"
        android:gravity="center"
        android:hint=" "
        android:paddingHorizontal="15px"
        android:paddingVertical="20px"
        android:textColor="@color/white"
        android:textCursorDrawable="@drawable/cursor_title"
        android:textSize="150px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <EditText
        android:id="@+id/et_content"
        style="@style/edit_shadow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20px"
        android:background="@android:color/transparent"
        android:focusable="false"
        android:gravity="center"
        android:hint=" "
        android:paddingHorizontal="15px"
        android:paddingVertical="10px"
        android:textColor="@color/white"
        android:textCursorDrawable="@drawable/cursor_title"
        android:textSize="80px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/et_title" />


    <EditText
        android:id="@+id/et_date"
        style="@style/edit_shadow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20px"
        android:background="@android:color/transparent"
        android:focusable="false"
        android:gravity="center"
        android:hint=" "
        android:paddingHorizontal="15px"
        android:paddingVertical="10px"
        android:textColor="@color/white"
        android:textCursorDrawable="@drawable/cursor_title"
        android:textSize="80px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/et_content" />

    <include
        android:id="@+id/layout_bt"
        layout="@layout/item_preview" />

    <com.czur.starry.device.baselib.widget.CommonButton
        android:id="@+id/bt_delete"
        android:layout_width="200px"
        android:layout_height="80px"
        android:layout_marginRight="30px"
        android:text="@string/str_bt_delete"
        android:textSize="30px"
        app:baselib_theme="dark3"
        app:layout_constraintRight_toLeftOf="@+id/bt_save"
        app:layout_constraintTop_toTopOf="@+id/bt_play_or_stop" />

    <com.czur.starry.device.baselib.widget.CommonButton
        android:id="@+id/bt_save"
        android:visibility="gone"
        android:layout_width="200px"
        android:layout_height="80px"
        android:layout_marginRight="30px"
        android:text="@string/btn_tv_save"
        android:textSize="30px"
        app:baselib_theme="dark3"
        app:layout_constraintRight_toLeftOf="@+id/bt_save_custom"
        app:layout_constraintTop_toTopOf="@+id/bt_play_or_stop" />

    <com.czur.starry.device.baselib.widget.CommonButton
        android:id="@+id/bt_save_custom"
        android:layout_width="300px"
        android:layout_height="80px"
        android:layout_marginRight="60px"
        android:layout_marginBottom="60px"
        android:text="@string/str_bt_save_custom"
        android:textSize="30px"
        app:baselib_theme="blue"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/bt_play_or_stop" />

    <com.czur.starry.device.baselib.widget.CommonButton
        android:id="@+id/bt_play_or_stop"
        android:layout_width="300px"
        android:layout_height="80px"
        android:layout_marginRight="60px"
        android:layout_marginBottom="60px"
        android:text="@string/str_bt_play"
        android:textSize="30px"
        app:baselib_theme="blue"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <com.czur.starry.device.baselib.widget.CommonButton
        android:id="@+id/bt_back"
        android:layout_width="300px"
        android:layout_height="80px"
        android:layout_marginBottom="60px"
        android:text="@string/str_back_play"
        android:textSize="30px"
        android:visibility="gone"
        app:baselib_theme="white2"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <ImageView
        android:id="@+id/title_bar_pre"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="60px"
        android:layout_marginTop="51px"
        android:src="@drawable/ic_back"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>