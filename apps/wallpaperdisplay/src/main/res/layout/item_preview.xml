<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/preview_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <com.czur.starry.device.baselib.widget.CommonButton
        android:id="@+id/bt_middle"
        android:layout_width="300px"
        android:layout_height="80px"
        android:layout_marginLeft="60px"
        android:layout_marginBottom="60px"
        android:text="@string/str_bt_middle"
        android:textSize="30px"
        app:baselib_theme="dark3"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent" />

    <com.czur.starry.device.baselib.widget.CommonButton
        android:id="@+id/bt_left"
        android:layout_width="300px"
        android:layout_height="80px"
        android:layout_marginLeft="30px"
        android:text="@string/str_bt_left"
        android:textSize="30px"
        app:baselib_theme="white2"
        app:layout_constraintLeft_toRightOf="@+id/bt_middle"
        app:layout_constraintTop_toTopOf="@+id/bt_middle" />

    <com.czur.starry.device.baselib.widget.CommonButton
        android:id="@+id/bt_reset"
        android:layout_width="200px"
        android:layout_height="80px"
        android:layout_marginLeft="30px"
        android:background="#7f000000"
        android:text="@string/str_bt_reset"
        android:textSize="30px"
        app:baselib_theme="dark3"
        app:layout_constraintLeft_toRightOf="@+id/bt_left"
        app:layout_constraintTop_toTopOf="@+id/bt_middle" />


</androidx.constraintlayout.widget.ConstraintLayout>