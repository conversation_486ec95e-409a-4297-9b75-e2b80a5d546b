<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:descendantFocusability="afterDescendants">


    <include
        android:id="@+id/select_id"
        layout="@layout/custom_first_item_button"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/im_custom"
        android:layout_width="400px"
        android:layout_height="225px"
        android:background="@drawable/bg_common"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:round="12px" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="400px"
        android:layout_height="wrap_content"
        android:layout_marginTop="10px"
        android:background="@android:color/transparent"
        android:gravity="center"
        android:text="@string/tv_self_define"
        android:textSize="24px"
        app:layout_constraintLeft_toLeftOf="@id/im_custom"
        app:layout_constraintTop_toBottomOf="@+id/im_custom" />

    <ProgressBar
        android:id="@+id/progress_bar"
        style="@style/Widget.AppCompat.ProgressBar"
        android:layout_width="70px"
        android:layout_height="70px"
        android:layout_gravity="center"
        android:indeterminateTint="@color/bg_main"
        android:indeterminateTintMode="src_atop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>