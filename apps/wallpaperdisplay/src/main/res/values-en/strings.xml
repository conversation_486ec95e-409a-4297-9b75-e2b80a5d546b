<resources>
    <string name="app_name">Customized Screen Saver</string>
    <string name="tv_recent_play">Recent use</string>
    <string name="tv_self_define">Customize</string>
    <string name="tv_welcome_define">Template for welcoming event</string>
    <string name="tv_meeting_define">Template for meeting</string>
    <string name="tv_festival_define">Template for birthday party</string>

    <string name="bt_upload_local">Upload from local files</string>
    <string name="bt_upload_qrcode">Scan QR code to upload</string>

    <string name="tv_local_content">Click to upload picture\n Recommended 1920*1080 or above, PNG/JPG</string>
    <string name="tv_local_music_content">Click to upload MP3 file</string>
    <string name="tv_qrcode_content">To guarantee visual effect, please upload picture with resolution 1920*1080 and \n</string>

    <string name="tv_choose_folder">Select Welcoming Template Picture</string>
    <string name="btn_tv_cancel">Cancel</string>
    <string name="btn_tv_preview">Preview</string>
    <string name="btn_tv_ok">OK</string>
    <string name="btn_tv_back">back</string>
    <string name="btn_tv_save">Save</string>
    <string name="str_stop_play">Stop applying screen saver</string>
    <string name="str_back_play">back</string>
    <string name="str_last_palytime">%1$shour(s)%2$smin(s) will end</string>
    <string name="str_palytime_hour">hour(s)</string>
    <string name="str_palytime_min">min(s)</string>
    <string name="str_palytime_sec">s</string>
    <string name="str_palytime_auto_stop">Screen saver will end in</string>
    <string name="str_sort_time_desc">Reverse chronological</string>
    <string name="str_sort_time_asc">Chronological</string>
    <string name="str_sort_name_desc">Reverse alphabetical</string>
    <string name="str_sort_name_asc">Alphabetical</string>

    <string name="str_create_temp_success">Customized template has been created！</string>
    <string name="str_create_temp_filed">Reach the limits of customized template quantity！</string>


    <string name="str_text_exit">Press back to exit screen saver </string>
    <string name="str_text_remind">When the TouchBoard is put to charging position, screen saver will be applied in the set period. </string>
    <string name="str_set_timeout">Set Duration</string>
    <string name="str_text_remind_check">Do not remind again</string>
    <string name="float_notify_new_call">%s正在邀请您加入会议</string>
    <string name="radio_bt_1_hours">1 hour</string>
    <string name="radio_bt_3_hours">3 hours</string>
    <string name="radio_bt_6_hours">6 hours</string>
    <string name="radio_bt_12_hours">12 hours</string>
    <string name="radio_bt_16_hours">16 hours</string>

    <string name="welcome_title">Warm welcomes</string>
    <string name="birthday_title">Happy  Birthday</string>

    <string name="sel_bar_item_all">All</string>
    <string name="str_bt_middle">Center text</string>
    <string name="str_bt_left">Top-left text</string>
    <string name="str_bt_reset">Reset</string>
    <string name="str_bt_delete">Delete</string>
    <string name="str_text_remind1">The screen saver will light up when the TouchBoard is put back to the charging dock in the set period.</string>
    <string name="dialog_rename_title">Rename</string>
    <string name="dialog_rename_hint">Enter file name</string>
</resources>