<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
    </style>

    <!-- Base application theme. -->
    <style name="Theme.Starry" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
    </style>

    <style name="tv_share_file_float_title">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">30px</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:textStyle">normal</item>
    </style>

    <style name="tv_share_file_bottom_choose">
        <item name="android:layout_width">200px</item>
        <item name="android:layout_height">50px</item>
        <item name="android:textSize">20px</item>
        <item name="android:gravity">center</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="text_sort">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">30px</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="iv_sort_sel">
        <item name="android:layout_width">22px</item>
        <item name="android:layout_height">22px</item>
        <item name="android:src">@drawable/ic_choose</item>
        <item name="android:layout_marginLeft">10px</item>
        <item name="android:visibility">invisible</item>
    </style>

    <style name="title_fomat">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">30px</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="checkbox_feedback" parent="android:Widget.CompoundButton.CheckBox">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/text_white</item>
        <item name="android:textSize">20px</item>
        <item name="android:textStyle">bold</item>
        <item name="android:button">@drawable/sel_check_box</item>
        <item name="android:paddingLeft">8px</item>
        <item name="android:background">@null</item>
        <item name="android:includeFontPadding">false</item>
        <!--波纹的颜色-->
        <item name="android:colorControlHighlight">@color/transparent</item>
        <!--选中的颜色-->
        <item name="colorAccent">@color/transparent</item>
    </style>

    <style name="edit_shadow">
        <item name="android:shadowColor">#78787878</item>
        <item name="android:shadowRadius">10</item>
        <item name="android:shadowDx">5</item>
        <item name="android:shadowDy">5</item>
    </style>

    <style name="style_sort_pop_layout">
        <item name="android:layout_width">200px</item>
        <item name="android:layout_height">60px</item>
        <item name="android:paddingLeft">28px</item>
        <item name="android:paddingRight">15px</item>
        <item name="android:orientation">horizontal</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="style_sort_pop_text">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">24px</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:textColor">@color/text_common</item>
    </style>

    <style name="style_sort_pop_img">
        <item name="android:layout_width">22px</item>
        <item name="android:layout_height">22px</item>
        <item name="android:layout_marginLeft">28px</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:src">@drawable/ic_choose</item>
    </style>
</resources>