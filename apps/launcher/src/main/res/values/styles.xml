<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="PxUsage">

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:overScrollMode">never</item>
        <item name="android:defaultFocusHighlightEnabled">false</item>
    </style>

    <style name="ActivityThemeBoot" parent="@android:style/Theme.Translucent.NoTitleBar">
        <item name="android:animation">@null</item>
        <item name="android:windowAnimationStyle">@null</item>
    </style>

    <style name="ActivityThemeMain" parent="Theme.AppCompat.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowShowWallpaper">true</item>
        <item name="android:textColorHint">#80FFFFFF</item>
        <item name="android:overScrollMode">never</item>
        <item name="android:textCursorDrawable">@drawable/drawable_base_cursor</item>

        <!--ContextMenu 的背景颜色-->
        <item name="android:dropDownListViewStyle">@style/listView</item>
        <item name="android:itemTextAppearance">@style/MyContextMenuText</item>

        <item name="android:defaultFocusHighlightEnabled">false</item>
    </style>

    <style name="short_cut_app_tv">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">#ffffff</item>
        <item name="android:textSize">24px</item>
        <item name="android:layout_marginBottom">30px</item>
        <item name="android:layout_marginLeft">30px</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="file_code_title_tv">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">#ffffff</item>
        <item name="android:textSize">20px</item>
        <item name="android:layout_marginTop">30px</item>
        <item name="android:layout_marginRight">30px</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="file_code_content_tv">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">#ffffff</item>
        <item name="android:textSize">30px</item>
        <item name="android:layout_marginTop">5px</item>
        <item name="android:layout_marginRight">30px</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="short_cut_app_content">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">#ffffff</item>
        <item name="android:textSize">24px</item>
        <item name="android:layout_marginBottom">20px</item>
        <item name="android:layout_marginRight">30px</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="contact_dialog_icon_iv">
        <item name="android:layout_width">60px</item>
        <item name="android:layout_height">60px</item>
        <item name="android:layout_marginRight">20px</item>
    </style>
</resources>
