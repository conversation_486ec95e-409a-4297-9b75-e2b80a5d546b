<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/toolSheetLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="PxUsage"
    tools:layout_height="218px"
    tools:layout_width="500px">
    <!--  应用列表  -->


    <com.czur.starry.device.launcher.widget.BlurImageView
        android:id="@+id/toolSheetBiv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="1px" />

    <com.czur.starry.device.launcher.widget.LauncherMainMarkView
        android:layout_width="0px"
        android:layout_height="0px"
        app:layout_constraintBottom_toBottomOf="@id/toolSheetBiv"
        app:layout_constraintLeft_toLeftOf="@id/toolSheetBiv"
        app:layout_constraintRight_toRightOf="@id/toolSheetBiv"
        app:layout_constraintTop_toTopOf="@id/toolSheetBiv" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="55px"
        android:layout_marginTop="25px"
        android:text="@string/title_fav_app"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/toolSheetBiv" />

    <ImageView
        android:id="@+id/toolSheetAppIv1"
        android:layout_width="82px"
        android:layout_height="82px"
        android:scaleType="fitXY" />

    <TextView
        android:id="@+id/toolSheetAppTv1"
        style="@style/tool_sheet_name_tv"
        app:layout_constraintLeft_toLeftOf="@id/toolSheetAppIv1"
        app:layout_constraintRight_toRightOf="@id/toolSheetAppIv1"
        app:layout_constraintTop_toBottomOf="@id/toolSheetAppIv1" />

    <ImageView
        android:id="@+id/toolSheetAppIv2"
        android:layout_width="82px"
        android:layout_height="82px"
        android:scaleType="fitXY" />

    <TextView
        android:id="@+id/toolSheetAppTv2"
        style="@style/tool_sheet_name_tv"
        app:layout_constraintLeft_toLeftOf="@id/toolSheetAppIv2"
        app:layout_constraintRight_toRightOf="@id/toolSheetAppIv2"
        app:layout_constraintTop_toBottomOf="@id/toolSheetAppIv2" />

    <ImageView
        android:id="@+id/toolSheetAppIv3"
        android:layout_width="82px"
        android:layout_height="82px"
        android:scaleType="fitXY" />

    <TextView
        android:id="@+id/toolSheetAppTv3"
        style="@style/tool_sheet_name_tv"
        app:layout_constraintLeft_toLeftOf="@id/toolSheetAppIv3"
        app:layout_constraintRight_toRightOf="@id/toolSheetAppIv3"
        app:layout_constraintTop_toBottomOf="@id/toolSheetAppIv3" />

    <ImageView
        android:id="@+id/toolSheetAppIv4"
        android:layout_width="82px"
        android:layout_height="82px"
        android:scaleType="fitXY" />

    <TextView
        android:id="@+id/toolSheetAppTv4"
        style="@style/tool_sheet_name_tv"
        app:layout_constraintLeft_toLeftOf="@id/toolSheetAppIv4"
        app:layout_constraintRight_toRightOf="@id/toolSheetAppIv4"
        app:layout_constraintTop_toBottomOf="@id/toolSheetAppIv4" />

    <androidx.constraintlayout.helper.widget.Flow
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginTop="85px"
        android:layout_marginHorizontal="55px"
        app:flow_horizontalStyle="spread_inside"
        app:constraint_referenced_ids="toolSheetAppIv1,toolSheetAppIv2,toolSheetAppIv3,toolSheetAppIv4"
        app:layout_constraintLeft_toLeftOf="@id/toolSheetBiv"
        app:layout_constraintRight_toRightOf="@id/toolSheetBiv"
        app:layout_constraintTop_toTopOf="@id/toolSheetBiv" />
</androidx.constraintlayout.widget.ConstraintLayout>