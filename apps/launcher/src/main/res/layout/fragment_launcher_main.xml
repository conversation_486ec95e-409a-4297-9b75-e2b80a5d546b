<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:clipChildren="false"
    tools:ignore="PxUsage">

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/timeFragmentContainer"
        android:name="com.czur.starry.device.launcher.pages.view.launcher.custom.LeftTimeFragment"
        android:layout_width="500px"
        android:layout_height="334px"
        app:layout_constraintBottom_toTopOf="@id/toolSheetFragmentContainer"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/quickBootFragmentContainer"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/toolSheetFragmentContainer"
        android:name="com.czur.starry.device.launcher.pages.view.launcher.custom.ToolSheetFragment"
        android:layout_width="500px"
        android:layout_height="218px"
        android:layout_marginTop="24px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@id/timeFragmentContainer"
        app:layout_constraintTop_toBottomOf="@id/timeFragmentContainer" />


    <androidx.fragment.app.FragmentContainerView
        android:clipChildren="false"
        android:id="@+id/quickBootFragmentContainer"
        android:layout_width="912px"
        android:layout_height="576px"
        android:layout_marginLeft="72px"
        android:name="com.czur.starry.device.launcher.pages.view.launcher.quickboot.v2.ComplicatedQuickBootFragment"
        app:layout_constraintBottom_toBottomOf="@id/toolSheetFragmentContainer"
        app:layout_constraintLeft_toRightOf="@id/timeFragmentContainer"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/timeFragmentContainer" />

    <ImageView
        android:id="@+id/slideUpIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:elevation="10px"
        android:padding="10px"
        android:src="@drawable/ic_slide_up"
        app:layout_constraintBottom_toTopOf="@id/slideUpTv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/slideUpTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="15px"
        android:paddingHorizontal="10px"
        android:shadowColor="@color/black"
        android:shadowDx="0"
        android:shadowDy="0"
        android:shadowRadius="2"
        android:text="@string/str_slide_up_hint"
        android:textColor="@color/white"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/slideUpGuideGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="slideUpIv,slideUpTv" />

</androidx.constraintlayout.widget.ConstraintLayout>