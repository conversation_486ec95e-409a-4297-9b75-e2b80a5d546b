<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    android:clipToOutline="false"
    tools:ignore="PxUsage,RtlHardcoded,ContentDescription"
    tools:layout_height="334px"
    tools:layout_width="500px">

    <com.czur.starry.device.launcher.widget.BlurImageView
        android:id="@+id/timeBiv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.czur.starry.device.launcher.widget.LauncherMainMarkView
        android:layout_width="0px"
        android:layout_height="0px"
        app:layout_constraintBottom_toBottomOf="@id/timeBiv"
        app:layout_constraintLeft_toLeftOf="@id/timeBiv"
        app:layout_constraintRight_toRightOf="@id/timeBiv"
        app:layout_constraintTop_toTopOf="@id/timeBiv" />


    <TextClock
        android:id="@+id/bigTimeClock"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="75px"
        android:format12Hour="HH:mm"
        android:format24Hour="HH:mm"
        android:includeFontPadding="false"
        android:shadowColor="@color/black"
        android:shadowDx="0"
        android:shadowDy="0"
        android:shadowRadius="5"
        android:textColor="@color/white"
        android:textSize="140px"
        android:textStyle="normal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextClock
        android:id="@+id/yearTimeClock"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:format12Hour="yyyy"
        android:format24Hour="yyyy"
        android:shadowColor="@color/black"
        android:shadowDx="0"
        android:shadowDy="0"
        android:shadowRadius="5"
        android:textColor="@color/white"
        android:textSize="24px"
        android:textStyle="normal"
        app:layout_constraintBottom_toTopOf="@id/dateTimeClock"
        app:layout_constraintLeft_toLeftOf="@id/bigTimeClock"
        app:layout_constraintTop_toBottomOf="@id/bigTimeClock" />

    <TextClock
        android:id="@+id/dateTimeClock"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="25px"
        android:format12Hour="MM/dd"
        android:format24Hour="MM/dd"
        android:shadowColor="@color/black"
        android:shadowDx="0"
        android:shadowDy="0"
        android:shadowRadius="5"
        android:textColor="@color/white"
        android:textSize="50px"
        android:textStyle="normal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="@id/bigTimeClock" />

    <TextClock
        android:id="@+id/weekTimeClock"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10px"
        android:format12Hour="EEEE"
        android:format24Hour="EEEE"
        android:shadowColor="@color/black"
        android:shadowDx="0"
        android:shadowDy="0"
        android:shadowRadius="5"
        android:textColor="@color/white"
        android:textSize="24px"
        android:textStyle="normal"
        app:layout_constraintBaseline_toBaselineOf="@id/dateTimeClock"
        app:layout_constraintLeft_toRightOf="@id/dateTimeClock" />

</androidx.constraintlayout.widget.ConstraintLayout>