<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="left"
    android:clipToPadding="false"
    tools:ignore="PxUsage,RtlHardcoded"
    tools:layout_height="576px"
    android:clipChildren="false"
    tools:layout_width="912px">


    <androidx.constraintlayout.helper.widget.Flow
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:flow_horizontalStyle="spread_inside"
        app:constraint_referenced_ids="quickBoot4,quickBoot5,quickBoot6,"/>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/quickBoot1"
        android:layout_width="632px"
        android:layout_height="334px"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.czur.starry.device.launcher.widget.BlurImageView
            android:id="@+id/quickBoot1BlurIv"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/bg_dial_unpress" />

        <TextView
            android:id="@+id/netMeetingAppTitleTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="28px"
            android:layout_marginTop="30px"
            android:shadowColor="@color/black"
            android:shadowDx="0"
            android:shadowDy="0"
            android:shadowRadius="5"
            android:text="@string/str_quick_boot_title_other_meeting"
            android:textColor="@color/white"
            android:textSize="30px"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/netMeetingAppTitle2Tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="50px"
            android:layout_marginEnd="30px"
            android:shadowColor="@color/black"
            android:shadowDx="0"
            android:shadowDy="0"
            android:shadowRadius="5"
            android:text="@string/str_quick_boot_title_other_meeting2"
            android:textColor="@color/white"
            android:textSize="30px"
            android:textStyle="bold"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.czur.starry.device.launcher.widget.OtherQuickBootAppIconIv
            android:id="@+id/byomAppIcon"
            android:layout_width="240px"
            android:layout_height="155px"
            android:layout_marginLeft="28px"
            android:layout_marginTop="80px"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.helper.widget.Flow
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="270px"
            android:layout_marginTop="110px"
            android:layout_marginRight="10px"
            app:constraint_referenced_ids="netMeetingAppIcon1,netMeetingAppIcon2"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/netMeetingApp1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="netMeetingAppIcon1,netMeetingAppName1" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/netMeetingApp2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="netMeetingAppIcon2,netMeetingAppName2" />

        <com.czur.starry.device.launcher.widget.OtherQuickBootAppIconIv
            android:id="@+id/netMeetingAppIcon1"
            android:layout_width="120px"
            android:layout_height="120px"
            app:float_tips="@string/float_tip_empty" />

        <TextView
            android:id="@+id/netMeetingAppName1"
            android:layout_width="140px"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:lineSpacingExtra="-7px"
            android:shadowColor="@color/black"
            android:shadowDx="0"
            android:shadowDy="0"
            android:shadowRadius="2"
            android:textColor="@color/white"
            android:textSize="20px"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="@id/netMeetingAppIcon1"
            app:layout_constraintRight_toRightOf="@id/netMeetingAppIcon1"
            app:layout_constraintTop_toBottomOf="@id/netMeetingAppIcon1" />

        <com.czur.starry.device.launcher.widget.OtherQuickBootAppIconIv
            android:id="@+id/netMeetingAppIcon2"
            android:layout_width="120px"
            android:layout_height="120px"
            app:float_tips="@string/float_tip_empty" />

        <TextView
            android:id="@+id/netMeetingAppName2"
            android:layout_width="140px"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:lineSpacingExtra="-7px"
            android:shadowColor="@color/black"
            android:shadowDx="0"
            android:shadowDy="0"
            android:shadowRadius="2"
            android:textColor="@color/white"
            android:textSize="20px"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="@id/netMeetingAppIcon2"
            app:layout_constraintRight_toRightOf="@id/netMeetingAppIcon2"
            app:layout_constraintTop_toBottomOf="@id/netMeetingAppIcon2" />


        <TextView
            android:layout_width="0px"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:lineSpacingExtra="-7px"
            android:shadowColor="@color/black"
            android:shadowDx="0"
            android:shadowDy="0"
            android:shadowRadius="2"
            android:text="@string/str_quick_boot_title_byom"
            android:textColor="@color/white"
            android:textSize="20px"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="@id/byomAppIcon"
            app:layout_constraintRight_toRightOf="@id/byomAppIcon"
            app:layout_constraintTop_toBottomOf="@id/byomAppIcon" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/quickBoot2"
        android:layout_width="272px"
        android:layout_height="334px"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <include layout="@layout/item_quick_boot" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/quickBoot4"
        android:layout_width="312px"
        android:layout_height="218px">

        <include layout="@layout/item_quick_boot" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/quickBoot5"
        android:layout_width="312px"
        android:layout_height="218px">

        <include layout="@layout/item_quick_boot" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/quickBoot6"
        android:layout_width="272px"
        android:layout_height="218px">

        <include layout="@layout/item_quick_boot" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>