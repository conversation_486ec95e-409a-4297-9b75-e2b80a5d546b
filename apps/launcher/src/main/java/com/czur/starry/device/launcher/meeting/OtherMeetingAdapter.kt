package com.czur.starry.device.launcher.meeting

import android.view.ViewGroup
import com.czur.starry.device.baselib.base.BaseDifferAdapter
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.data.bean.NetMeetingApp
import com.czur.starry.device.launcher.widget.OtherQuickBootAppIconIv

/**
 * Created by 陈丰尧 on 2022/8/23
 */
class OtherMeetingAdapter : BaseDifferAdapter<NetMeetingApp>() {

    override fun areItemsTheSame(oldItem: NetMeetingApp, newItem: NetMeetingApp): Boolean {
        return oldItem.pkgName == newItem.pkgName
    }

    override fun bindViewHolder(holder: BaseVH, position: Int, itemData: NetMeetingApp) {
        holder.getView<OtherQuickBootAppIconIv>(R.id.itemIconIv).apply {
            updateIcon(itemData)
        }
        holder.setText(itemData.appName, R.id.itemNameTv)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
        return BaseVH(R.layout.item_other_meeting_app, parent)
    }
}