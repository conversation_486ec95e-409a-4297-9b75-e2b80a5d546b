package com.czur.starry.device.launcher.pages.view.launcher

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import com.czur.starry.device.launcher.backdrop.BackdropManager

/**
 * Created by 陈丰尧 on 2024/7/25
 */
class LauncherMainViewModel(application: Application) : AndroidViewModel(application) {
    var hasInit = false // 是否已经初始化过了

    val backdropColorFlow = BackdropManager.backdropColorFlow
    val backdropColor
        get() = BackdropManager.backdropColor
}