package com.czur.starry.device.launcher.widget

import android.content.Context
import android.util.AttributeSet
import android.view.View
import androidx.core.content.ContextCompat
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.backdrop.BackdropManager
import com.czur.starry.device.launcher.backdrop.BackdropManager.BackdropColor.*
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/**
 * Created by 陈丰尧 on 2025/6/4
 */
class LauncherMainMarkView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    private val scope = MainScope()
    private var job: Job? = null

    private var darkDrawable =
        ContextCompat.getDrawable(context, R.drawable.mark_launcher_main_dark)
    private var lightDrawable =
        ContextCompat.getDrawable(context, R.drawable.mark_launcher_main_light)

    init {
        background = ContextCompat.getDrawable(context, R.drawable.mark_launcher_main_dark)
    }

    fun updateDrawable(
        darkDrawable: Int,
        lightDrawable: Int
    ) {
        this.darkDrawable = ContextCompat.getDrawable(context, darkDrawable)

        this.lightDrawable = ContextCompat.getDrawable(context, lightDrawable)

        background = when (BackdropManager.backdropColor) {
            Dark -> this.darkDrawable
            Light -> this.lightDrawable
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        job?.cancel()
        job = scope.launch {
            BackdropManager.backdropColorFlow.collect {
                background = when (it) {
                    Dark -> darkDrawable

                    Light -> lightDrawable
                }
            }
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        job?.cancel()
    }
}