package com.czur.starry.device.launcher.pages.view.launcher.quickboot

import android.content.Intent
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.map
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_FILE
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_NOTICE_SETTING
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_SCREEN_SHARE
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_WRITE_PAD
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.VersionIndustry
import com.czur.starry.device.file.filelib.FileHandlerLive
import com.czur.starry.device.file.filelib.FileHandlerLive.fileShareCodeStatus
import com.czur.starry.device.file.filelib.FileHandlerLive.unReadFileName
import com.czur.starry.device.launcher.R
import com.czur.starry.device.otalib.OTAHandler
import com.czur.starry.device.starrypadlib.StarryPadPaintHandler
import kotlin.random.Random

/**
 * Created by 陈丰尧 on 2/22/21
 * 配置主页快速速启动的相关参数
 */
private const val TAG = "QuickBootApp"

open class QuickBootApp(
    val id: String,
    val nameRes: Int,                               // 显示的名字
    val iconResDark: Int,                           // 背景图片
    val iconResLight: Int,                          // 白色图标
    val bgDrawableDark: Int,                        // 深色背景drawable
    val bgDrawableLight: Int,                       // 浅色背景drawable
    val pointShadowColor: Int? = null,             // 圆点阴影颜色
    val badgeLive: LiveData<Boolean>? = null,       // 有新标记的LiveData
    private val intentBuilder: Intent.() -> Intent?    // 构建启动Intent
) {
    // 启动Intent
    val bootIntent: Intent?
        get() = Intent().intentBuilder()
}

val fileNameLiveData = MediatorLiveData<String>().apply {

    fun updateFileName() {
        logTagV(TAG, "新文件名字 = $unReadFileName")
        value = unReadFileName
    }

    addSource(FileHandlerLive.unReadFileNameLive) {
        updateFileName()
    }

}

val starryPadFileNameLiveData = MediatorLiveData<String>().apply {

    fun updateFileName(name: String) {
        logTagV(TAG, "StarryPad新文件名字 = $name")
        value = name
    }

    addSource(StarryPadPaintHandler.newPaintFileNameLive) {
        updateFileName(it)
    }

}

val fileCodeLiveData = MediatorLiveData<String>().apply {

    fun updateFileCode() {
        logTagV(TAG, "新校验码")
        value = fileShareCodeStatus ?: Random.nextInt(1000, 10000).toString()
    }

    addSource(FileHandlerLive.fileShareCodeLive) {
        updateFileCode()
    }
    addSource(FileHandlerLive.fileShareCodeEnableLive) {
        updateFileCode()
    }
    addSource(FileHandlerLive.fileShareEnableLive) {
        updateFileCode()
    }
}
val settingNewLiveData = MediatorLiveData<Boolean>().apply {
    fun updateVersion() {
        val ota = OTAHandler.newVersionStatus
        val touchPad = OTAHandler.newTouchPadVersion
        val camera =
            OTAHandler.newCameraVersion != "null" && OTAHandler.newCameraVersion != OTAHandler.currentCameraVersion
        logTagV(TAG, "ota新版本:$ota , 触控板新版本:${touchPad}")
        value = ota || touchPad || camera
    }

    addSource(OTAHandler.newTouchPadVersionLive) {
        updateVersion()
    }
    addSource(OTAHandler.newVersionStatusLive) {
        updateVersion()
    }
    addSource(OTAHandler.newCameraVersionStatusLive) {
        updateVersion()
    }

}

/**
 * 获取QuickBoot的信息
 */
val quickBootApps = listOf(
    QuickBootApp("",0, 0, 0, 0, 0) { null }, // 占位, 位置0不显示任何内容
    QuickBootApp(
        "ScreenShare",
        if (Constants.versionIndustry == VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD) R.string.quick_boot_screen_share_army else R.string.quick_boot_screen_share,
        iconResDark = R.drawable.icon_quick_share_dark,
        iconResLight = R.drawable.icon_quick_share_light,
        bgDrawableDark = R.drawable.mark_quick_share_dark,
        bgDrawableLight = R.drawable.mark_quick_share_light,
    ) {
        // 无线投屏
        action = ACTION_BOOT_SCREEN_SHARE
        addFlags(Intent.FLAG_ACTIVITY_NO_ANIMATION)
        this
    },
    QuickBootApp(
        "StarryPad",
        R.string.quick_boot_write_pad,
        iconResDark = R.drawable.icon_quick_starrypad_dark,
        iconResLight = R.drawable.icon_quick_starrypad_light,
        bgDrawableDark = R.drawable.mark_quick_starrypad_dark,
        bgDrawableLight = R.drawable.mark_quick_starrypad_light,
        pointShadowColor = 0xFF72B8FF.toInt(),
        badgeLive = starryPadFileNameLiveData.map { it.isNotEmpty() }
    ) {
        // 手写板
        action = ACTION_BOOT_WRITE_PAD
        this
    },
    QuickBootApp(
        "File",
        R.string.quick_boot_file,
        iconResDark = R.drawable.icon_quick_file_dark,
        iconResLight = R.drawable.icon_quick_file_light,
        bgDrawableDark = R.drawable.mark_quick_file_dark,
        bgDrawableLight = R.drawable.mark_quick_file_light,
        pointShadowColor = 0xFFFFCA5C.toInt(),
        badgeLive = fileNameLiveData.map { newName ->
            newName != "null" && newName.isNotEmpty()
        },
    ) {
        // 文件
        action = ACTION_BOOT_FILE
        this
    },
    QuickBootApp(
        "File",
        R.string.quick_boot_options,
        iconResDark = R.drawable.icon_quick_settings_dark,
        iconResLight = R.drawable.icon_quick_settings_light,
        bgDrawableDark = R.drawable.mark_quick_setting_dark,
        bgDrawableLight = R.drawable.mark_quick_setting_light,
        pointShadowColor = 0xFFDA7AFF.toInt(),
        badgeLive = settingNewLiveData
    ) {
        // 设置
        action = ACTION_BOOT_NOTICE_SETTING
        this
    }
)