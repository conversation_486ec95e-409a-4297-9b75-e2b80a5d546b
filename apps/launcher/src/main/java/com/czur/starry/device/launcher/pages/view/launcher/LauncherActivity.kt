package com.czur.starry.device.launcher.pages.view.launcher

import android.annotation.SuppressLint
import android.app.Activity
import android.app.ActivityOptions
import android.bluetooth.BluetoothAdapter
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.hardware.display.DisplayManager
import android.media.AudioAttributes
import android.media.AudioManager
import android.media.SoundPool
import android.os.Bundle
import android.os.Environment
import android.os.PowerManager
import android.provider.Settings
import android.util.DisplayMetrics
import android.view.WindowManager
import com.czur.czurutils.extension.platform.newTask
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.Constants.starryHWInfo
import com.czur.starry.device.baselib.common.KEY_DEVICE_LAST_VERSION
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.common.VersionIndustry
import com.czur.starry.device.baselib.common.hw.StudioSeries
import com.czur.starry.device.baselib.utils.ONE_MIN
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.VersionUtil
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.fw.proxy.RkDisplayOutputManagerProxy
import com.czur.starry.device.baselib.utils.fw.proxy.SystemManagerProxy
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.baselib.utils.prop.getStringSystemProp
import com.czur.starry.device.baselib.utils.prop.setBooleanSystemProp
import com.czur.starry.device.baselib.utils.prop.setStringSystemProp
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.backdrop.BackdropManager
import com.czur.starry.device.launcher.databinding.ActivityLauncherBinding
import com.czur.starry.device.launcher.pages.view.launcher.LauncherMainActivity.Companion.KEY_BOOT_APP_PKG
import com.czur.starry.device.launcher.utils.hasInstanceThisApp
import com.czur.starry.device.launcher.utils.updateTask
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import kotlin.concurrent.thread

/**
 * Created by 陈丰尧 on 2022/3/22
 */
class LauncherActivity : Activity() {
    companion object {
        private const val TAG = "WelcomeActivity"
        private const val KEY_SHOW_BOOT_IMG = "system.czur.show_boot_img"

        private const val CHECK_PROVIDER_TIME = 5 * ONE_MIN // 等待Provider启动需要一分钟

        private const val VIDEO_FILE_FOLDER_NAME = "localMeetingVideo"
        private const val VIDEO_EXTENSION_TMP = "tmp"
        private const val ACTION_STARTUP_FINISH = "com.czur.starry.device.launcher.ACTION_STARTUP_FINISH"
    }

    private val scope = MainScope()
    private lateinit var binding: ActivityLauncherBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        scope.launch {
            completeUserSetupForSystem()    // 完成系统的设置
        }
        window.addFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
        binding = ActivityLauncherBinding.inflate(layoutInflater)
        setContentView(binding.root)
        scope.launch {
            if (starryHWInfo.series == StudioSeries) {
                bootSmallRoundScreen()
            }
        }
        if (getBooleanSystemProp(KEY_SHOW_BOOT_IMG, false)) {
            logTagV(TAG, "直接启动主画面")
            bootMain()
        } else {
            showBootImg()
            scope.launch {
                val isOTABoot = isOTABoot()
                if (isOTABoot) {
                    // 如果是OTA上来的话
                    logTagD(TAG, "当前是OTA升级后的第一次启动")
                    showOTABootUI()
                    delay(15 * ONE_SECOND)
                    setRkDisplay()
                } else {
                    logTagD(TAG, "正常重启")
                }
                enableBlueTooth()       // 开启蓝牙
                checkAppVersion()       // 执行升级任务
                checkTmpLocalVideo()    // 检查本地录像的临时文件
                BackdropManager.prepareBackdropManager() // 检查桌面背景
                checkProvider()         // 检查通过后, 执行下一步
                bootWatchService()      // 启动监控服务
                if (isOTABoot) {
                    logTagV(TAG, "准备重启")
                    rebootDevice()
                } else {
                    bootCZKeyStone()
                    playSound()
                }
            }
        }
    }

    /**
     * Android14 之后, 需要设置两个属性, 才能进入Launcher
     */
    private suspend fun completeUserSetupForSystem() = withContext(Dispatchers.Default){
        logTagD(TAG, "completeUserSetupForSystem")
        Settings.Secure.putInt(contentResolver, "user_setup_complete", 1)
        Settings.Global.putInt(contentResolver, "device_provisioned", 1)
    }

    private fun bootWatchService() {
        doWithoutCatch {
            logTagD(TAG, "bootWatchService")
            val intent = Intent().apply {
                `package` = "com.czur.starry.device.noticecenter"
                action = "com.czur.starry.device.noticecenter.BOOT_WATCH"
            }
            startService(intent)
        }
    }

    /**
     * 获取开机要启动的应用包名
     */
    private suspend fun getBootAppPkgName(): String {
        val savedPkg = SettingUtil.PersonalizationSetting.getLauncherBootAppPkg()
        logTagV(TAG, "savedPkg:${savedPkg}")
        if (savedPkg.isEmpty()) {
            logTagV(TAG, "没有设定开机启动app")
            return ""
        }
        if (!hasInstanceThisApp(savedPkg)) {
            logTagW(TAG, "$savedPkg 已经卸载")
            SettingUtil.PersonalizationSetting.setLauncherBootAppPkg("")
            return ""
        }
        return savedPkg
    }

    private fun rebootDevice() {
        logTagV(TAG, "重启设备")
        val pManager = getSystemService(Context.POWER_SERVICE) as PowerManager
        pManager.reboot("")
    }

    /**
     * 检查并删除本地的错误录像
     */
    private suspend fun checkTmpLocalVideo() {
        withContext(Dispatchers.IO) {
            logTagV(TAG, "checkTmpLocalVideo")
            val sdCardPath = Environment.getExternalStorageDirectory().path
            File(sdCardPath, VIDEO_FILE_FOLDER_NAME).also {
                if (!it.exists()) {
                    it.mkdirs()
                }
            }.listFiles { item ->
                item.extension == VIDEO_EXTENSION_TMP
            }?.forEach {
                logTagV(TAG, "删除临时文件:${it.name}")
                it.delete()
            }

            logTagV(TAG, "checkTmpLocalVideo 完成")
        }
    }

    /**
     * 开启蓝牙, 因为蓝牙开启的比较慢, 所以在这里先开启一次
     */
    @SuppressLint("MissingPermission")
    private suspend fun enableBlueTooth() = withContext(Dispatchers.IO) {
        if (Constants.versionIndustry == VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD) {
            logTagI(TAG, "军工版本, 不开启蓝牙")
            return@withContext
        }
        val btAdapter = BluetoothAdapter.getDefaultAdapter()
        if (!btAdapter.isEnabled) {
            logTagD(TAG, "开启蓝牙")
            btAdapter.enable()
        }
    }

    /**
     * 检查是否有需要更新的部分
     */
    private suspend fun checkAppVersion() {
        VersionUtil(application, updateTask).checkAndUpdate()
    }

    /**
     * 展示OTA上来的UI
     */
    private fun showOTABootUI() {
        binding.otaHintTv.show()
    }

    private suspend fun setRkDisplay() = withContext(Dispatchers.IO) {
        logTagD(TAG, "OTA升级后, 设置RK屏幕参数")
        val sysManager = SystemManagerProxy()

        val mode = sysManager.getDlp3439LooksMode()
        logTagD(TAG, "getDlp3439LooksMode:$mode")
        val bcsh = when (mode) {
            SystemManagerProxy.LooksMode.LOOKS_MODE_6500K -> {
                logTagD(TAG, "当前是6500K")
                sysManager.BCSH_6500K
            }

            SystemManagerProxy.LooksMode.LOOKS_MODE_8880K -> {
                logTagD(TAG, "当前是8880K")
                sysManager.BCSH_8880K
            }

            else -> {
                logTagD(TAG, "当前是其他模式, 按照888K处理")
                sysManager.BCSH_8880K
            }
        }
        if (bcsh.size != 4) {
            logTagE(TAG, "bcsh.size != 4")
            return@withContext
        }
        val b = bcsh[0]
        val c = bcsh[1]
        val s = bcsh[2]
        val h = bcsh[3]
        logTagD(TAG, "设置RK屏幕参数 b:$b c:$c s:$s h:$h")
        val rkDisplayOutputManager = RkDisplayOutputManagerProxy()
        rkDisplayOutputManager.setBrightness(b)
        rkDisplayOutputManager.setContrast(c)
        rkDisplayOutputManager.setSaturation(s)
        rkDisplayOutputManager.setHue(h)
        logTagD(TAG, "设置RK屏幕参数完成")
    }

    private suspend fun checkProvider() {
        val startCheckTime = System.currentTimeMillis()
        val checkDelayTime = ONE_SECOND
        var times = 1
        startService(Intent(this, CheckProviderService::class.java))
        while (!getBooleanSystemProp(KEY_CHECK_PROVIDER, false)) {
            times++
            if (System.currentTimeMillis() - startCheckTime > CHECK_PROVIDER_TIME) {
                // 这里之所以没有选择重启设备, 因为: 虽然重启设备大概率不会出现该情况, 但是在极端情况下, 设备就会不断重启
                // 无法进入Launcher, 让用户进入Launcher才能去抓log
                logTagW(TAG, "等待时间过长, 进入Launcher碰运气吧")
                return
            }
            if (times % 15 == 0) {
                // 重新启动一次Service, 保证Service是可以被启动的
                startService(Intent(this, CheckProviderService::class.java))
            }
            logTagW(TAG, "等待${checkDelayTime}ms后重新检查")
            delay(checkDelayTime)
        }
        logTagV(TAG, "各个Provider 已经启动完成")
    }

    /**
     * 判断是否是OTA之后的第一次启动
     */
    private suspend fun isOTABoot(): Boolean = withContext(Dispatchers.IO) {
        val currentVersion = Constants.FIRMWARE_NAME
        val lastVersion = getStringSystemProp(KEY_DEVICE_LAST_VERSION, "")
        setStringSystemProp(KEY_DEVICE_LAST_VERSION, currentVersion)
        currentVersion != lastVersion
    }


    /**
     * 启动主画面
     */
    private fun bootMain(bootPkg: String = "") {
        logTagD(TAG, "设置完毕, 发送广播")
        sendBroadcast(Intent(ACTION_STARTUP_FINISH))

        val intent = Intent(this, LauncherMainActivity::class.java)
        if (bootPkg.isNotEmpty()) {
            intent.putExtra(KEY_BOOT_APP_PKG, bootPkg)
        }
        startActivity(intent)
        finish()
    }

    private fun bootSmallRoundScreen() {
        logTagD(TAG, "启动小圆屏")
        val intent = Intent().apply {
            component = ComponentName("com.czur.starry.device.smallroundscreen", "com.czur.starry.device.smallroundscreen.AnimationActivity")
        }.newTask()

        val displayManager = getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
        val displays = displayManager.displays
        var targetDisplayId = -1
        for (display in displays) {
            val metrics = DisplayMetrics()
            display.getRealMetrics(metrics)
            if (metrics.widthPixels == 360 && metrics.heightPixels == 360) {
                targetDisplayId = display.displayId
                break
            }
        }

        if (targetDisplayId != -1) {
            val options = ActivityOptions.makeBasic().apply {
                launchDisplayId = targetDisplayId
            }
            logTagD(TAG, "找到了尺寸为 360x360 的屏幕")
            startActivity(intent, options.toBundle())
        } else {
            logTagD(TAG, "没有找到尺寸为 360x360 的屏幕")
        }
    }

    /**
     * 显示Welcome图片
     */
    private fun showBootImg() {
        if (Constants.starryHWInfo.salesLocale == StarryDevLocale.Overseas) {
            binding.bootIv.setImageResource(R.drawable.welcome_bg_overseas)
        }
        binding.bootIv.show()
    }

    /**
     * 播放启动音效
     */
    private fun playSound() {
        scope.launch {
            withContext(Dispatchers.IO) {
                setBooleanSystemProp(KEY_SHOW_BOOT_IMG, true)
            }
            val soundPoolMusic = SoundPool.Builder()
                .setMaxStreams(1)
                .setAudioAttributes(
                    AudioAttributes.Builder().setLegacyStreamType(AudioManager.STREAM_MUSIC).build()
                )
                .build()
            val musicSoundId: Int =
                soundPoolMusic.load(this@LauncherActivity, R.raw.welcome_sound, 1)

            delay(5 * ONE_SECOND)
            val bootPkg = getBootAppPkgName()
            soundPoolMusic.play(musicSoundId, 1F, 1F, 1, 0, 1F)
            delay(ONE_SECOND)
            thread {
                Thread.sleep(ONE_SECOND * 10)
                soundPoolMusic.release()
                logTagD(TAG, "音效播放完成,释放资源")
            }
            bootMain(bootPkg)
        }
    }

    private fun bootCZKeyStone() {
        logTagV(TAG, "启动自动对焦服务")
        val intent = Intent().apply {
            `package` = "com.czur.keystone"
            action = "com.czur.keystone.ACTION.START"
        }
        startService(intent)
    }

    override fun onDestroy() {
        doWithoutCatch {
            scope.cancel()
        }
        super.onDestroy()
    }

}