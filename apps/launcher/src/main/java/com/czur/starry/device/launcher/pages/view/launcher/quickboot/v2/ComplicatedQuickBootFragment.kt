package com.czur.starry.device.launcher.pages.view.launcher.quickboot.v2

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Intent
import android.graphics.Bitmap
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.Group
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.common.BootParam
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.utils.InternetStatus
import com.czur.starry.device.baselib.utils.blur
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.invisible
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnCreate
import com.czur.starry.device.baselib.utils.setDebounceTouchClickListener
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.setOnSuspendClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.utils.takeScreenShot
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.file.filelib.FileHandlerLive.fileShareCodeEnable
import com.czur.starry.device.file.filelib.FileHandlerLive.fileShareEnable
import com.czur.starry.device.launcher.R
import com.czur.starry.device.launcher.backdrop.BackdropManager
import com.czur.starry.device.launcher.backdrop.BackdropManager.BackdropEvent
import com.czur.starry.device.launcher.databinding.FragmentQuickBootComplicatedBinding
import com.czur.starry.device.launcher.meeting.NetMeetingAppStatus
import com.czur.starry.device.launcher.meeting.OtherMeetingFloat
import com.czur.starry.device.launcher.pages.view.launcher.AppInfoViewModel
import com.czur.starry.device.launcher.pages.view.launcher.KeyCodeVM
import com.czur.starry.device.launcher.pages.view.launcher.quickboot.PlaceHolderQuickBootApp
import com.czur.starry.device.launcher.pages.view.launcher.quickboot.QuickBootApp
import com.czur.starry.device.launcher.pages.view.launcher.quickboot.ShareViewModel
import com.czur.starry.device.launcher.pages.view.launcher.quickboot.quickBootApps
import com.czur.starry.device.launcher.utils.bootApp
import com.czur.starry.device.launcher.utils.bootAppByAction
import com.czur.starry.device.launcher.widget.OtherQuickBootAppIconIv


/**
 * Created by 陈丰尧 on 2022/8/19
 * 复杂的快速启动项
 */
class ComplicatedQuickBootFragment : CZViewBindingFragment<FragmentQuickBootComplicatedBinding>() {
    companion object {
        private const val TAG = "ComplicatedQuickBootFragment"
        private const val ANIM_DURATION = 100L
        private const val ANIM_SCALE_RATIO = 1.07f
    }

    private val shareViewModel: ShareViewModel by viewModels({ requireActivity() })
    private val keyCodeVM: KeyCodeVM by viewModels({ requireActivity() })


    // 应用信息的ViewModel
    private val appsVM: AppInfoViewModel by viewModels({ requireActivity() })


    private var shareName: TextView? = null
    private var wifiNameLl: LinearLayout? = null
    private var wifiNameTv: TextView? = null
    private var wifiNameIv: ImageView? = null
    private var ethernetTv: TextView? = null

    // 视频会议弹窗是否显示
    private var isNetMeetingShowing = false

    private val quickBootLayouts by lazy {
        listOf(
            binding.quickBoot1, // 视频会议
            binding.quickBoot2,
            binding.quickBoot4,
            binding.quickBoot5,
            binding.quickBoot6,
        )
    }

    private val iconViews by lazy {
        listOf(
            NetAppIconView(
                binding.netMeetingAppIcon1,
                binding.netMeetingAppName1,
                binding.netMeetingApp1
            ),
            NetAppIconView(
                binding.netMeetingAppIcon2,
                binding.netMeetingAppName2,
                binding.netMeetingApp2
            ),

            )
    }

    override fun FragmentQuickBootComplicatedBinding.initBindingViews() {

        if (Constants.starryHWInfo.salesLocale == StarryDevLocale.Overseas) {
            netMeetingAppTitle2Tv.gone()
        } else {
            netMeetingAppTitle2Tv.show()
        }

        quickBootLayouts.forEachIndexed { index, quickBootLayout ->

            val quickBootApp = quickBootApps[index]

            // 设置动画效果
            quickBootLayout.setOnHoverListener { view, event ->
                when (event.action) {
                    MotionEvent.ACTION_HOVER_EXIT -> scaleView(view, false, index)
                    MotionEvent.ACTION_HOVER_ENTER -> scaleView(view, true, index)
                }
                false
            }

            if (quickBootApp !is PlaceHolderQuickBootApp) {
                // 不是占位符
                setQuickBootUI(quickBootLayout, quickBootApp)
            } else if (index == 0) {
                // 视频会议模块
                initOtherMeetingUI()
            }

        }
    }

    /**
     * 初始化第三方网络会议UI
     */
    private fun initOtherMeetingUI() {
        fun scareIcon(view: View, scaleUp: Boolean) {
            view.pivotX = view.width.toFloat() / 2F
            view.pivotY = view.height.toFloat() / 2F
            if (scaleUp) {
                scaleUp(view)
            } else {
                scaleDown(view)
            }
        }

        binding.quickBoot1BlurIv.post {
            launch {
                BackdropManager.setBlurBitmapToBlurIv(binding.quickBoot1BlurIv)
            }
        }

        binding.quickBoot1.setOnDebounceClickListener {
            if (isNetMeetingShowing) {
                logTagV(TAG, "已经显示弹窗了, 忽略")
                return@setOnDebounceClickListener
            }
            isNetMeetingShowing = true
            // 弹出视频会议弹窗
            launch {
                try {
                    val bgImg = takeScreenShot(Bitmap.Config.ARGB_8888)?.blur(samplingRate = 4)
                    OtherMeetingFloat(
                        bgImg,
                        getString(R.string.float_title_other_net_meeting),
                        appsVM.otherNetMeetingAppsLive
                    )
                        .apply {
                            setOnDismissListener {
                                isNetMeetingShowing = false
                            }
                        }
                        .show()
                } catch (e: Exception) {
                    e.printStackTrace()
                    logTagE(TAG, "====${e}")
                }
            }
        }


        // 点击小项 click事件会影响Hover效果
        val netMeetingAppList =
            listOf(binding.byomAppIcon, binding.netMeetingAppIcon1, binding.netMeetingAppIcon2)
        netMeetingAppList.forEachIndexed { index, otherMeetingIconIv ->

            otherMeetingIconIv.setDebounceTouchClickListener {
                if (index == 0) {
                    bootAppByAction(BootParam.ACTION_BOOT_BYOM_GUIDE)
                } else {
                    bootOrInstallApp(index - 1)
                }
            }
            otherMeetingIconIv.setOnHoverListener { view, event ->
                when (event.action) {
                    MotionEvent.ACTION_HOVER_EXIT -> scareIcon(view, false)
                    MotionEvent.ACTION_HOVER_ENTER -> scareIcon(view, true)
                }
                false
            }
        }
    }

    /**
     * 启动对应的会议App
     */
    private fun bootOrInstallApp(index: Int) {
        val clickApp =
            appsVM.otherNetMeetingApps.getOrNull(index) ?: return   // 没有对应的App信息就直接Return
        when (clickApp.status) {
            NetMeetingAppStatus.INSTALL -> {
                logTagV(TAG, "启动App:${clickApp.appName}")
                bootApp(clickApp.pkgName)
            }

            NetMeetingAppStatus.UNINSTALL -> {
                if (clickApp.downloadProcess >= 0) {
                    logTagD(TAG, "正在下载, 不做任何操作")
                } else {
                    logTagD(TAG, "安装App:${clickApp.appName}")
                    appsVM.downloadNetMeetingApp(clickApp)
                }
            }
        }
    }


    /**
     * 设置QuickBootApp的行为
     */
    private fun setQuickBootUI(quickBootLayout: View, quickBootApp: QuickBootApp) {
        val quickBootBgIv = quickBootLayout.findViewById<ImageView>(R.id.quickBootBgIv)
        val quickBootTitleTv = quickBootLayout.findViewById<TextView>(R.id.quickBootTitleTv)
        val quickBootBadgeIv = quickBootLayout.findViewById<ImageView>(R.id.quickBootBadgeIv)
        val quickBootContent = quickBootLayout.findViewById<TextView>(R.id.quickBootContent)
        val quickBootBadgePoint = quickBootLayout.findViewById<View>(R.id.quickBootBadgePoint)
        val fileCodeTitleTv = quickBootLayout.findViewById<TextView>(R.id.fileCodeTitleTv)
        val fileCodeContentTv = quickBootLayout.findViewById<TextView>(R.id.fileCodeContentTv)

        quickBootBgIv.setImageResource(quickBootApp.imgRes)
        quickBootTitleTv.setText(quickBootApp.nameRes)

        quickBootApp.badgeLive?.observe(this) { new ->
            // 显示角标的部分
            if (new) {
                quickBootBadgeIv.show()
            } else {
                quickBootBadgeIv.gone()
            }
        }

        quickBootApp.newFileLive?.observe(this) { newName ->
            // 显示新增文件名字
            if (newName != "null" && newName.isNotEmpty()) {
                logTagV(TAG, "跟新文件名称:${newName}")
                quickBootBadgeIv.show()
                quickBootContent.text = newName
                quickBootContent.show()
                quickBootBadgePoint.show()
            } else {
                logTagV(TAG, "隐藏新文件")
                quickBootBadgeIv.gone()
                quickBootContent.gone()
                quickBootBadgePoint.gone()
            }
        }


        quickBootApp.fileCodeLive?.observe(this) { fileCode ->
            // 显示传输文件校验码
            if (fileShareEnable && fileShareCodeEnable) {
                fileCodeTitleTv.show()
                fileCodeContentTv.text = fileCode
                fileCodeContentTv.show()
            } else {
                fileCodeTitleTv.gone()
                fileCodeContentTv.gone()
            }
        }

        // 是否显示投屏码
        if (quickBootApp.showShare) {
            quickBootLayout.findViewById<TextView>(R.id.shareNameTv).let {
                it.show()
                shareName = it
            }
            quickBootLayout.findViewById<LinearLayout>(R.id.wifiNameLl).let {
                it.show()
                wifiNameLl = it
            }
            quickBootLayout.findViewById<TextView>(R.id.wifiNameTv).let {
                it.show()
                wifiNameTv = it
            }
            quickBootLayout.findViewById<ImageView>(R.id.wifiNameIv).let {
                it.show()
                wifiNameIv = it
            }
            quickBootLayout.findViewById<TextView>(R.id.ethernetTv).let {
                it.show()
                ethernetTv = it
            }
        } else {
            quickBootLayout.findViewById<TextView>(R.id.shareNameTv).let {
                it.gone()
            }
            quickBootLayout.findViewById<LinearLayout>(R.id.wifiNameLl).let {
                it.gone()
            }
        }

        quickBootLayout.setOnSuspendClickListener(lifecycleScope) {
            if (!quickBootApp.enable()) {
                logTagV(TAG, "${getString(quickBootApp.nameRes)} 禁止启动")
                quickBootApp.cannotOpenToast?.let {
                    toast(it)
                }
                return@setOnSuspendClickListener
            }

            // 需要登录的模块
            if (quickBootApp.needLogin && !UserHandler.isLogin) {
                logTagD(TAG, "${getString(quickBootApp.nameRes)} 需要登录")
                toast(R.string.toast_need_login)
                return@setOnSuspendClickListener
            }
            val bootIntent = quickBootApp.bootIntent
            if (bootIntent != null) {
                bootIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                try {
                    logTagD(TAG, "启动QuickBoot模块:${getString(quickBootApp.nameRes)}")
                    startActivity(bootIntent)
                } catch (e: Exception) {
                    logTagE(TAG, "quickBoot 启动失败:", tr = e)
                    quickBootApp.cannotOpenToast?.let {
                        toast(it)
                    }
                }
            } else {
                quickBootApp.cannotOpenToast?.let {
                    toast(it)
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        launch {
            shareViewModel.loadShareInfo()
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        keyCodeVM.scrollTime.observe(viewLifecycleOwner) {
            binding.quickBoot1BlurIv.invalidate()
        }

        shareViewModel.wifiSSIDNameLive.observe(this) {
            wifiNameIv?.gone(it.isEmpty())
            wifiNameTv?.gone(it.isEmpty())
            wifiNameTv?.text = it
            val connect = shareViewModel.ethernetWorkLive.value == InternetStatus.CONNECT
            ethernetTv?.gone(it.isNotEmpty() || !connect)

        }

        shareViewModel.ethernetWorkLive.observe(this) {
            val wifiNet = shareViewModel.wifiSSIDNameLive.value ?: ""
            wifiNameLl?.gone(it == InternetStatus.DISCONNECT)
            if (it == InternetStatus.CONNECT) {
                ethernetTv?.gone(wifiNet.isNotEmpty())
            }
        }


        shareViewModel.showShareNameLive.observe(this) {
            shareName?.let { tv ->
                tv.text = it
            }
        }

        // 第三方网络会议应用
        appsVM.otherNetMeetingAppsLive.observe(this) { otherMeetingApps ->
            /*
            Launcher上的显示逻辑:
            1. 如果有已安装的应用, 则只显示已安装的应用
            2. 一个已安装应用都没有, 显示后台配置列表中的前三个
             */

            val showApps = otherMeetingApps
                .filter {
                    it.status == NetMeetingAppStatus.INSTALL
                }
                .ifEmpty {
                    // 没有安装的应用
                    otherMeetingApps
                }
                .take(iconViews.size)        // 最多2个
            repeat(iconViews.size) { index ->
                val appView = iconViews[index]
                showApps.getOrNull(index)?.let {
                    appView.groupView.show()
                    appView.iconView.updateIcon(it)
                    appView.nameView.text = it.appName
                } ?: kotlin.run {
                    appView.groupView.invisible()
                }
            }

        }

        repeatCollectOnCreate(BackdropManager.eventFlow) {
            if (it == BackdropEvent.CHANGE) {
                binding.quickBoot1BlurIv.post {
                    launch {
                        BackdropManager.setBlurBitmapToBlurIv(binding.quickBoot1BlurIv)
                    }
                }
            }
        }

        binding.byomAppIcon.updateByomIcon()
    }

    private fun scaleView(view: View, scaleUp: Boolean, index: Int) {
        /**
         * 设置中心点位置
         */
        when (index) {
            0 -> {
                // 右下角
                view.pivotX = view.width.toFloat() / 2F
                view.pivotY = view.height.toFloat()
            }

            1 -> {
                // 左下角
                view.pivotX = 0F
                view.pivotY = view.height.toFloat()
            }

            2 -> {
                // 左下角
                view.pivotX = 0F
                view.pivotY = view.height.toFloat()
            }

            3 -> {
                // 右上角
                view.pivotX = view.width.toFloat()
                view.pivotY = 0F
            }

            4 -> {
                // 顶边中间
                view.pivotX = view.width.toFloat() / 2F
                view.pivotY = 0F
            }

            5 -> {
                // 左上角
                view.pivotX = 0F
                view.pivotY = 0F
            }
        }
        if (scaleUp) {
            scaleUp(view)
            if (index == 0) {
                val x = view.width * (ANIM_SCALE_RATIO - 1) / 2F // 控件移动的距离
                quickBootLayouts[index + 1].tranX(x) // 右侧控件
            }
            if (index == 4) {
                val x = view.width * (ANIM_SCALE_RATIO - 1) / 2F // 控件移动的距离
                quickBootLayouts[index - 1].tranX(-x)  // 左侧控件
                quickBootLayouts[index + 1].tranX(x) // 右侧控件
            }
        } else {
            scaleDown(view)
            if (index == 0) {
                val x = view.width * (ANIM_SCALE_RATIO - 1) / 2F // 控件移动的距离
                quickBootLayouts[index + 1].tranX(0F) // 右侧控件
            }
            if (index == 4) {
                quickBootLayouts[index - 1].tranX(0F)  // 左侧控件
                quickBootLayouts[index + 1].tranX(0F) // 右侧控件
            }
        }
    }

    private fun View.tranX(tranX: Float) {
        val anim = ObjectAnimator.ofFloat(this, "translationX", tranX)
        anim.duration = ANIM_DURATION
        anim.start()
    }

    /**
     * 放大
     */
    private fun scaleUp(view: View) {
        val animSet = AnimatorSet()
        val objectAnimatorX = ObjectAnimator.ofFloat(view, "scaleX", ANIM_SCALE_RATIO)
        val objectAnimatorY = ObjectAnimator.ofFloat(view, "scaleY", ANIM_SCALE_RATIO)
        objectAnimatorX.duration = ANIM_DURATION
        objectAnimatorY.duration = ANIM_DURATION
        animSet.play(objectAnimatorX).with(objectAnimatorY)
        animSet.start()
    }

    /**
     * 缩小
     */
    private fun scaleDown(view: View) {
        val animSet = AnimatorSet()
        val objectAnimatorX = ObjectAnimator.ofFloat(view, "scaleX", 1.0f)
        val objectAnimatorY = ObjectAnimator.ofFloat(view, "scaleY", 1.0f)
        objectAnimatorX.duration = ANIM_DURATION
        objectAnimatorY.duration = ANIM_DURATION
        animSet.play(objectAnimatorX).with(objectAnimatorY)
        animSet.start()
    }

    /**
     * IconView
     */
    private data class NetAppIconView(
        val iconView: OtherQuickBootAppIconIv,
        val nameView: TextView,
        val groupView: Group
    )
}