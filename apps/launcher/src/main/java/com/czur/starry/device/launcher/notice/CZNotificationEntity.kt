package com.czur.starry.device.launcher.notice

import android.graphics.Bitmap
import com.czur.starry.device.baselib.cznotification.CZNotificationIntentWrapper

/**
 * Created by 陈丰尧 on 2024/8/13
 */
data class CZNotificationEntity(
    val id: Int,
    val belongPkgName: String,      // 所属包名
    val icon: Bitmap,                // 图标
    val title: String,                  // 标题
    val intentWrapper: CZNotificationIntentWrapper,       // 点击通知的跳转
    val removeAfterExecution: <PERSON><PERSON><PERSON>,  // 执行后移除
)