package com.czur.starry.device.launcher.widget

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.animation.LinearInterpolator
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.content.ContextCompat
import com.czur.starry.device.baselib.utils.scaleXY
import com.czur.starry.device.launcher.R
import kotlin.random.Random

/**
 * Created by 陈丰尧 on 2022/3/31
 * App抖动
 */
@SuppressLint("ClickableViewAccessibility")
class ShakeIV @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : AppCompatImageView(context, attrs, defStyleAttr) {
    companion object {
        private const val MAX_OFF_SET_ANGLE_MIN = 1  // 每次抖动的最大偏移量
        private const val MAX_OFF_SET_ANGLE_MAX = 5  // 每次抖动的最大偏移量
        private const val MAX_OFF_SET_X = 4
        private const val MAX_OFF_SET_Y = 3
        private const val SHAKE_CYCLE_MAX = 15  // 抖动的最大次数
        private const val SHAKE_CYCLE_MIN = 12  // 抖动的最小次数

        private const val DURATION_MIN = 2500L
        private const val DURATION_MAX = 3000L
    }

    private val shakeAnim by lazy {
        initAnim()
    }

    private var upTime = 0L

    private fun initAnim(): AnimatorSet {
        val shakeCount = Random.nextInt(SHAKE_CYCLE_MIN, SHAKE_CYCLE_MAX)
        val arr = FloatArray(shakeCount)
        val arrX = FloatArray(shakeCount)
        val yCount = Random.nextInt(SHAKE_CYCLE_MIN, SHAKE_CYCLE_MAX)
        val arrY = FloatArray(yCount)
        repeat(yCount) {
            val y = Random.nextInt(1, MAX_OFF_SET_Y).toFloat()
            if (it % 2 == 0) {
                arrY[it] = y
            } else {
                arrY[it] = -y
            }
        }
        // 让抖动的初始方向也不一样
        val direction = if (Random.nextBoolean()) 1 else -1
        repeat(shakeCount) {
            val angle = Random.nextInt(MAX_OFF_SET_ANGLE_MIN, MAX_OFF_SET_ANGLE_MAX).toFloat()
            val x = Random.nextInt(1, MAX_OFF_SET_X).toFloat()
            if (it % 2 == 0) {
                arr[it] = angle * direction
                arrX[it] = x * direction
            } else {
                arr[it] = -angle * direction
                arrX[it] = -x * direction
            }
        }
        val anim = AnimatorSet()

        val angleAnim = ObjectAnimator.ofFloat(this, "rotation", *arr)

        val angleDuration = Random.nextLong(DURATION_MIN, DURATION_MAX)
        angleAnim.duration = angleDuration
        angleAnim.repeatCount = ValueAnimator.INFINITE
        angleAnim.repeatMode = ValueAnimator.REVERSE
        angleAnim.interpolator = LinearInterpolator()

        val xAnim = ObjectAnimator.ofFloat(this, "translationX", *arrX)
        xAnim.duration = angleDuration
        xAnim.repeatCount = ValueAnimator.INFINITE
        xAnim.repeatMode = ValueAnimator.REVERSE
        xAnim.interpolator = LinearInterpolator()

        val yAnim = ObjectAnimator.ofFloat(this, "translationY", *arrY)
        yAnim.duration = Random.nextLong(DURATION_MIN, DURATION_MAX)
        yAnim.repeatCount = ValueAnimator.INFINITE
        yAnim.repeatMode = ValueAnimator.REVERSE
        yAnim.interpolator = LinearInterpolator()

        anim.playTogether(angleAnim, xAnim, yAnim)

        return anim
    }


    init {
        setOnHoverListener { v, event ->
            if (shakeAnim.isRunning) return@setOnHoverListener false  // 卸载模式不响应
            if (System.currentTimeMillis() - upTime < 20) return@setOnHoverListener false  // 点击完成后的那次鼠标进入, 不响应
            when (event.action) {
                MotionEvent.ACTION_HOVER_ENTER -> {
                    v.animate().scaleXY(1.1F).setDuration(150).start()
                }

                MotionEvent.ACTION_HOVER_EXIT -> {
                    v.animate().scaleXY(1F).setDuration(150).start()
                }
            }
            true
        }

        setOnTouchListener { v, event ->
            if (shakeAnim.isRunning) return@setOnTouchListener false  // 卸载模式不响应
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    v.animate().scaleXY(0.9F).setDuration(150).start()
                    setColorFilter(ContextCompat.getColor(context, R.color.filter_app_click))
                    return@setOnTouchListener true
                }

                MotionEvent.ACTION_UP,
                MotionEvent.ACTION_CANCEL -> {
                    upTime = System.currentTimeMillis()
                    v.animate().scaleXY(1F).setDuration(150).start()
                    colorFilter = null
                }
            }
            false
        }
    }

    /**
     * 开始抖动
     */
    fun startShake() {
        shakeAnim.start()
    }

    /**
     * 停止抖动
     */
    fun stopShake() {
        shakeAnim.cancel()
        rotation = 0F
        translationX = 0F
        translationY = 0F
    }

}