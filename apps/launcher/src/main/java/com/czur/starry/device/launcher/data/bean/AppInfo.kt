package com.czur.starry.device.launcher.data.bean

import android.graphics.drawable.Drawable
import com.czur.starry.device.launcher.meeting.NetMeetingAppStatus

/**
 * Created by 陈丰尧 on 2/20/21
 */
data class AppInfo(
    val appName: String,
    val pkgName: String,
    val appIcon: Drawable,
    val installTime: Long,
    val canUninstall: Boolean,
    val needCamera: Boolean = false
) {
    val copyDrawable: Drawable
        get() = appIcon.constantState!!.newDrawable()   // 复制一个新的Drawable,防止和其他地方共用一个Drawable
}

data class LaunchPadApp(
    val appInfo: AppInfo,
    val uninstallMode: Boolean
) {
    val showUninstall: Boolean
        get() = uninstallMode && appInfo.canUninstall

    /**
     * 是否是成者自己的应用
     */
    val isCZURApp: Boolean
        get() = appInfo.pkgName.contains("czur")
}

data class NetMeetingApp(
    val appName: String,                // 名称
    val pkgName: String,                // 包名
    val appIcon: Drawable?,             // 图标
    val iconUrl: String,                // 网络图标
    val status: NetMeetingAppStatus,    // 应用状态: 已安装, 未安装
    val appID: Long,                    // 对应AppStore的ID
    val downloadUrl: String,            // 下载地址
    val downloadProcess: Int,           // 下载进度
    val netVersion: Int,                 // 网络版本
    val downloadEstimatedSize: Long,     // 下载大小
    val firstInstallTime: Long,          // 安装成功时间
)