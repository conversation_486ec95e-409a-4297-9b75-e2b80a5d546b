package com.czur.starry.device.launcher.widget

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.View
import com.czur.starry.device.baselib.utils.getTopControlBarHeight
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.MainScope

/**
 * Created by 陈丰尧 on 2021/11/1
 */
class BlurImageView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr), CoroutineScope by MainScope() {
    var bitmap: Bitmap? = null
        set(value) {
            field = value
            invalidate()
        }
    val hasBlurBitmap: Boolean
        get() = bitmap != null
    private val localArr = IntArray(2)

    private val srcRect = Rect()
    private val destRect = Rect()

    private val outPath = Path()

    private val paint = Paint().apply {
        isAntiAlias = true
        isDither = true
        isFilterBitmap = true
    }
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        outPath.reset()
        outPath.addRoundRect(
            0F, 0F, width.toFloat(), height.toFloat(),
            15F, 15F, Path.Direction.CW
        )
        canvas.clipPath(outPath)
        bitmap?.let {
            // 获取view的位置
            getLocationOnScreen(localArr)
            var y = localArr[1] - getTopControlBarHeight()
            var offset = 0
            if (y < 0) {
                offset = y
                y = 0
            }

            // 计算src位置
            srcRect.set(0, y, width, y + height + offset)
            destRect.set(0, -offset, width, height)

            canvas.drawBitmap(it, srcRect, destRect, paint)
        }
    }


}