package com.czur.starry.device.launcher.pages.view.launcher.custom

import android.graphics.Outline
import android.os.Bundle
import android.view.View
import android.view.ViewOutlineProvider
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnCreate
import com.czur.starry.device.launcher.backdrop.BackdropManager
import com.czur.starry.device.launcher.backdrop.BackdropManager.BackdropColor.*
import com.czur.starry.device.launcher.backdrop.BackdropManager.BackdropEvent
import com.czur.starry.device.launcher.databinding.FragmentLeftTimeBinding
import com.czur.starry.device.launcher.pages.view.launcher.KeyCodeVM
import com.czur.starry.device.launcher.pages.view.launcher.LauncherMainViewModel
import com.czur.starry.device.launcher.widget.BlurImageView
import kotlin.getValue


/**
 * Created by 陈丰尧 on 2023/5/19
 */
private const val TAG = "LeftTimeFragment"

class LeftTimeFragment : CZViewBindingFragment<FragmentLeftTimeBinding>() {
    private val keyCodeVM: KeyCodeVM by viewModels({ requireActivity() })
    private val mainViewModel: LauncherMainViewModel by activityViewModels()


    override fun FragmentLeftTimeBinding.initBindingViews() {
        initBlurBg(timeBiv)

        binding.root.outlineProvider = object : ViewOutlineProvider() {
            override fun getOutline(view: View?, outline: Outline?) {
                // 圆角矩形
                outline?.setRoundRect(
                    0, 0, view!!.width, view.height, 10F
                )
            }

        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        keyCodeVM.scrollTime.observe(viewLifecycleOwner) {
            binding.timeBiv.invalidate()
        }

        repeatCollectOnCreate(mainViewModel.backdropColorFlow) {
            when (it) {
                Dark -> binding.root.apply {
                    outlineSpotShadowColor = 0xFF6186FF.toInt()
                    outlineAmbientShadowColor = 0xFF6186FF.toInt()
                }

                Light -> binding.root.apply {
                    outlineSpotShadowColor = 0xFFA6C1FF.toInt()
                    outlineAmbientShadowColor = 0xFFA6C1FF.toInt()
                }
            }

        }

        repeatCollectOnCreate(BackdropManager.eventFlow) {
            if (it == BackdropEvent.CHANGE) {
                logTagD(TAG, "壁纸发生变化了,重新获取模糊图")
                // 重新设置模糊图片
                initBlurBg(binding.timeBiv)
            }
        }
    }

    private fun initBlurBg(biv: BlurImageView) {
        biv.post {
            launch {
                BackdropManager.setBlurBitmapToBlurIv(biv)
            }
        }
    }
}