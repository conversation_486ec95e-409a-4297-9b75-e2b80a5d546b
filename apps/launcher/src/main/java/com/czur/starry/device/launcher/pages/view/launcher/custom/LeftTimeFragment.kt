package com.czur.starry.device.launcher.pages.view.launcher.custom

import android.os.Bundle
import androidx.fragment.app.viewModels
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnCreate
import com.czur.starry.device.launcher.backdrop.BackdropManager
import com.czur.starry.device.launcher.backdrop.BackdropManager.BackdropEvent
import com.czur.starry.device.launcher.databinding.FragmentLeftTimeBinding
import com.czur.starry.device.launcher.pages.view.launcher.KeyCodeVM
import com.czur.starry.device.launcher.widget.BlurImageView


/**
 * Created by 陈丰尧 on 2023/5/19
 */
private const val TAG = "LeftTimeFragment"

class LeftTimeFragment : CZViewBindingFragment<FragmentLeftTimeBinding>() {
    private val keyCodeVM: KeyCodeVM by viewModels({ requireActivity() })

    override fun FragmentLeftTimeBinding.initBindingViews() {
        initBlurBg(timeBiv)
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        keyCodeVM.scrollTime.observe(viewLifecycleOwner) {
            binding.timeBiv.invalidate()
        }

        repeatCollectOnCreate(BackdropManager.eventFlow) {
            if (it == BackdropEvent.CHANGE) {
                logTagD(TAG, "壁纸发生变化了,重新获取模糊图")
                // 重新设置模糊图片
                initBlurBg(binding.timeBiv)
            }
        }
    }

    private fun initBlurBg(biv: BlurImageView) {
        biv.post {
            launch {
                BackdropManager.setBlurBitmapToBlurIv(biv)
            }
        }
    }
}