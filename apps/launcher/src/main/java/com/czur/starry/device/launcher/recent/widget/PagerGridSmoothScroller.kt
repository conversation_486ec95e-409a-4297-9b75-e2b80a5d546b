package com.czur.starry.device.launcher.recent.widget

import android.content.Context
import android.graphics.Rect
import android.util.DisplayMetrics
import android.util.Log
import android.view.View
import androidx.recyclerview.widget.LinearSmoothScroller
import androidx.recyclerview.widget.RecyclerView
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min

internal class PagerGridSmoothScroller(
    context: Context,
    private val mLayoutManager: PagerGridLayoutManager
) : LinearSmoothScroller(context) {
    companion object {
        private const val TAG = "PagerGridSmoothScroller"

        /**
         * 滚动速度
         */
        const val MILLISECONDS_PER_INCH: Float = 200f

        /**
         * 最大滚动时间
         */
        const val MAX_SCROLL_ON_FLING_DURATION: Int = 200 //ms

        @JvmStatic
        fun calculateDx(manager: PagerGridLayoutManager, snapRect: Rect, targetRect: Rect): Int {
            if (!manager.canScrollHorizontally()) {
                return 0
            }
            return targetRect.left - snapRect.left
        }

        @JvmStatic
        fun calculateDy(manager: PagerGridLayoutManager, snapRect: Rect, targetRect: Rect): Int {
            if (!manager.canScrollVertically()) {
                return 0
            }
            return targetRect.top - snapRect.top
        }

        fun calculateDx(
            manager: PagerGridLayoutManager,
            snapRect: Rect,
            targetRect: Rect,
            isLayoutToEnd: Boolean
        ): Int {
            if (!manager.canScrollHorizontally()) {
                return 0
            }
            return if (isLayoutToEnd) (targetRect.left - snapRect.left) else (targetRect.right - snapRect.right)
        }

        fun calculateDy(
            manager: PagerGridLayoutManager,
            snapRect: Rect,
            targetRect: Rect,
            isLayoutToEnd: Boolean
        ): Int {
            if (!manager.canScrollVertically()) {
                return 0
            }
            return if (isLayoutToEnd) (targetRect.top - snapRect.top) else (targetRect.bottom - snapRect.bottom)
        }
    }

    /**
     * 该方法会在targetSnapView被layout出来的时候调用。
     */
    override fun onTargetFound(targetView: View, state: RecyclerView.State, action: Action) {
        val layoutManager = layoutManager
        if (layoutManager is PagerGridLayoutManager) {

            val targetPosition = layoutManager.getPosition(targetView)

            val pointF = computeScrollVectorForPosition(targetPosition)
                ?: return //为null，则不处理

            var isLayoutToEnd = pointF.x > 0 || pointF.y > 0
            if (layoutManager.shouldHorizontallyReverseLayout()) {
                isLayoutToEnd = !isLayoutToEnd
            }
            val snapRect = if (isLayoutToEnd) {
                layoutManager.startSnapRect
            } else {
                layoutManager.endSnapRect
            }
            val targetRect = Rect()
            layoutManager.getDecoratedBoundsWithMargins(targetView, targetRect)
            val dx = calculateDx(layoutManager, snapRect, targetRect, isLayoutToEnd)
            val dy = calculateDy(layoutManager, snapRect, targetRect, isLayoutToEnd)
            val time = calculateTimeForDeceleration(
                max(abs(dx.toDouble()), abs(dy.toDouble())).toInt()
            )

            if (time > 0) {
                action.update(dx, dy, time, mDecelerateInterpolator)
            } else {
                //说明滑动完成，计算页标
                layoutManager.calculateCurrentPagerIndexByPosition(targetPosition)
            }
        }
    }

    /**
     * 不可过小，不然可能会出现划过再回退的情况
     *
     * @param displayMetrics
     * @return 值越大，滚动速率越慢，反之
     */
    public override fun calculateSpeedPerPixel(displayMetrics: DisplayMetrics): Float {
        return mLayoutManager.millisecondPreInch / displayMetrics.densityDpi
    }

    /**
     * 为避免长时间滚动，设置一个最大滚动时间
     *
     * @param dx 滚动的像素距离
     * @return 值越大，滑动时间越长，滚动速率越慢，反之
     */
    public override fun calculateTimeForScrolling(dx: Int): Int {
        val time = min(
            mLayoutManager.maxScrollOnFlingDuration.toDouble(),
            super.calculateTimeForScrolling(dx).toDouble()
        ).toInt()
        Log.i(TAG, "calculateTimeForScrolling-time: $time")
        return time
    }
}
