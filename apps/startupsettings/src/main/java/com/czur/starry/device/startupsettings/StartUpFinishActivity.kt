package com.czur.starry.device.startupsettings

import android.content.Intent
import android.os.Bundle
import android.view.KeyEvent
import androidx.fragment.app.commit
import com.czur.czurutils.extension.platform.startService
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.BaseActivity
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.VersionIndustry
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnCreate
import com.czur.starry.device.startupsettings.fragment.TouchPadGuideFragment
import kotlinx.coroutines.delay
import kotlin.system.exitProcess

/**
 * Created by 陈丰尧 on 2022/2/28
 * 初期设定结束画面
 */
private const val TAG = "StartUpFinishActivity"

class StartUpFinishActivity : BaseActivity() {
    override fun getLayout(): Int = R.layout.window_startup_finish

    override val closeBootAnim: Boolean = false
    private var autoFinish = false
    override fun initViews() {
        super.initViews()

        if (Constants.versionIndustry == VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD) {
            logTagI(TAG, "军工版本, 直接结束")
            moveToFinish()
        } else if (Constants.starryHWInfo.model == StarryModel.StudioModel.StudioSPlus) {
            logTagI(TAG, "StudioSPlus版本, 直接结束")
            moveToFinish()
        } else {
            supportFragmentManager.commit {
                add(R.id.finishContainer, TouchPadGuideFragment())
            }
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        repeatCollectOnCreate(StartUpApp.instance.eventFlow) {
            when (it) {
                StartUpApp.StartupEvent.STARTUP_FINISH -> {
                    logTagI(TAG, "收到初期设定结束事件")
                    bootLauncher()
                }
            }
        }
    }

    fun moveToFinish() {
        logTagD(TAG, "moveToFinish")
        startService<StartUpFinishSettingWindow>()
    }

    override fun onStop() {
        super.onStop()
        if (autoFinish) {
            logTagI(TAG, "Launcher 已经启动,结束自己 autoFinish")
            finish()
        }
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        // 拦截所有事件
        return true
    }

    /**
     * 手动启动Launcher后, 再结束初期设定, 这样好看一些
     */
    private fun bootLauncher() {
        logTagD(TAG, "bootLauncher")
        autoFinish = true
        // startLauncher
        val intent = Intent()
        // 这里直接
        intent.setClassName(
            "com.czur.starry.device.launcher",
            "com.czur.starry.device.launcher.pages.view.launcher.LauncherMainActivity"
        )
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        startActivity(intent)

        // 这是保底机制
        launch {
            delay(5 * ONE_SECOND)
            if (!isFinishing) {
                logTagW(TAG, "还没有触发自动Finish!!!")
                finish()
            }
        }
    }
}