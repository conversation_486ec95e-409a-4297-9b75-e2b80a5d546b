package com.czur.starry.device.personalcenter.account.ai.order

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import androidx.navigation.fragment.findNavController
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.invisible
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.utils.toastFail
import com.czur.starry.device.personalcenter.databinding.LayoutAiTransOrderQrCodeBinding
import kotlin.getValue

/**
 * Created by 陈丰尧 on 2025/4/23
 */
private const val TAG = "OrderQrCodeFragment"

class OrderQrCodeFragment : CZViewBindingFragment<LayoutAiTransOrderQrCodeBinding>() {
    private val orderViewModel: OrderViewModel by activityViewModels()

    override fun LayoutAiTransOrderQrCodeBinding.initBindingViews() {
        titleBar.onBackClick = {
            // 返回按钮点击事件
            findNavController().navigateUp()
        }

        // 添加刷新二维码的点击事件
        loadingErrorBgView.setOnDebounceClickListener {
            refreshQrCode()
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        refreshQrCode()
    }

    private fun refreshQrCode() {
        launch {
            binding.loadingErrorGroup.gone()
            binding.qrCodeIv.invisible()
            binding.progressBar.show()

            val qrCodeResult = orderViewModel.getQrCode()
            binding.progressBar.gone()

            val qrCode = qrCodeResult.getOrNull()
            if (qrCode == null) {
                logTagE(TAG, "获取二维码失败")
                binding.loadingErrorGroup.show()
            } else {
                binding.qrCodeIv.show()
                binding.qrCodeIv.setImageBitmap(qrCode)
            }
        }
    }
}