package com.czur.starry.device.personalcenter.account

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.utils.ONE_HOUR
import com.czur.starry.device.baselib.utils.ONE_MIN
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.basic.alsoSuccess
import com.czur.starry.device.baselib.utils.getString
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.personalcenter.R
import com.czur.starry.device.personalcenter.bean.AITransMemberInfo
import com.czur.starry.device.personalcenter.net.AccountManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter

/**
 * Created by 陈丰尧 on 2025/4/24
 */
private const val TAG = "UserAccountViewModel"

class UserAccountViewModel(application: Application) : AndroidViewModel(application) {
    private val _aiTransTimeFlow = MutableStateFlow("")
    val aiTransTimeFlow = _aiTransTimeFlow

    private val _userInputNickNameFlow = MutableStateFlow("")
    val nickNameSaveBtnEnableFlow = _userInputNickNameFlow.map {
        it.isNotEmpty()
    }

    fun updateUserInputNickName(nickName: String) {
        _userInputNickNameFlow.value = nickName
    }

    suspend fun updateNickName(): Result<Unit> {
        val newName = _userInputNickNameFlow.value
        val oldName = UserHandler.nickname
        if (newName == oldName) {
            logTagD(TAG, "新昵称和旧昵称相同，不需要更新")
            return Result.success(Unit)
        }
        logTagI(TAG, "更新昵称: $oldName -> $newName")

        return AccountManager.updateNickName(newName).alsoSuccess {
            logTagV(TAG, "昵称修改成功")
            launch(Dispatchers.Default) {
                UserHandler.nickname = newName
            }
        }
    }


    fun refreshAITransTime() {
        launch {
            withContext(Dispatchers.IO) {
                val miaoResult = AccountManager.getAITransInfo()
                if (miaoResult.isSuccess) {
                    miaoResult.getOrNull()?.let {
                        formatAsrExpireTime(it)
                    }?.let {
                        _aiTransTimeFlow.value = it
                    }

                } else {
                    logTagW(TAG, "获取AITrans时间失败")
                    _aiTransTimeFlow.value = ""
                }
            }
        }
    }

    fun formatAsrExpireTime(aiInfo: AITransMemberInfo): String? {
        logTagI(TAG, "formatAsrExpireTime: $aiInfo")
        val infoStr = buildString {
            if (aiInfo.expireTime >= System.currentTimeMillis()) {
                val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
                val timeStr = Instant.ofEpochMilli(aiInfo.expireTime)
                    .atZone(ZoneId.systemDefault())
                    .format(formatter)
                append(getString(R.string.str_ai_trans_valid_until, timeStr))
            }
            if (aiInfo.remaining > 0) {
                val time = aiInfo.remaining
                val hours = time / ONE_HOUR
                val minutes = (time / ONE_MIN) % 60
                val seconds = (time / ONE_SECOND) % 60

                // Format with proper zero-padding
                val hoursStr = hours.toString().padStart(2, '0')
                val minutesStr = minutes.toString().padStart(2, '0')
                val secondsStr = seconds.toString().padStart(2, '0')

                val timeStr = if (hours > 0) {
                    // Format as HH:MM:SS if hours > 0
                    "$hoursStr:$minutesStr:$secondsStr"
                } else {
                    // Format as MM:SS if hours = 0
                    "$minutesStr:$secondsStr"
                }
                if (isNotEmpty()) {
                    append("\n")
                }
                append(
                    getString(R.string.str_ai_trans_remainingDuration, timeStr)
                )
            }
        }
        return if (infoStr.isNotEmpty()) {
            infoStr
        } else {
            null
        }
    }

}