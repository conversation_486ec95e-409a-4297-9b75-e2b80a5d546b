package com.czur.starry.device.personalcenter.account.ai.recharge

import androidx.navigation.fragment.NavHostFragment
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.personalcenter.R
import com.czur.starry.device.personalcenter.databinding.ActivityAiTransCommonBinding

/**
 * Created by 陈丰尧 on 2025/4/22
 * AI字幕纪要充值功能
 */
class AITransRechargeActivity : CZViewBindingAty<ActivityAiTransCommonBinding>() {
    override fun ActivityAiTransCommonBinding.initBindingViews() {
        val fragment = navHost.getFragment<NavHostFragment>()
        val navId = R.navigation.nav_ai_trans_recharge
        fragment.navController.setGraph(navId)
    }
}