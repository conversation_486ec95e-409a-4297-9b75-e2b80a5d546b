package com.czur.starry.device.personalcenter.begin.login

import android.text.InputFilter
import android.view.View
import androidx.core.widget.doAfterTextChanged
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.network.core.common.ResCode
import com.czur.starry.device.baselib.utils.keyboard.keyboardShow
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.setOnActionDoneListener
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.baselib.view.dialog.LoadingDialog
import com.czur.starry.device.personalcenter.R
import com.czur.starry.device.personalcenter.begin.AbsBeginFragment
import com.czur.starry.device.personalcenter.begin.viewmodel.DeviceInfoViewModel
import com.czur.starry.device.personalcenter.begin.viewmodel.LoginForgetCaptchaViewModel
import com.czur.starry.device.personalcenter.databinding.LayoutPhoneCaptchaBinding
import com.czur.starry.device.personalcenter.net.AccountManager
import com.czur.starry.device.personalcenter.startup.getNetworkErrorDialog

/**
 * Created by 陈丰尧 on 2023/3/9
 * 验证码页面
 */
private const val TAG = "LoginForgetCaptchaFragment"

class LoginForgetCaptchaFragment : AbsBeginFragment<LayoutPhoneCaptchaBinding>() {

    private val modelLogin: LoginForgetCaptchaViewModel by viewModels()
    private val deviceInfoViewModel: DeviceInfoViewModel by viewModels({ requireActivity() })

    private val loadingDialog: LoadingDialog by lazy { LoadingDialog() }

    private var chinesePhoneFilter = InputFilter.LengthFilter(11)

    override fun LayoutPhoneCaptchaBinding.initBindingViews() {
        captchaNextBtn.text = getString(R.string.step_next)

        captchaTitleTv.setText(R.string.setup_find_account_title)
        captchaDescTv.setText(R.string.setup_find_account_desc)

        initListener()
    }

    private fun LayoutPhoneCaptchaBinding.initListener() {
        updateEditTextLengthFilter()

        getCaptchaTv.setOnClickListener {
            launch {
                modelLogin.getCaptcha()
            }
        }

        captchaPreBtn.setOnClickListener {
            logTagV(TAG, "上一步")
            findNavController().navigateUp()
        }

        captchaNextBtn.setOnClickListener {
            launch {
                loadingDialog.show()
                modelLogin.getDevice()
                loadingDialog.dismiss()
            }
        }

        // 手机号输入监听
        captchaPhoneEt.doAfterTextChanged {
            modelLogin.phoneNo.value = it?.toString() ?: ""
        }
        captchaPhoneEt.requestFocusFromTouch()
        captchaPhoneEt.keyboardShow(300L)
        // 验证码输入框
        captchaEt.doAfterTextChanged {
            modelLogin.captcha.value = it?.toString() ?: ""
        }

        captchaEt.setOnActionDoneListener {
            if (captchaNextBtn.isEnabled) {
                captchaNextBtn.performClick()
                true
            } else {
                false
            }
        }

        modelLogin.nextEnable.observe(viewLifecycleOwner) {
            captchaNextBtn.isEnabled = it
        }
        modelLogin.captchaEnable.observe(viewLifecycleOwner) {
            getCaptchaTv.isEnabled = it
        }

        AccountManager.captchaTime.observe(viewLifecycleOwner) {
            if (it < 0) {
                countDownTv.visibility = View.GONE
                getCaptchaTv.visibility = View.VISIBLE
            } else {
                countDownTv.visibility = View.VISIBLE
                getCaptchaTv.visibility = View.GONE
                countDownTv.text = "${it}s"
            }
        }

        modelLogin.errorCode.observe(viewLifecycleOwner) {
            // 网络错误
            when (it) {
                ResCode.RESULT_CODE_VERIFICATION_CODE_ERROR -> {
                    toast(R.string.toast_error_verification_code)
                    captchaEt.setText("")
                }
                ResCode.RESULT_CODE_SMS_REQUEST_TOO_MUCH -> {
                    toast(R.string.toast_error_verification_code)
                    captchaEt.setText("")
                }
                ResCode.RESULT_CODE_NO_INTERNET, ResCode.RESULT_CODE_NO_NET_CONNECT ->
                    getNetworkErrorDialog(requireActivity()) {
                        launch {
                            // 网络错误, 重试
                            loadingDialog.show()
                            modelLogin.retry()
                            loadingDialog.dismiss()
                        }
                    }.show()
            }
        }

        modelLogin.deviceInfo.observe(viewLifecycleOwner) {
            if (it.meetingAccount.isEmpty()) {
                when (Constants.starryHWInfo.salesLocale) {
                    StarryDevLocale.Mainland -> toast(R.string.toast_forget_no_device)
                    StarryDevLocale.Overseas -> toast(R.string.toast_forget_no_device_email)
                }
            } else {
                deviceInfoViewModel.deviceInfo = it
                deviceInfoViewModel.phoneNoOrEmail = modelLogin.phoneNo.value!!
                LoginForgetCaptchaFragmentDirections
                    .actionLoginForgetCaptchaFragmentToDeviceInfoFragment()
                    .nav()
            }
        }

    }

    /**
     * 更新手机号的长度校验逻辑
     * 是+86的 最大长度就是11位
     * 其他没有限制
     */
    private fun LayoutPhoneCaptchaBinding.updateEditTextLengthFilter() {
        val rawFilters = captchaPhoneEt.filters?.toMutableList() ?: mutableListOf()
        if (captchaAreaCodeTv.text.toString() == "+86") {
            rawFilters.add(chinesePhoneFilter)
        } else {
            rawFilters.remove(chinesePhoneFilter)
        }
        captchaPhoneEt.filters = rawFilters.toTypedArray()
    }
}