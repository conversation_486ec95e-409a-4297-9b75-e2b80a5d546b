package com.czur.starry.device.personalcenter.provider

import android.content.ContentValues
import android.content.Context
import android.content.UriMatcher
import android.net.Uri
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.preferencesDataStore
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.data.provider.UserHandler.PATH_UPDATE_USER_INFO
import com.czur.starry.device.baselib.handler.SPContentHandler
import com.czur.starry.device.baselib.handler.SPContentProvider
import com.czur.starry.device.baselib.handler.createDefCorruptionHandler
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.baselib.utils.prop.setBooleanSystemProp
import com.czur.starry.device.personalcenter.net.AccountManager
import kotlinx.coroutines.runBlocking

private const val TAG = "PersonalProvider"

class PersonalProvider : SPContentProvider() {
    override val Context.providerDataStore: DataStore<Preferences> by preferencesDataStore(
        name = "PersonalInfoProvider",
        corruptionHandler = createDefCorruptionHandler("PersonalInfoProvider")
    )
    override val targetHandler: SPContentHandler by lazy { UserHandler }

    companion object {
        private const val PROP_KEY_KICK_OFF = "system.personal.kickoff"
    }


    override fun onSubQuery(key: String, defValue: String): String? {
        if (key == UserHandler.SP_BEEN_KICK_OUT) {
            logTagV(TAG, "读取被踢掉信息")
            return getBooleanSystemProp(PROP_KEY_KICK_OFF, false).toString()
        }
        return super.onSubQuery(key, defValue)
    }

    override fun onSubUpdate(key: String, value: String): Boolean {
        if (key == UserHandler.SP_BEEN_KICK_OUT) {
            logTagV(TAG, "设置被踢掉信息")
            setBooleanSystemProp(PROP_KEY_KICK_OFF, value.toBoolean())
            return true
        }
        return super.onSubUpdate(key, value)
    }

    override fun onUpdate(
        uriMatcher: UriMatcher,
        uri: Uri,
        values: ContentValues?,
        selection: String?,
        selectionArgs: Array<String>?,
    ): Int {
        if (uriMatcher.match(uri) == PATH_UPDATE_USER_INFO.code) {
            // 更新用户信息
            runBlocking {
                doWithoutCatch(TAG, "更新联系人信息失败:") {
                    // 网络请求更新用户信息
                    val updateInfo = AccountManager.getPersonalInfo()
                    // 用户昵称
                    val keyNickName = getSaveKey(UserHandler.SP_NICKNAME)
                    // 头像
                    val headImage = getSaveKey(UserHandler.SP_HEAD_IMAGE)
                    // bucket
                    val bucket = getSaveKey(UserHandler.SP_BUCKET_NAME)
                    // endpoint
                    val endpoint = getSaveKey(UserHandler.SP_ENDPOINT)
                    // mobile
                    val mobile = getSaveKey(UserHandler.SP_MOBILE)

                    dataStore.edit {
                        it[keyNickName] = updateInfo.nickName
                        it[headImage] = updateInfo.headImage.orEmpty()
                        it[bucket] = updateInfo.bucketName
                        it[endpoint] = updateInfo.endpoint
                        it[mobile] = updateInfo.mobile
                    }

                    // notify
                    logTagV(TAG, "通知用户信息数据更新")
                    notifyUpdate(
                        UserHandler.SP_NICKNAME to updateInfo.nickName,
                        UserHandler.SP_HEAD_IMAGE to (updateInfo.headImage.orEmpty()),
                        UserHandler.SP_BUCKET_NAME to updateInfo.bucketName,
                        UserHandler.SP_ENDPOINT to updateInfo.endpoint,
                        UserHandler.SP_MOBILE to updateInfo.mobile,
                    )
                }
            }

            return values?.size() ?: 0
        }
        return super.onUpdate(uriMatcher, uri, values, selection, selectionArgs)
    }
}