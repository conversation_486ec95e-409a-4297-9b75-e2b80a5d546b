<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="120px"
    android:paddingRight="45px"
    tools:ignore="PxUsage">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideLine"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintGuide_begin="95px"
        android:orientation="vertical" />

    <com.czur.uilib.choose.CZImageCheckBox
        android:id="@+id/itemCheckBox"
        android:layout_width="50px"
        android:layout_height="50px"
        app:checkedImg="@drawable/ic_order_checked"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toLeftOf="@id/guideLine"
        app:layout_constraintTop_toTopOf="parent"
        app:unCheckedImg="@drawable/ic_order_unchecked" />

    <TextView
        android:id="@+id/orderNameTv"
        android:layout_width="340px"
        android:layout_height="wrap_content"
        android:layout_marginLeft="40px"
        android:ellipsize="end"
        android:lines="1"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/itemCheckBox"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="购买会员服务" />

    <TextView
        android:id="@+id/orderNumberTv"
        android:layout_width="280px"
        android:layout_height="wrap_content"
        android:layout_marginLeft="40px"
        android:ellipsize="end"
        android:lines="1"
        android:textColor="@color/white"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintBaseline_toBaselineOf="@id/orderNameTv"
        app:layout_constraintLeft_toRightOf="@id/orderNameTv"
        tools:text="SD3489020200802" />

    <TextView
        android:id="@+id/orderTimeTv"
        android:layout_width="140px"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30px"
        android:ellipsize="end"
        android:lines="1"
        android:textColor="@color/white"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintBaseline_toBaselineOf="@id/orderNumberTv"
        app:layout_constraintLeft_toRightOf="@id/orderNumberTv"
        tools:text="2023.10.01" />

    <TextView
        android:id="@+id/itemPriceTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="5px"
        android:textColor="@color/white"
        android:textSize="36px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toLeftOf="@id/unitTv"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="2000" />

    <TextView
        android:id="@+id/unitTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_order_list_item_rmb"
        android:textColor="@color/white"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintBaseline_toBaselineOf="@id/itemPriceTv"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/receiveTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_received"
        android:textColor="@color/white"
        android:textSize="24px"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintRight_toLeftOf="@id/guideLine"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>