<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="PxUsage">

    <com.czur.uilib.CZTitleBar
        android:id="@+id/titleBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:baselib_titlebar_title="@string/back"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/titleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="175px"
        android:text="@string/title_ai_trans_recharge"
        android:textColor="@color/white"
        android:textSize="48px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/titleLineView"
        android:layout_width="800px"
        android:layout_height="1px"
        android:layout_marginTop="290px"
        android:alpha="0.3"
        android:background="@color/white"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/subTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/subtitle_ai_trans_qr"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="normal"
        app:layout_constraintBottom_toTopOf="@id/qrCodeIv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleLineView"
        app:layout_constraintVertical_chainStyle="packed" />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/qrCodeIv"
        android:layout_width="256px"
        android:layout_height="256px"
        android:layout_marginTop="30px"
        android:background="@color/white"
        android:padding="8px"
        app:layout_constraintBottom_toTopOf="@id/qrInfoTv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/subTitleTv"
        app:round="8px" />

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="0px"
        android:layout_height="0px"
        android:indeterminateTint="@color/white"
        android:indeterminateTintMode="src_atop"
        android:padding="80px"
        app:bl_corners_radius="10px"
        app:bl_solid_color="#4D000000"
        app:layout_constraintBottom_toBottomOf="@id/qrCodeIv"
        app:layout_constraintLeft_toLeftOf="@id/qrCodeIv"
        app:layout_constraintRight_toRightOf="@id/qrCodeIv"
        app:layout_constraintTop_toTopOf="@id/qrCodeIv" />

    <View
        android:id="@+id/loadingErrorBgView"
        android:layout_width="0px"
        android:layout_height="0px"
        app:bl_corners_radius="10px"
        app:bl_solid_color="#4D000000"
        app:layout_constraintBottom_toBottomOf="@id/qrCodeIv"
        app:layout_constraintLeft_toLeftOf="@id/qrCodeIv"
        app:layout_constraintRight_toRightOf="@id/qrCodeIv"
        app:layout_constraintTop_toTopOf="@id/qrCodeIv"
        tools:background="#4D000000" />

    <ImageView
        android:id="@+id/refreshIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/img_refresh"
        app:layout_constraintBottom_toTopOf="@id/loadingErrorTv"
        app:layout_constraintLeft_toLeftOf="@id/loadingErrorBgView"
        app:layout_constraintRight_toRightOf="@id/loadingErrorBgView"
        app:layout_constraintTop_toTopOf="@id/loadingErrorBgView"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/loadingErrorTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20px"
        android:text="@string/str_click_refresh"
        android:textColor="@color/white"
        android:textSize="24px"
        app:layout_constraintBottom_toBottomOf="@id/loadingErrorBgView"
        app:layout_constraintLeft_toLeftOf="@id/loadingErrorBgView"
        app:layout_constraintRight_toRightOf="@id/loadingErrorBgView"
        app:layout_constraintTop_toBottomOf="@id/refreshIv" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/loadingErrorGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="loadingErrorBgView,refreshIv,loadingErrorTv" />


    <TextView
        android:id="@+id/qrInfoTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30px"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        android:layout_marginBottom="50px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/qrCodeIv"
        tools:text="@string/str_ai_trans_qr_info" />


</androidx.constraintlayout.widget.ConstraintLayout>