<?xml version="1.0" encoding="utf-8"?>

<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="PxUsage">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:ignore="PxUsage">

        <TextView
            android:id="@+id/titleTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="175px"
            android:text="@string/title_ai_trans_recharge"
            android:textColor="@color/white"
            android:textSize="48px"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/titleLineView"
            android:layout_width="800px"
            android:layout_height="1px"
            android:layout_marginTop="290px"
            android:alpha="0.3"
            android:background="@color/white"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/titleMonthlyCardTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/title_ai_trans_m_card"
            android:textColor="@color/white"
            android:textSize="30px"
            android:textStyle="normal"
            app:layout_constraintBottom_toTopOf="@id/monthlyCardRv"
            app:layout_constraintLeft_toLeftOf="@id/monthlyCardRv"
            app:layout_constraintRight_toRightOf="@id/monthlyCardRv"
            app:layout_constraintTop_toBottomOf="@id/titleLineView"
            app:layout_constraintVertical_chainStyle="packed" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/monthlyCardRv"
            android:layout_width="630px"
            android:layout_height="400px"
            android:layout_marginTop="40px"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toTopOf="@id/rechargeBtn"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/timeBasedRv"
            app:layout_constraintTop_toBottomOf="@id/titleMonthlyCardTv"
            tools:itemCount="4"
            tools:listitem="@layout/item_ai_trans_recharge" />

        <TextView
            android:id="@+id/titleTimeBasedTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/title_ai_trans_time_based"
            android:textColor="@color/white"
            android:textSize="30px"
            android:textStyle="normal"
            app:layout_constraintLeft_toLeftOf="@id/timeBasedRv"
            app:layout_constraintRight_toRightOf="@id/timeBasedRv"
            app:layout_constraintTop_toTopOf="@id/titleMonthlyCardTv" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/timeBasedRv"
            android:layout_width="630px"
            android:layout_height="400px"
            android:layout_marginLeft="70px"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintLeft_toRightOf="@id/monthlyCardRv"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/monthlyCardRv"
            tools:itemCount="4"
            tools:listitem="@layout/item_ai_trans_recharge" />

        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="80px"
            android:layout_height="80px"
            android:indeterminateTint="@color/white"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/monthlyCardRv"
            app:layout_constraintLeft_toLeftOf="@id/monthlyCardRv"
            app:layout_constraintRight_toRightOf="@id/timeBasedRv"
            app:layout_constraintTop_toTopOf="@id/monthlyCardRv" />

        <com.czur.uilib.btn.CZButton
            android:id="@+id/rechargeBtn"
            android:layout_width="300px"
            android:layout_height="80px"
            android:layout_marginBottom="60px"
            android:text="@string/str_recharge"
            android:textSize="30px"
            app:colorStyle="PositiveInBlue"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/noNetworkGroup"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/bg_main_blue"
        android:visibility="gone"
        tools:ignore="PxUsage">

        <ImageView
            android:id="@+id/errorIv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/img_no_network1"
            android:tintMode="src_atop"
            app:layout_constraintBottom_toTopOf="@id/errorTv"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            app:tint="@color/white" />


        <TextView
            android:id="@+id/errorTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="35px"
            android:text="@string/str_no_network"
            android:textColor="@color/white"
            android:textSize="30px"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/errorIv" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.czur.uilib.CZTitleBar
        android:id="@+id/titleBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:baselib_titlebar_title="@string/app_name"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</FrameLayout>