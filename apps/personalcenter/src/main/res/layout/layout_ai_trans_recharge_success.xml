<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="PxUsage">

    <ImageView
        android:id="@+id/logoIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="255px"
        android:src="@drawable/img_recharge_success"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/titleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="50px"
        android:text="@string/title_ai_trans_recharge_success"
        android:textColor="@color/white"
        android:textSize="48px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/logoIv" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toTopOf="@id/backBtn"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleTv">

        <TextView
            android:id="@+id/labelPriceTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:alpha="0.5"
            android:text="@string/label_ai_trans_recharge_success_price"
            android:textColor="@color/white"
            android:textSize="30px"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@id/labelNameTv"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@+id/labelNameTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15px"
            android:alpha="0.5"
            android:text="@string/label_ai_trans_recharge_success_name"
            android:textColor="@color/white"
            android:textSize="30px"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@id/labelTimeTv"
            app:layout_constraintLeft_toLeftOf="@id/labelPriceTv"
            app:layout_constraintTop_toBottomOf="@id/labelPriceTv" />

        <TextView
            android:id="@+id/labelTimeTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15px"
            android:alpha="0.5"
            android:text="@string/label_ai_trans_recharge_success_time"
            android:textColor="@color/white"
            android:textSize="30px"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="@id/labelNumberTv"
            app:layout_constraintLeft_toLeftOf="@id/labelPriceTv"
            app:layout_constraintTop_toBottomOf="@id/labelNameTv" />

        <TextView
            android:id="@+id/labelNumberTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15px"
            android:alpha="0.5"
            android:text="@string/label_ai_trans_recharge_success_number"
            android:textColor="@color/white"
            android:textSize="30px"
            android:textStyle="bold"
            app:layout_constraintBottom_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="@id/labelPriceTv"
            app:layout_constraintTop_toBottomOf="@id/labelTimeTv" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/labelBarrier"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="right"
            app:constraint_referenced_ids="labelPriceTv,labelNameTv,labelTimeTv,labelNumberTv" />

        <TextView
            android:id="@+id/priceTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15px"
            android:textColor="@color/white"
            android:textSize="30px"
            android:textStyle="bold"
            app:layout_constraintBaseline_toBaselineOf="@id/labelPriceTv"
            app:layout_constraintLeft_toRightOf="@id/labelBarrier"
            tools:text="450.00元" />

        <TextView
            android:id="@+id/nameTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15px"
            android:textColor="@color/white"
            android:textSize="30px"
            android:textStyle="bold"
            app:layout_constraintBaseline_toBaselineOf="@id/labelNameTv"
            app:layout_constraintLeft_toRightOf="@id/labelBarrier"
            tools:text="AI字幕纪要会员" />

        <TextView
            android:id="@+id/timeTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15px"
            android:textColor="@color/white"
            android:textSize="30px"
            android:textStyle="bold"
            app:layout_constraintBaseline_toBaselineOf="@id/labelTimeTv"
            app:layout_constraintLeft_toRightOf="@id/labelBarrier"
            tools:text="2023-10-01 12:00:01" />

        <TextView
            android:id="@+id/numberTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15px"
            android:textColor="@color/white"
            android:textSize="30px"
            android:textStyle="bold"
            app:layout_constraintBaseline_toBaselineOf="@id/labelNumberTv"
            app:layout_constraintLeft_toRightOf="@id/labelBarrier"
            tools:text="20231001120001" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.czur.uilib.btn.CZButton
        android:id="@+id/backBtn"
        android:layout_width="300px"
        android:layout_height="80px"
        android:layout_marginBottom="60px"
        android:text="@string/str_ai_trans_recharge_success_back"
        android:textSize="30px"
        app:colorStyle="PositiveInBlue"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>