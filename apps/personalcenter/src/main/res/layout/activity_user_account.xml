<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".account.UserAccountActivity"
    tools:ignore="PxUsage">

    <com.czur.uilib.CZTitleBar
        android:id="@+id/titleBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:baselib_titlebar_title="@string/app_name"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 整体居中的 View -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/centeredView"
        android:layout_width="1000px"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 头像 -->
        <androidx.constraintlayout.utils.widget.ImageFilterView
            android:id="@+id/avatar"
            android:layout_width="100px"
            android:layout_height="100px"
            android:layout_marginStart="30px"
            android:scaleType="centerCrop"
            android:src="@drawable/img_user_avatar"
            app:layout_constraintBottom_toBottomOf="@+id/inputField"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:roundPercent="1" />

        <androidx.constraintlayout.utils.widget.ImageFilterView
            android:id="@+id/fieldBG"
            android:layout_width="380px"
            android:layout_height="70px"
            android:layout_marginStart="30px"
            android:background="@color/edit_deep_blue"
            app:layout_constraintBottom_toBottomOf="@id/avatar"
            app:layout_constraintStart_toEndOf="@id/avatar"
            app:layout_constraintTop_toTopOf="parent"
            app:round="10px" />

        <EditText
            android:id="@+id/inputField"
            android:layout_width="360px"
            android:layout_height="wrap_content"
            android:layout_marginStart="40px"
            android:background="@null"
            android:ellipsize="end"
            android:scrollHorizontally="true"
            android:singleLine="true"
            android:textColor="@color/white"
            app:layout_constraintBottom_toBottomOf="@id/avatar"
            app:layout_constraintStart_toEndOf="@id/avatar"
            app:layout_constraintTop_toTopOf="parent" />

        <com.czur.uilib.btn.CZButton
            android:id="@+id/saveBtn"
            android:layout_width="150px"
            android:layout_height="60px"
            android:text="@string/str_save"
            android:layout_marginRight="30px"
            android:textSize="20px"
            android:visibility="invisible"
            app:colorStyle="PositiveInBlue"
            app:layout_constraintBottom_toBottomOf="@id/inputField"
            app:layout_constraintEnd_toEndOf="@id/centeredView"
            app:layout_constraintTop_toTopOf="@id/inputField" />

        <com.czur.uilib.btn.CZButton
            android:id="@+id/cancelBtn"
            android:layout_width="150px"
            android:layout_height="60px"
            android:layout_marginEnd="20px"
            android:text="@string/str_cancel"
            android:textSize="20px"
            android:visibility="invisible"
            app:colorStyle="NegativeInBlue"
            app:layout_constraintBottom_toBottomOf="@id/inputField"
            app:layout_constraintEnd_toStartOf="@id/saveBtn"
            app:layout_constraintTop_toTopOf="@id/inputField" />

        <!-- 分割线 -->
        <View
            android:id="@+id/line"
            android:layout_width="match_parent"
            android:layout_height="2px"
            android:layout_marginTop="25px"
            android:background="@color/line_color"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/avatar" />

        <!-- 账号 -->
        <TextView
            android:id="@+id/accountTitle"
            style="@style/title_text"
            android:layout_marginTop="35px"
            android:text="@string/login_account"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/line" />

        <TextView
            android:id="@+id/accountValue"
            style="@style/content_text"
            app:layout_constraintStart_toEndOf="@id/accountTitle"
            app:layout_constraintTop_toTopOf="@id/accountTitle"
            tools:text="000000" />

        <com.czur.uilib.btn.CZButton
            android:id="@+id/logoutButton"
            style="@style/action_button"
            android:text="@string/str_cancellation"
            android:layout_marginRight="30px"
            app:colorStyle="PositiveInBlue"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/accountValue" />

        <!-- 密保手机 -->
        <TextView
            android:id="@+id/securityPhoneTitle"
            style="@style/title_text"
            android:layout_marginTop="70px"
            android:text="@string/str_secured_mobile_phone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/accountTitle" />

        <TextView
            android:id="@+id/securityPhoneValue"
            style="@style/content_text"
            app:layout_constraintStart_toEndOf="@id/securityPhoneTitle"
            app:layout_constraintTop_toTopOf="@id/securityPhoneTitle"
            tools:text="***********" />

        <com.czur.uilib.btn.CZButton
            android:id="@+id/editPhoneButton"
            style="@style/action_button"
            android:text="@string/str_modify"
            app:colorStyle="PositiveInBlue"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginRight="30px"
            app:layout_constraintTop_toTopOf="@id/securityPhoneValue" />

        <!-- AI字幕 -->
        <TextView
            android:id="@+id/aiSubtitleTitle"
            style="@style/title_text"
            android:layout_marginTop="70px"
            android:text="@string/str_AI_translation_subtitle_member"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/securityPhoneTitle" />

        <TextView
            android:id="@+id/aiSubtitleValidity"
            style="@style/content_text"
            android:layout_marginStart="30px"
            android:layout_marginTop="10px"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/aiSubtitleTitle"
            tools:text="有效期至: 8888-88-88" />


        <com.czur.uilib.btn.CZButton
            android:id="@+id/aiSubtitleActions"
            style="@style/action_button"
            android:text="@string/str_recharge"
            android:layout_marginRight="30px"
            app:colorStyle="PositiveInBlue"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/aiSubtitleTitle"
            app:layout_constraintBottom_toBottomOf="@id/aiSubtitleTitle"/>

        <com.czur.uilib.btn.CZButton
            android:id="@+id/recordsAndInvoices"
            style="@style/action_button"
            android:layout_width="200px"
            android:layout_marginEnd="20px"
            android:text="@string/str_records_and_invoices"
            app:colorStyle="NegativeInBlue"
            app:layout_constraintEnd_toStartOf="@+id/aiSubtitleActions"
            app:layout_constraintTop_toTopOf="@id/aiSubtitleTitle"
            app:layout_constraintBottom_toBottomOf="@id/aiSubtitleTitle"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 退出登录 -->
    <androidx.constraintlayout.helper.widget.Layer
        android:id="@+id/logoutClickLayer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:constraint_referenced_ids="logoutIv,logoutTv" />

    <ImageView
        android:id="@+id/logoutIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="100px"
        android:layout_marginBottom="80px"
        android:src="@drawable/skip_arrow_right"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/logoutTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="10px"
        android:text="@string/str_log_out"
        android:textColor="@color/white"
        android:textSize="36px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/logoutIv"
        app:layout_constraintRight_toLeftOf="@id/logoutIv"
        app:layout_constraintTop_toTopOf="@id/logoutIv" />
</androidx.constraintlayout.widget.ConstraintLayout>