<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="500px"
    android:layout_height="360px"
    app:bl_corners_radius="10px"
    app:bl_solid_color="#5879FC"
    tools:background="#5879FC">

    <TextView
        android:id="@+id/singleBtnFloatTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="25px"
        android:includeFontPadding="false"
        android:text="@string/dialog_normal_title_tips"
        android:textColor="@color/white"
        android:textSize="36px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/singleBtnFloatContentTv"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginLeft="40px"
        android:layout_marginRight="40px"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="24px"
        android:textStyle="bold"
        android:text="@string/str_hw_paired_not_active2"
        android:lineSpacingExtra="8px"
        app:layout_constraintBottom_toTopOf="@id/singleBtnFloatConfirmBtn"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/singleBtnFloatTitleTv"/>

    <com.czur.starry.device.baselib.widget.CommonButton
        android:id="@+id/singleBtnFloatConfirmBtn"
        android:layout_width="180px"
        android:layout_height="50px"
        android:layout_marginBottom="30px"
        android:text="@string/dialog_normal_confirm"
        android:textSize="20px"
        android:textStyle="bold"
        app:baselib_theme="white2"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>