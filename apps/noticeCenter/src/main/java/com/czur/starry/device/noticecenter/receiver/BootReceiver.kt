package com.czur.starry.device.noticecenter.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.handler.CZPropHandler
import com.czur.starry.device.noticecenter.WatchService
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/**
 * Created by 陈丰尧 on 2024/5/23
 */
private const val TAG = "BootReceiver"

class BootReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent?) {
        logTagD(TAG, "NoticeCenter BootReceiver onReceive")
        MainScope().launch {
            try {
                CZPropHandler.clearScope(CZPropHandler.PropScope.TEMP)
            } catch (e: Throwable) {
                logTagE(TAG, "clearScope error", tr = e)
            }
        }

    }
}