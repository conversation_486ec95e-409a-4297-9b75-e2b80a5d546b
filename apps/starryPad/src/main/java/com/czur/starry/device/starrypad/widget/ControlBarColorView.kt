package com.czur.starry.device.starrypad.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View

/**
 * Created by 陈丰尧 on 2025/3/17
 */
private const val COLOR_SEL = 0xFFFAFAFA.toInt()
private const val COLOR_UN_SEL = 0xFF9696AC.toInt()

class ControlBarColorView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : View(context, attrs, defStyleAttr) {
    var showColor: Int = Color.BLACK
        set(value) {
            field = value
            invalidate()
        }

    private val paint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.FILL
        color = showColor
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        paint.color = if (isSelected) {
            COLOR_SEL
        } else {
            COLOR_UN_SEL
        }
        // 绘制

        val w = width - paddingLeft - paddingRight
        val h = height - paddingTop - paddingBottom
        val edge = w.coerceAtMost(h)

        // 中心点坐标
        val centerX = paddingLeft + w / 2F
        val centerY = paddingTop + h / 2F

        // 在中心绘制圆角矩形
        canvas.drawRoundRect(
            centerX - edge / 2F,
            centerY - edge / 2F,
            centerX + edge / 2F,
            centerY + edge / 2F,
            3F,
            3F,
            paint
        )
        // 绘制中心的颜色
        paint.color = showColor
        val colorEdge = edge - 4
        canvas.drawRoundRect(
            centerX - colorEdge / 2F,
            centerY - colorEdge / 2F,
            centerX + colorEdge / 2F,
            centerY + colorEdge / 2F,
            2F,
            2F,
            paint
        )
    }

    override fun setSelected(selected: Boolean) {
        super.setSelected(selected)
        invalidate()
    }
}