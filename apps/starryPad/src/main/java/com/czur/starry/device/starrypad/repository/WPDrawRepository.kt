package com.czur.starry.device.starrypad.repository

import com.czur.starry.writepadlib.proto.WPTransferData
import com.czur.starry.writepadlib.proto.WPTransferData.BizSyncUserInput
import com.czur.starry.writepadlib.widget.drawing.palette.core.IPaintStepCreator
import com.czur.starry.writepadlib.widget.drawing.palette.core.createPaintStepCreator
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow

/**
 * Created by 陈丰尧 on 2024/1/8
 * 负责
 * 1. 用户数据的收集
 * 2. 绘制步骤的生成
 */
class WPDrawRepository(
    private val scope: CoroutineScope
) {
    private val stepCreators = mutableMapOf<String, IPaintStepCreator>()
    private val _drawStepFlow = MutableSharedFlow<WPTransferData.PaintStepContentDraw>(
        extraBufferCapacity = 128,
        onBufferOverflow = BufferOverflow.DROP_OLDEST
    )

    // 绘制步骤流
    val drawStepFlow = _drawStepFlow.asSharedFlow()

    /**
     * 添加用户输入点
     */
    fun onUserInputReceive(devID: String, userInput: BizSyncUserInput) {
        val stepCreator = getStepCreator(devID)
        stepCreator.addUserInputPoint(userInput)
    }

    /**
     * 获取绘制步骤生成器
     */
    private fun getStepCreator(devID: String): IPaintStepCreator {
        return stepCreators.getOrPut(devID) {
            createPaintStepCreator(devID, scope).apply {
                pointChangeListener = {
                    _drawStepFlow.tryEmit(it)
                }
            }
        }
    }
}