package com.czur.starry.device.starrypad.net

import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagW
import com.alibaba.sdk.android.oss.ClientException
import com.alibaba.sdk.android.oss.OSS
import com.alibaba.sdk.android.oss.ServiceException
import com.alibaba.sdk.android.oss.callback.OSSCompletedCallback
import com.alibaba.sdk.android.oss.internal.OSSAsyncTask
import com.alibaba.sdk.android.oss.model.CompleteMultipartUploadResult
import com.alibaba.sdk.android.oss.model.MultipartUploadRequest
import com.czur.starry.device.baselib.utils.randomStr

/**
 * Created by 陈丰尧 on 2022/5/10
 */

enum class OssTaskState {
    RUNNING,    // 任务正在运行中
    SUCCESS,    // 任务成功
    FAIL,       // 任务失败
}

class OSSRequestManager(
    private val fileOss: OSS
) {
    companion object {
        private const val TAG = "OSSRequestManager"
    }

    private val taskMap = mutableMapOf<String, OSSAsyncTask<*>>()
    private val sizeListenerMap = mutableMapOf<String, TransferSizeListener>()
    private val taskStateMap = mutableMapOf<String, OssTaskState>()
    private val errorMap = mutableMapOf<String, Exception>()

    fun getException(taskId: String): Exception? = errorMap[taskId]

    /**
     * 过去结束Size
     */
    fun getFinishSize(taskId: String): Long = sizeListenerMap[taskId]?.transferSize ?: 0L

    fun getTotalSize(taskId: String): Long = sizeListenerMap[taskId]?.totalSize ?: 0L

    fun clearTask(taskId: String) {
        taskMap.remove(taskId)?.let {
            if (!it.isCanceled && !it.isCompleted) {
                // 如果任务没有完成, 就取消
                it.cancel()
            }
        }
        sizeListenerMap.remove(taskId)
        taskStateMap.remove(taskId)
        errorMap.remove(taskId)

    }

    /**
     * 获取当前Task的状态
     */
    fun getTaskStatus(taskId: String): OssTaskState? = taskStateMap[taskId]

    fun addUploadRequest(
        request: MultipartUploadRequest<*>, targetSize: Long = 0L
    ): String {
        // 生成ID
        val id = generateTaskID()

        // 进度回调
        val sizeListener = TransferSizeListener()
        sizeListener.updateSize(request.objectKey, 0L, targetSize)
        sizeListenerMap[id] = sizeListener
        request.setProgressCallback { request, currentSize, totalSize ->
            sizeListener.updateSize(request.objectKey, currentSize, totalSize)
        }

        taskStateMap[id] = OssTaskState.RUNNING


        val task = fileOss.asyncMultipartUpload(
            request,
            object :
                OSSCompletedCallback<MultipartUploadRequest<*>, CompleteMultipartUploadResult> {
                override fun onSuccess(
                    request: MultipartUploadRequest<*>?,
                    result: CompleteMultipartUploadResult?
                ) {
                    taskStateMap[id] = OssTaskState.SUCCESS
                }

                override fun onFailure(
                    request: MultipartUploadRequest<*>?,
                    clientException: ClientException?,
                    serviceException: ServiceException?
                ) {
                    clientException?.let {
                        logTagE(TAG, "上传失败,客户端错误", tr = it)
                        errorMap[id] = it
                    }
                    serviceException?.let {
                        logTagE(TAG, "上传失败, 服务端错误", tr = it)
                        errorMap[id] = it
                    }
                    if (clientException == null && serviceException == null) {
                        logTagW(TAG, "上传失败, OSS没有抛出异常")
                    }
                    taskStateMap[id] = OssTaskState.FAIL
                }

            })

        taskMap[id] = task

        return id

    }

    /**
     * 生成任务ID
     */
    private fun generateTaskID(): String = randomStr()

}