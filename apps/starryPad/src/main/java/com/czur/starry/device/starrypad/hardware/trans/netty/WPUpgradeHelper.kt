package com.czur.starry.device.starrypad.hardware.trans.netty

import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.ONE_KB
import com.czur.starry.device.starrypad.hardware.ServerProtoBuilder
import com.czur.starry.device.starrypad.hardware.WritePadDeviceModeManager
import com.czur.starry.writepadlib.proto.WPTransferData.UpgradeData
import com.czur.starry.writepadlib.proto.WPTransferData.UpgradeInfo
import com.google.protobuf.ByteString
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.isActive
import kotlinx.coroutines.withContext
import java.io.File

/**
 * Created by 陈丰尧 on 2023/12/14
 */
private const val TAG = "WPUpgradeHelper"
private const val SIZE_SLICE = 100 * ONE_KB.toInt() // 文件的每一片都是100KB

class WPUpgradeHelper {
    suspend fun transUpgradeFile(
        devID: String,
        filePath: String,
        percentChange: (Int) -> Unit
    ): Boolean =
        withContext(Dispatchers.IO) {
            logTagD(TAG, "开始传输升级文件: $filePath")
            val upgradeFile = File(filePath)
            var index = 0
            val startTime = System.currentTimeMillis()
            val maxIndex = upgradeFile.length() / SIZE_SLICE
            logTagV(TAG, "maxIndex: $maxIndex")
            percentChange(0)

            var transComplete = false
            upgradeFile.inputStream().use {
                val buffer = ByteArray(SIZE_SLICE)
                var readSize: Int
                while (isActive) {
                    readSize = it.read(buffer)
                    if (readSize == -1) {
                        logTagD(TAG, "传输完成")
                        transComplete = true
                        break
                    }
                    val sendData = ServerProtoBuilder.withUpgradeInfo {
                        upgradeData = UpgradeData.newBuilder()
                            .setDataIndex(index)
                            .setDataBody(ByteString.copyFrom(buffer, 0, readSize))
                            .build()
                    }
                    logTagD(TAG, "传输升级文件: $index / $maxIndex")
                    try {

                        WritePadDeviceModeManager.sendMsgSync(sendData, devID)
                    } catch (e: Throwable) {
                        logTagW(TAG, "传输升级文件失败", tr = e)
                        break
                    }
                    percentChange((index * 100 / maxIndex).toInt())
                    index++
                }
            }

            val endTime = System.currentTimeMillis()
            logTagD(TAG, "传输升级文件结束, 耗时: ${endTime - startTime}ms")
            // 发送结束标记
            if (!transComplete) {
                logTagW(TAG, "传输升级文件失败, 发送中断消息")
                WritePadDeviceModeManager.sendMsg(
                    ServerProtoBuilder.withUpgradeInfo {
                        upgradeTransMsg = UpgradeInfo.UpgradeTransMsg.SVR_ERROR
                    }, devID
                )
            } else {
                WritePadDeviceModeManager.sendMsgSync(
                    ServerProtoBuilder.withUpgradeInfo {
                        upgradeTransMsg = UpgradeInfo.UpgradeTransMsg.SVR_END
                    }, devID
                )
            }
            transComplete
        }
}