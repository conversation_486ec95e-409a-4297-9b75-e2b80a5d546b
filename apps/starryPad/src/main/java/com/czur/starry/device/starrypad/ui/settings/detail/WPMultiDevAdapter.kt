package com.czur.starry.device.starrypad.ui.settings.detail

import android.view.ViewGroup
import com.czur.starry.device.baselib.base.BaseDifferAdapter
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.starrypad.R
import com.czur.starry.device.starrypad.devinfo.WPDevInfo

/**
 * Created by 陈丰尧 on 2024/10/15
 */
class WPMultiDevAdapter : BaseDifferAdapter<WPDevInfo>() {
    override fun bindViewHolder(holder: BaseVH, position: Int, itemData: WPDevInfo) {
        // 是否连接
        holder.visible(itemData.connected, R.id.wpConnectGroup)
        holder.visible(!itemData.connected, R.id.wpItemDisConnectedBg)
        // 设备名称
        holder.setText(itemData.name, R.id.wpDevNameTv)
        // 固件版本
        holder.setText(itemData.version, R.id.wpDevVersionNameTv)
        holder.visible(itemData.version.isNotEmpty() && itemData.connected, R.id.wpDevVersionNameTv)
        // 是否可以OTA
        holder.visible(itemData.canUpgrade, R.id.wpDevUpgradeIv)
    }

    override fun areItemsTheSame(oldItem: WPDevInfo, newItem: WPDevInfo): Boolean {
        return oldItem.name == newItem.name
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
        return BaseVH(R.layout.item_wp_multi, parent)
    }
}