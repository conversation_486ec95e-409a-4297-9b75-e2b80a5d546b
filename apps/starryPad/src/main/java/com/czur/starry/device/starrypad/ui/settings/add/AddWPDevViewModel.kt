package com.czur.starry.device.starrypad.ui.settings.add

import android.annotation.SuppressLint
import android.app.Application
import android.bluetooth.BluetoothDevice
import androidx.lifecycle.AndroidViewModel
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.bluetoothlib.bluetooth.BluetoothCallback
import com.czur.starry.device.bluetoothlib.bluetooth.CachedBluetoothDevice
import com.czur.starry.device.bluetoothlib.bluetooth.LocalBluetoothManager
import com.czur.starry.device.starrypad.common.WP_NAME_PREFIX
import com.czur.starry.device.starrypad.devinfo.WPDevInfo
import com.czur.starry.device.starrypad.devinfo.WPDeviceInfoManager
import com.czur.starry.device.starrypad.hardware.WritePadDeviceModeManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlin.time.Duration.Companion.seconds

/**
 * Created by 陈丰尧 on 2024/2/26
 */
private const val TAG = "AddWPDevViewModel"

class AddWPDevViewModel(application: Application) : AndroidViewModel(application) {
    // 是否正在扫描
    private val _isScanningFlow = MutableStateFlow(true)
    val isScanningFlow = _isScanningFlow.asStateFlow()
    val isScanning get() = _isScanningFlow.value

    private val dockBluetoothCallback = DockBluetoothCallback()

    private val localManager: LocalBluetoothManager =
        LocalBluetoothManager.getInstance(application, null)
            .also { it!!.eventManager.registerCallback(dockBluetoothCallback) }!!
    private val localAdapter by lazy { localManager.bluetoothAdapter }

    private val allDevicesFlow = MutableStateFlow(CachedDevicesWrapper(emptyList()))

    // 用来存储设备名和地址的缓存,有可能开始有名称,后来又刷没了
    private val wpNameCache = mutableMapOf<String, String>()

    // 扫描到的设备
    private val selAddressFlow = MutableStateFlow("")
    private val availableDevicesFlow = allDevicesFlow.map {
        it.cachedDevices.filter { device ->
            val name = device.name
            if (name.startsWith(WP_NAME_PREFIX, true)) {
                wpNameCache[device.address] = name
            } else {
                return@filter false
            }
            device.bondState != BluetoothDevice.BOND_BONDED && device.bondState != BluetoothDevice.BOND_BONDING
                    && !WPDeviceInfoManager.isNewConnDev(name) && !WPDeviceInfoManager.isLegalDeviceFromCache(
                name
            )
        }
    }.flowOn(Dispatchers.IO)
    val foundWPDevFlow = combine(availableDevicesFlow, selAddressFlow) { devices, selAddress ->
        if (devices.isEmpty()) {
            return@combine emptyList<FoundWPDevice>()
        }
        if (selAddress.isEmpty()) {
            selAddressFlow.value = devices.firstOrNull()?.address ?: ""
            return@combine emptyList<FoundWPDevice>()
        } else if (devices.firstOrNull { it.device.address == selAddress } == null) {
            selAddressFlow.value = ""   // 选中的设备不在列表中,清空选中
            return@combine emptyList<FoundWPDevice>()
        }
        devices.map {
            var name = it.name
            if (!name.startsWith(WP_NAME_PREFIX, true)) {
                name = wpNameCache[it.address] ?: name  // 从缓存中取名字
            }
            FoundWPDevice(
                it.address,
                name,
                it.address == selAddress
            )
        }.filter { it.name.startsWith(WP_NAME_PREFIX, true) }.sortedBy { it.name }
    }.distinctUntilChanged()
        .flowOn(Dispatchers.IO)

    private val _viewStateFlow = MutableSharedFlow<AddWpViewState>()
    val viewStateFlow = _viewStateFlow.asSharedFlow()

    var connectingDevice: CachedBluetoothDevice? = null
        private set

    private var connectDownCount: Job? = null

    private var scanTimeOutJob: Job? = null

    init {
        launch {
            WritePadDeviceModeManager.clientInfoFlow.collect {
                if (it.name == connectingDevice?.name) {
                    // 连接成功
                    connectDownCount?.cancel()
                }
            }
        }
    }

    @SuppressLint("MissingPermission")
    suspend fun deleteLastConnectingDev() {
        val device = localAdapter.bondedDevices.firstOrNull {
            it.address == selAddressFlow.value
        }
        if (device == null) {
            logTagW(TAG, "没有配对的设备")
            localManager.cachedDeviceManager.clearNonBondedDevices()
            return
        }
        WPDeviceInfoManager.delDev(WPDevInfo(device.name))
        localManager.cachedDeviceManager.clearNonBondedDevices()
    }

    /**
     * 开始扫描
     */
    suspend fun startScan() {
        logTagD(TAG, "开始扫描")
        scanTimeOutJob?.cancel()
        launch {
            _viewStateFlow.emit(AddWpViewState.SCANNING)
            WPDeviceInfoManager.clearUnSavedBTDevice()
        }
        selAddressFlow.value = ""
        localManager.cachedDeviceManager.clearNonBondedDevices()
        allDevicesFlow.value = CachedDevicesWrapper(emptyList())
        delay(1.seconds)
        localAdapter.startScanning(true)
        scanTimeOutJob = launch {
            delay(30.seconds)
            logTagW(TAG, "扫描超时, 还没有收到系统回调,尝试重新注册")
            _isScanningFlow.emit(false)
            localManager.eventManager.unregisterCallback(dockBluetoothCallback)
            localManager.eventManager.registerCallback(dockBluetoothCallback)
            stopScan()
        }
    }

    private fun stopScan() {
        logTagV(TAG, "停止扫描")
        scanTimeOutJob?.cancel()
        localAdapter.stopScanning()
    }

    fun updateSelDev(dev: FoundWPDevice) {
        if (!dev.sel) {
            logTagD(TAG, "updateSelDev:${dev}")
            selAddressFlow.value = dev.address
        }
    }

    /**
     * 连接选中的设备
     */
    @SuppressLint("MissingPermission")
    fun connectSelDev() {
        val selDevice = allDevicesFlow.value.cachedDevices.firstOrNull {
            it.device.address == selAddressFlow.value
        }
        if (selDevice == null) {
            logTagW(TAG, "没找到要连接的设备?")
            return
        }
        launch {
            if (WPDeviceInfoManager.checkAndMarkConnecting(selDevice.wpName())) {
                logTagV(TAG, "连接新设备:${selDevice.wpName()}")
                _viewStateFlow.emit(AddWpViewState.CONNECTING) // 切换到连接UI
                startConnectTimeout()
                connect(selDevice)
            } else {
                logTagW(TAG, "不能连接新设备")
            }

        }
    }

    private fun startConnectTimeout() {
        connectDownCount?.cancel()
        connectDownCount = launch {
            logTagV(TAG, "开始连接倒计时")
            delay(WPDeviceInfoManager.MAX_CONNECTING_TIME)
            logTagV(TAG, "连接超时")

            stopScan()

            deleteLastConnectingDev()

            allDevicesFlow.value = CachedDevicesWrapper(emptyList())
            _viewStateFlow.emit(AddWpViewState.CONNECT_TIMEOUT)
        }

    }

    @SuppressLint("MissingPermission")
    private fun connect(device: CachedBluetoothDevice) {
        logTagD(TAG, "connect:${device.wpName()}")
        connectingDevice = device
        val bondState = device.bondState
        if (bondState == BluetoothDevice.BOND_BONDED) {
            logTagD(TAG, "connect:已配对, 重新连接")
            device.connect()
        } else {
            logTagD(TAG, "connect:未配对, 开始配对")
            device.startPairing().also {
                logTagD(TAG, "connect:配对结果:$it")
            }
        }
    }

    private inner class DockBluetoothCallback : BluetoothCallback {
        override fun onBluetoothStateChanged(bluetoothState: Int) {}

        override fun onScanningStateChanged(started: Boolean) {
            if (!started) {
                scanTimeOutJob?.cancel()
            }
            launch {
                logTagV(TAG, "onScanningStateChanged(Sys callback):$started")
                _isScanningFlow.emit(started)
            }
        }

        override fun onDeviceAdded(cachedDevice: CachedBluetoothDevice?) {
            logTagD(TAG, "onDeviceAdded:${cachedDevice?.name}")
            allDevicesFlow.value =
                CachedDevicesWrapper(localManager.cachedDeviceManager.cachedDevicesCopy)
        }

        override fun onDeviceDeleted(cachedBluetoothDevice: CachedBluetoothDevice?) {
            logTagD(TAG, "onDeviceDeleted:${cachedBluetoothDevice?.name}")
            allDevicesFlow.value =
                CachedDevicesWrapper(localManager.cachedDeviceManager.cachedDevicesCopy)
        }

        override fun onDeviceBondStateChanged(
            cachedDevice: CachedBluetoothDevice?,
            bondState: Int
        ) {
        }

        override fun onConnectionStateChanged(cachedDevice: CachedBluetoothDevice?, state: Int) {

        }
    }

    private data class CachedDevicesWrapper(
        val cachedDevices: Collection<CachedBluetoothDevice>,
        val createTime: Long = System.currentTimeMillis()
    )

    override fun onCleared() {
        super.onCleared()
        logTagV(TAG, "onCleared")
        scanTimeOutJob?.cancel()
        connectDownCount?.cancel()
        stopScan()
        localManager.cachedDeviceManager.clearNonBondedDevices()
        localManager.eventManager.unregisterCallback(dockBluetoothCallback)
    }

    fun clearUnBoundDev() {
        logTagD(TAG, "clearUnBoundDev")
        localManager.cachedDeviceManager.clearNonBondedDevices()
    }

    enum class AddWpViewState {
        SCANNING,       // 搜索中
        CONNECTING,     // 连接中
        CONNECT_TIMEOUT // 连接超时
    }

    private fun CachedBluetoothDevice.wpName(): String {
        val targetName = name
        if (targetName.startsWith(WP_NAME_PREFIX, true)) {
            return targetName
        }
        val name = wpNameCache[address] ?: targetName
        return targetName
    }
}