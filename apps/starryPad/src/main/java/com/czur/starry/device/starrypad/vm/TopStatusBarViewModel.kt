package com.czur.starry.device.starrypad.vm

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import com.czur.starry.device.starrypad.devinfo.WPDeviceInfoManager
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map

/**
 * Created by 陈丰尧 on 2024/3/12
 * 顶部状态栏的ViewModel
 */
class TopStatusBarViewModel(application: Application) : AndroidViewModel(application) {
    // 连接的设备数量
    val connectDevCountFlow = WPDeviceInfoManager.connectingClientCountFlow

    // 是否有连接的设备
    val hasConnectDevFlow = connectDevCountFlow.map { it > 0 }.distinctUntilChanged()
}