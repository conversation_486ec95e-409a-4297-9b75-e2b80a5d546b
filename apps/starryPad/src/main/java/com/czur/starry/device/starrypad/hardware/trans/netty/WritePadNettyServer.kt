package com.czur.starry.device.starrypad.hardware.trans.netty

import com.czur.czurutils.global.CZAppInfo
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.utils.SettingHandler
import com.czur.starry.device.starrypad.BuildConfig
import com.czur.starry.device.starrypad.common.NETTY_PORT
import com.czur.starry.device.starrypad.common.WPFuncOption
import com.czur.starry.device.starrypad.devinfo.WPDeviceInfoManager
import com.czur.starry.device.starrypad.hardware.IRemoteMsgHelper
import com.czur.starry.device.starrypad.hardware.ServerProtoBuilder
import com.czur.starry.writepadlib.netty.pipeline.WritePadNettyFrameDecoder
import com.czur.starry.writepadlib.proto.WPTransferData
import com.czur.starry.writepadlib.proto.WPTransferData.Mode
import com.czur.starry.writepadlib.proto.WPTransferData.NettyMessage
import com.czur.starry.writepadlib.util.devID
import com.google.common.collect.BiMap
import com.google.common.collect.HashBiMap
import io.netty.bootstrap.ServerBootstrap
import io.netty.channel.ChannelHandlerContext
import io.netty.channel.ChannelId
import io.netty.channel.ChannelInboundHandlerAdapter
import io.netty.channel.ChannelInitializer
import io.netty.channel.ChannelOption
import io.netty.channel.ChannelOutboundHandlerAdapter
import io.netty.channel.ChannelPromise
import io.netty.channel.SimpleChannelInboundHandler
import io.netty.channel.group.DefaultChannelGroup
import io.netty.channel.nio.NioEventLoopGroup
import io.netty.channel.socket.nio.NioServerSocketChannel
import io.netty.channel.socket.nio.NioSocketChannel
import io.netty.handler.codec.LengthFieldPrepender
import io.netty.handler.codec.protobuf.ProtobufDecoder
import io.netty.handler.codec.protobuf.ProtobufEncoder
import io.netty.handler.timeout.IdleStateEvent
import io.netty.handler.timeout.IdleStateHandler
import io.netty.util.concurrent.Future
import io.netty.util.concurrent.GenericFutureListener
import io.netty.util.concurrent.GlobalEventExecutor
import io.netty.util.internal.logging.InternalLoggerFactory
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import java.util.concurrent.TimeUnit
import kotlin.coroutines.resume


/**
 * Created by 陈丰尧 on 2023/10/19
 * Netty服务端, 消息转发的核心
 */
private const val TAG = "WritePadNettyServer"

interface INettyMsgSender {
    /**
     * 发送消息, 异步发送
     * @param msg 消息
     * @param devID 设备ID, 如果为null, 则发送给所有的客户端
     */
    suspend fun sendMsg(msg: NettyMessage, devID: String? = null)

    /**
     * 发送消息, 同步发送
     * @param msg 消息
     * @param devID 设备ID, 如果为null, 则发送给所有的客户端
     */
    suspend fun sendMsgSync(msg: NettyMessage, devID: String? = null)
}

class WritePadNettyServer(val msgHelper: IRemoteMsgHelper) : INettyMsgSender {
    private var server: ServerBootstrap? = null

    private val channelGroup by lazy {
        DefaultChannelGroup("WritePadNettyServer", GlobalEventExecutor.INSTANCE)
    }

    /**
     * 客户端的ip和name对应
     * key: channelID
     * value: devID
     */
    private var remoteIps: BiMap<ChannelId, String> = HashBiMap.create()

    private val normalListener =
        GenericFutureListener<Future<Void>> { future ->
            if (!future.isSuccess) {
                logTagE(TAG, "消息发送失败", tr = future.cause())
            }
        }

    suspend fun startServer() = withContext(Dispatchers.IO) {
        logTagD(TAG, "启动Netty")
        InternalLoggerFactory.setDefaultFactory(LogcatLoggerFactory)
        val bossGroup = NioEventLoopGroup()
        val workerGroup = NioEventLoopGroup()

        server = ServerBootstrap().group(bossGroup, workerGroup)
            .channel(NioServerSocketChannel::class.java)
            .childOption(ChannelOption.SO_KEEPALIVE, true)
            .childOption(ChannelOption.TCP_NODELAY, true)
            .childOption(ChannelOption.SO_REUSEADDR, true)
            .childHandler(object : ChannelInitializer<NioSocketChannel>() {
                override fun initChannel(ch: NioSocketChannel) {
                    ch.pipeline().apply {
                        addLast(WritePadNettyFrameDecoder())

                        addLast("frameEncoder", LengthFieldPrepender(4))
                        addLast("ProtobufEncoder", ProtobufEncoder())

                        addLast(ProtobufDecoder(NettyMessage.getDefaultInstance()))
                        // 空闲3分钟就会断开连接
                        addLast("idle", IdleStateHandler(0, 0, 3, TimeUnit.MINUTES))

                        if (BuildConfig.DEBUG) {
                            addLast("MsgLog", NettyDataLogChannel())
                        }

                        addLast("ClientCount", object : ChannelInboundHandlerAdapter() {
                            override fun channelActive(ctx: ChannelHandlerContext) {
                                super.channelActive(ctx)
                                logTagD(TAG, "客户端连接成功: ${ctx.channel().remoteAddress()}")
                                channelGroup.add(ctx.channel())

                                val langStr = SettingHandler.czurLang.webCode
                                val msg = ServerProtoBuilder.withServerMode {
                                    mode = Mode.MODE_MOUSE  // 固定为鼠标模式, 等待客户端查询
                                    language = langStr
                                }
                                // 同步给客户端StarryInfo
                                val starryInfo = ServerProtoBuilder.withStarryInfo(
                                    starrySN = Constants.SERIAL,
                                    starryLang = langStr,
                                    mirrorEnable = WPFuncOption.mirrorEnable,   // 会控同传是否启用
                                    starryVersion = CZAppInfo.appVersionCode    // Starry版本号
                                )

                                runBlocking {
                                    sendMsgToTarget(starryInfo, ctx.channel().id(), false)
                                    sendMsgToTarget(msg, ctx.channel().id(), false)
                                }
                            }

                            override fun channelInactive(ctx: ChannelHandlerContext) {
                                super.channelInactive(ctx)
                                logTagD(TAG, "客户端断开连接")

                                channelGroup.remove(ctx.channel())

                                val channelID = ctx.channel().id()

                                val remoteName = remoteIps[channelID]
                                logTagI(
                                    TAG, "断开:${channelID} - $remoteName"
                                )
                                remoteName?.let {
                                    msgHelper.onClintOffline(it)
                                }
                            }
                        })

                        addLast("IllegalDeviceInspection",
                            object : SimpleChannelInboundHandler<NettyMessage>() {
                                override fun channelRead0(
                                    ctx: ChannelHandlerContext?,
                                    msg: NettyMessage?
                                ) {
                                    val channelID = ctx?.channel()?.id()
                                    val devID = remoteIps[channelID]
                                    if (devID != null) {
                                        val isLegal =
                                            WPDeviceInfoManager.isLegalDeviceFromCache(devID)
                                        if (!isLegal) {
                                            logTagW(
                                                TAG,
                                                "IllegalDeviceInspection: 设备不合法(${devID}), 断开连接"
                                            )
                                            msgHelper.onClintOffline(devID)
                                            val rejectMsg = ServerProtoBuilder.withProcessControl(
                                                WPTransferData.ProcessControlEvent.CONN_REJECT
                                            )
                                            ctx?.writeAndFlush(rejectMsg)   // 不断开连接,等待客户端断开
                                            return
                                        }
                                    }

                                    ctx?.fireChannelRead(msg)
                                }

                            })

                        addLast("NettyData", object : SimpleChannelInboundHandler<NettyMessage>() {

                            override fun userEventTriggered(
                                ctx: ChannelHandlerContext?, evt: Any?
                            ) {
                                if (evt is IdleStateEvent) {
                                    logTagW(
                                        TAG, "客户端超时断开连接: ${
                                            ctx?.channel()?.id()
                                        } - ${ctx?.channel()?.remoteAddress()}"
                                    )
                                    ctx?.close()
                                }
                                super.userEventTriggered(ctx, evt)
                            }

                            override fun channelRead0(
                                ctx: ChannelHandlerContext?, msg: NettyMessage?
                            ) {
                                if (msg == null) {
                                    ctx?.fireChannelRead(null)
                                    return
                                }

                                // 处理心跳
                                if (msg.hasHeartbeatMsg()) {
                                    if (msg.heartbeatMsg.type == WPTransferData.WPHeartbeatType.PING) {
                                        val pong = ServerProtoBuilder.withHeartbeat {
                                            type = WPTransferData.WPHeartbeatType.PONG
                                        }
                                        ctx?.writeAndFlush(pong)
                                        return
                                    }
                                }

                                // 刷新ip和设备ID的对应关系
                                val devID = msg.devID
                                if (devID.isEmpty()) {
                                    logTagW(TAG, "devID为空")
                                    return
                                }

                                if (msg.hasVersionControl()) {
                                    // 版本维护
                                    val clientVersion = msg.versionControl.versionCode
                                    logTagV(TAG, "客户端版本号: $clientVersion - ${msg.devID}")
                                    WPDeviceInfoManager.updateClientVersion(
                                        devID, clientVersion
                                    )
                                    // 回复客户端 Starry侧版本号
                                    val response =
                                        ServerProtoBuilder.withVersionInfo(CZAppInfo.appVersionCode)
                                    ctx?.writeAndFlush(response)
                                    return
                                }

                                ctx?.channel()?.id()?.let {
                                    try {
                                        remoteIps.forcePut(
                                            it, devID
                                        )   // 需要强制写入, 因为有可能同一个devID但是换channel了

                                    } catch (e: Throwable) {
                                        logTagW(TAG, "forcePut异常 id:${it},dev:${devID}", tr = e)
                                        remoteIps[it] = devID
                                    }
                                }

                                msgHelper.onRemoteMsgReceived(msg)
                            }
                        })


                        // 发送消息时屏蔽掉旧版本的设备
                        addLast("blockOldVersion", object : ChannelOutboundHandlerAdapter() {
                            override fun write(
                                ctx: ChannelHandlerContext, msg: Any, promise: ChannelPromise
                            ) {
                                val clientID = remoteIps[ctx.channel().id()] ?: return super.write(
                                    ctx, msg, promise
                                )
                                if (WPDeviceInfoManager.hasOldVersionDevice) {
                                    if (isNeedCheckVersionMsg(msg)) {
                                        val clientVersionLegal =
                                            WPDeviceInfoManager.clientVersionLegal(clientID)
                                        if (!clientVersionLegal) {
                                            logTagW(
                                                TAG, "客户端:${clientID}版本不合法, 不允许发送消息"
                                            )
                                            promise.setSuccess()
                                            return
                                        }
                                    } else if (msg is NettyMessage && msg.dataBodyCase == NettyMessage.DataBodyCase.SERVER_MODE) {
                                        val clientVersionLegal =
                                            WPDeviceInfoManager.clientVersionLegal(clientID)
                                        if (!clientVersionLegal) {
                                            logTagW(
                                                TAG,
                                                "客户端:${clientID}版本不合法, 只允许发送鼠标模式"
                                            )
                                            super.write(ctx, ServerProtoBuilder.withServerMode {
                                                mode = WPTransferData.Mode.MODE_MOUSE
                                            }, promise)
                                            return
                                        }
                                    }
                                }
                                try {
                                    super.write(ctx, msg, promise)
                                } catch (e: Throwable) {
                                    logTagW(TAG, "write异常", tr = e)
                                }
                            }
                        })
                        addLast("ChannelError", object : ChannelInboundHandlerAdapter() {
                            override fun exceptionCaught(
                                ctx: ChannelHandlerContext?,
                                cause: Throwable?
                            ) {

                                val failChannel = ctx?.channel()
                                logTagW(
                                    TAG,
                                    "ChannelError: ${failChannel?.id()} - ${failChannel?.remoteAddress()} - ${cause?.message}",
                                    tr = cause
                                )
                                if (failChannel?.isActive == true) {
                                    logTagW(TAG, "channel 未关闭")
                                } else {
                                    logTagW(TAG, "channel已经关闭")
                                    channelGroup.remove(failChannel)
                                }
                            }
                        })
                    }
                }

            }).apply {
                bind(NETTY_PORT)
            }

    }

    private fun isNeedCheckVersionMsg(msg: Any): Boolean {
        if (msg !is NettyMessage) return false
        return when (msg.dataBodyCase) {
            NettyMessage.DataBodyCase.BIZ_DATA, NettyMessage.DataBodyCase.MODE_CHANGE_REQ -> true

            NettyMessage.DataBodyCase.PROCESS_CONTROL -> {
                // 流程控制, 部分有限制
                val event =
                    if (msg.processControl.hasProcessEvent()) msg.processControl.processEvent else null
                when (event) {
                    WPTransferData.ProcessControlEvent.DRAW_ACTION_PAGE_CHANGING,
                    WPTransferData.ProcessControlEvent.DRAW_ACTION_PAGE_COUNT_LIMIT,
                    WPTransferData.ProcessControlEvent.DRAW_ACTION_PAGE_CHANGE_ILLEGAL,
                    WPTransferData.ProcessControlEvent.DRAW_ACTION_SAVING,
                    WPTransferData.ProcessControlEvent.DRAW_ACTION_SAVE_FINISH,
                        -> true

                    else -> false
                }
            }

            else -> false
        }
    }

    /**
     * 发送消息, 异步发送
     * @param msg 消息
     * @param devID 设备ID, 如果为null, 则发送给所有的客户端
     */
    override suspend fun sendMsg(msg: NettyMessage, devID: String?) {
        if (devID == null) {
            broadcastMsg(msg, false)
        } else {
            val channelId = remoteIps.inverse()[devID]
            if (channelId == null) {
                logTagW(TAG, "找不到客户端:$devID")
            } else {
                sendMsgToTarget(msg, channelId, false)
            }
        }
    }

    /**
     * 发送消息, 同步发送
     * @param msg 消息
     * @param devID 设备ID, 如果为null, 则发送给所有的客户端
     */
    override suspend fun sendMsgSync(msg: NettyMessage, devID: String?) {
        withContext(Dispatchers.IO) {
            if (devID == null) {
                broadcastMsg(msg, true)
            } else {
                val channelId = remoteIps.inverse()[devID]
                if (channelId == null) {
                    logTagW(TAG, "找不到客户端:$devID")
                } else {
                    sendMsgToTarget(msg, channelId, true)
                }
            }
        }
    }

    suspend fun stopServer() = withContext(Dispatchers.IO) {
        try {
            server?.config()?.let {
                it.group().shutdownGracefully().await()
                it.childGroup().shutdownGracefully().await()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private suspend fun sendMsgToTarget(
        msg: NettyMessage, channelId: ChannelId, sync: Boolean
    ) {
        val targetClient = channelGroup.find(channelId)
        if (targetClient == null) {
            logTagW(TAG, "找不到客户端:$channelId")
            return
        } else {
            if (sync) {
                suspendCancellableCoroutine<Unit> { it ->
                    targetClient.writeAndFlush(msg).addListener { future ->
                        if (!future.isSuccess) {
                            logTagE(TAG, "发送消息失败", tr = future.cause())
                        }
                        it.resume(Unit)
                    }
                }
            } else {
                targetClient.writeAndFlush(msg).addListener(normalListener)
            }
        }
    }

    private suspend fun broadcastMsg(msg: NettyMessage, sync: Boolean) {
        if (sync) {
            suspendCancellableCoroutine<Unit> { it ->
                channelGroup.writeAndFlush(msg).addListener { future ->
                    if (!future.isSuccess) {
                        logTagE(TAG, "广播消息失败(同步)", tr = future.cause())
                    }
                    it.resume(Unit)
                }
            }

        } else {
            channelGroup.writeAndFlush(msg).addListener(normalListener)
        }
    }


}