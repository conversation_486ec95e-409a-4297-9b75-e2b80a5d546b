package com.czur.starry.device.starrypad.util

import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.starry.device.starrypad.vm.PaletteViewModel
import com.czur.starry.writepadlib.widget.drawing.palette.CZPaletteView
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

/**
 * Created by 陈丰尧 on 2025/2/7
 */
private const val TAG = "PaintUIHelper"

class PaintUIHelper(
    private val scope: CoroutineScope,
    private val paletteViewMode: PaletteViewModel,
    private val paletteView: CZPaletteView
) : CoroutineScope by scope {


    /**
     * 创建新页面
     */
    fun createNewPage(devID: String) {
        logTagD(TAG, "新建-${devID}")
        if (!paletteViewMode.canCreateNewPage()) {
            logTagI(TAG, "无法创建新页面")
            paletteViewMode.sendCreateNewPageError(devID)
            return
        }

        if (paletteViewMode.isCreatingNewPage()) {
            logTagI(TAG, "正在创建新页面(${devID})")
            return
        }
        paletteViewMode.setCreatingNewPage(true)
        launch {
            paletteViewMode.doWithPaletteState(PaletteState.CONTROLLING) {
                paletteViewMode.createNewPage(paletteView.getDrawBitmap())
            }
            paletteViewMode.setCreatingNewPage(false)
        }
    }

    /**
     * 撤销
     */
    fun undo() {
        logTagD(TAG, "撤销")
        launch {
            paletteViewMode.doWithPaletteState(PaletteState.CONTROLLING) {
                paletteView.undo()?.let {
                    paletteViewMode.broadcastPaintContent(it, false)
                }
            }
        }
    }

    /**
     * 重做
     */
    fun redo() {
        logTagD(TAG, "重做")
        launch {
            paletteViewMode.doWithPaletteState(PaletteState.CONTROLLING) {
                paletteView.redo()?.let {
                    paletteViewMode.broadcastPaintContent(it, false)
                }
            }
        }
    }
}