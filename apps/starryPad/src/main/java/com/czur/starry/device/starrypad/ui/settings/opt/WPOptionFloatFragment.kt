package com.czur.starry.device.starrypad.ui.settings.opt

import android.os.Bundle
import com.czur.starry.device.baselib.base.v2.fragment.floating.CZVBFloatingFragment
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatOnResume
import com.czur.starry.device.starrypad.databinding.FloatFragmentWpOptionBinding
import com.czur.starry.device.starrypad.repository.SettingRepository

/**
 * Created by 陈丰尧 on 2024/11/15
 */
class WPOptionFloatFragment : CZVBFloatingFragment<FloatFragmentWpOptionBinding>() {
    override fun FloatFragmentWpOptionBinding.initBindingViews() {
        closeIv.setOnClickListener {
            dismiss()
        }


        shareWhenExitSwitch.setOnSwitchChangeListener { isOn, fromUser ->
            if (fromUser) {
                launch {
                    SettingRepository.setShareWhenExit(isOn)
                }
            }
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        repeatOnResume {
            val shareWhenExit = SettingRepository.shareWhenExit()
            binding.shareWhenExitSwitch.setSwitchOn(shareWhenExit)
        }
    }
}