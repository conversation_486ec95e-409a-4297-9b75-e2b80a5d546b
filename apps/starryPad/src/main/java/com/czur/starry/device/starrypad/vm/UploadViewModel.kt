package com.czur.starry.device.starrypad.vm

import android.app.Application
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import androidx.lifecycle.AndroidViewModel
import com.czur.czurutils.img.QrCodeUtil
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.starrypad.R
import com.czur.starry.device.starrypad.db.entity.PaintEntity
import com.czur.starry.device.starrypad.hardware.WritePadDeviceModeManager
import com.czur.starry.device.starrypad.repository.PaintRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.io.File

/**
 * Created by 陈丰尧 on 2022/5/10
 */
class UploadViewModel(application: Application) : AndroidViewModel(application) {
    companion object {
        private const val TAG = "UploadViewModel"
    }

    private val logoBitmap by lazy {
        BitmapFactory.decodeResource(CZURAtyManager.appContext.resources, R.drawable.qr_logo)
    }

    private val _maxSizeFlow = MutableStateFlow(0L)
    val maxSizeFlow = _maxSizeFlow.asStateFlow()

    private val _finishSizeFlow = MutableStateFlow(0L)
    val finishSizeFlow = _finishSizeFlow.asStateFlow()

    private val _uploadIndexFlow = MutableStateFlow(1)
    val uploadIndexFlow = _uploadIndexFlow.asStateFlow()

    val serverModelFlow = WritePadDeviceModeManager.serverModeFlow

    fun reset() {
        _maxSizeFlow.value = 0
        _finishSizeFlow.value = 0
        _uploadIndexFlow.value = 1
    }

    /**
     * 开始上传
     * 进度条信息按照文件个数来的 22.9.19 光博提
     */
    suspend fun startUpload(uploadPaintEntities: List<PaintEntity>): Bitmap {
        // 计算总大小
        val sumSize = uploadPaintEntities.sumOf {
            File(it.contentImgPath).length()
        }
        logTagD(TAG, "开始上传")
        _maxSizeFlow.value = sumSize

        val qrCodeUrl =
            PaintRepository.uploadPaints(uploadPaintEntities) { finishSize, uploadIndex ->
                logTagV(TAG, "上传完成:${finishSize * 100F / sumSize}%, 第${uploadIndex}张")
                _finishSizeFlow.value = finishSize
                _uploadIndexFlow.value = uploadIndex + 1
            }
        logTagD(TAG, "上传全部完成:$qrCodeUrl")
        _finishSizeFlow.value = sumSize
        // 获取二维码信息
        return QrCodeUtil.generateQrCodeBmp(qrCodeUrl){
            edge = 260
            logoConfig {
                logoBmp = logoBitmap
                pointColor = 0xFF3D3D3D.toInt()
                bgColor = Color.WHITE
            }
        }
    }
}