package com.czur.starry.device.starrypad.ui.settings.detail

import android.os.Bundle
import android.text.Html
import android.text.Spanned
import androidx.fragment.app.activityViewModels
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.v2.fragment.floating.CZVBFloatingFragment
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.starrypad.databinding.FloatFragmentWpRnBinding
import com.czur.starry.device.starrypad.devinfo.WPDevInfo
import com.czur.starry.device.starrypad.ui.settings.WritePadDevInfoViewModel
import kotlinx.coroutines.flow.map

/**
 * Created by 陈丰尧 on 2024/3/29
 */
class WritePadReleaseNoteFloatFragment : CZVBFloatingFragment<FloatFragmentWpRnBinding>() {
    companion object {
        private const val TAG = "WritePadReleaseNoteFloatFragment"

        const val KEY_DEV_NAME = "KEY_DEV_NAME"
    }

    private val wpViewModel: WritePadDevInfoViewModel by activityViewModels()
    private var currentDevInfo: WPDevInfo? = null

    var onBtnClick: ((devInfo: WPDevInfo) -> Unit)? = null

    override fun FloatingFragmentParams.initFloatingParams() {
        this.outSideDismiss = true
        this.floatingBgMode = FloatingBgMode.Dark
    }

    override fun FloatFragmentWpRnBinding.initBindingViews() {
        closeIv.setOnClickListener {
            dismiss()
        }

        upgradeDevBtn.setOnClickListener {
            if (currentDevInfo == null) {
                logTagW(TAG, "没有设备信息")
                return@setOnClickListener
            }
            onBtnClick?.invoke(currentDevInfo!!)
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        val devName = requireArguments().getString(WPDevDetailFloatFragment.KEY_DEV_NAME)
        if (devName.isNullOrEmpty()) {
            dismiss()
            logTagW(TAG, "没有设备名称")
            return
        }
        val targetDevFlow = wpViewModel.writePadInfoListFlow.map { list ->
            list.firstOrNull { it.name == devName }
        }
        repeatCollectOnResume(targetDevFlow) {
            currentDevInfo = it

            val versionName = it?.otaInfo?.versionName ?: ""
            logTagD(TAG, "versionName: $versionName")
            binding.versionNameTv.text = versionName
            if (versionName.isNotEmpty()) {
                val rn = wpViewModel.getReleaseNote(versionName)
                binding.releaseNoteTv.text = rn
            } else {
                logTagW(TAG, "没有版本号")
                dismiss()
            }

        }
    }

    override fun onDismiss() {
        super.onDismiss()
        onBtnClick = null
    }
}