package com.czur.starry.device.starrypad.hardware

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.SystemClock
import androidx.core.content.ContextCompat
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.writepadlib.proto.WPTransferData

/**
 * Created by 陈丰尧 on 2025/3/25
 */
private const val TAG = "TouchBarEventReceiver"
private const val ACTION_BOOT_MARK = "com.czur.starry.device.starrypad.BOOT_MARK"
private const val ACTION_BOOT_PALETTE = "com.czur.starry.device.starrypad.BOOT_PALETTE"

class TouchBarEventReceiver : BroadcastReceiver() {
    private var lastResponseTime = 0L
    fun register(context: Context) {
        logTagV(TAG, "注册控制栏广播")
        val intentFilter = IntentFilter().apply {
            addAction("com.czur.starry.device.starrypad.BOOT_MARK")
            addAction("com.czur.starry.device.starrypad.BOOT_PALETTE")
        }
        ContextCompat.registerReceiver(context, this, intentFilter, ContextCompat.RECEIVER_EXPORTED)
    }

    fun unregister(context: Context) {
        logTagV(TAG, "注销控制栏广播")
        context.unregisterReceiver(this)
    }


    override fun onReceive(context: Context, intent: Intent) {
        logTagV(TAG, "接收到控制栏广播")
        if (SystemClock.elapsedRealtime() - lastResponseTime < ONE_SECOND) {
            logTagW(TAG, "控制栏广播间隔过短，丢弃")
            return
        }
        lastResponseTime = SystemClock.elapsedRealtime()

        when (intent.action) {
            ACTION_BOOT_MARK -> {
                logTagV(TAG, "接收到控制栏广播: 批注模式")
                WritePadDeviceModeManager.updateClientModeReq(
                    WPTransferData.ModeChangeReq.newBuilder()
                        .setDevID(ServerProtoBuilder.devID)
                        .setMode(WPTransferData.Mode.MODE_MARK)
                        .build()
                )
            }

            ACTION_BOOT_PALETTE -> {
                logTagV(TAG, "接收到控制栏广播: 白板模式")
                WritePadDeviceModeManager.updateClientModeReq(
                    WPTransferData.ModeChangeReq.newBuilder()
                        .setDevID(ServerProtoBuilder.devID)
                        .setMode(WPTransferData.Mode.MODE_PALETTE)
                        .build()
                )
            }

            else -> {
                logTagW(TAG, "未知的控制栏广播: ${intent.action}")
            }

        }
    }
}