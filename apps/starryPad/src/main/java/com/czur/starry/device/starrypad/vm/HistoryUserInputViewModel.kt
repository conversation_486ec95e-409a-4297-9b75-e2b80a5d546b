package com.czur.starry.device.starrypad.vm

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.onEach

/**
 * Created by 陈丰尧 on 2024/6/19
 */
private const val USER_IDLE_TIME = 3 * ONE_SECOND   // 用户空闲时间

class HistoryUserInputViewModel(application: Application) : AndroidViewModel(application) {
    enum class UserBehavior {
        IDLE,   // 空闲
        INPUT,  // 输入
    }

    private val _userInputFlow = MutableStateFlow(0L)
    private val _userBehaviorFlow = MutableStateFlow(UserBehavior.INPUT) // 先让用户看见, 因为第一次进入页面时, 用户是有操作的
    val userBehaviorFlow = _userBehaviorFlow.asStateFlow()

    init {
        launch(Dispatchers.Default) {
            _userInputFlow.onEach {
                _userBehaviorFlow.value = UserBehavior.INPUT
            }.collectLatest {
                delay(USER_IDLE_TIME)
                _userBehaviorFlow.value = UserBehavior.IDLE
            }
        }
    }

    fun onUserInput() {
        _userInputFlow.value = System.currentTimeMillis()
    }
}