package com.czur.starry.device.starrypad.repository

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.BitmapRegionDecoder
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.utils.getTimeStr
import com.czur.starry.device.baselib.utils.randomStr
import com.czur.starry.device.baselib.utils.saveToFile
import com.czur.starry.device.starrypad.db.StarryPadDataBase
import com.czur.starry.device.starrypad.db.entity.AlbumEntity
import com.czur.starry.device.starrypad.db.entity.AlbumWithPaintList
import com.czur.starry.device.starrypad.db.entity.PaintEntity
import com.czur.starry.device.starrypad.net.IUploadServer
import com.czur.starry.device.starrypad.net.UploadManager
import com.czur.starry.device.starrypad.util.BitmapUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * Created by 陈丰尧 on 2022/4/26
 */
object PaintRepository {
    private const val TAG = "PaintRepository"

    // 相册名称
    private const val ALBUM_NAME_PATTERN = "yyyy.MM.dd HH:mm:ss"

    private const val FILE_NAME_BG_IMG = "bgImg.webp"           // 背景图
    private const val FILE_NAME_DRAW_IMG = "drawImg.webp"       // 绘制图
    private const val FILE_NAME_CONTENT_IMAGE = "content.jpg"  // 内容图 如果是白板模式,则剪裁白边, 如果是标注模式,则为合成图

    private const val FILE_CONTENT_MIN_WIDTH = 1920     // 内容图最小宽度
    private const val FILE_CONTENT_MIN_HEIGHT = 1080    // 内容图最小高度

    private val paintDirFile: File by lazy {
        File(globalAppCtx.filesDir, "paints").apply {
            if (!exists()) {
                mkdirs()
            }
        }
    }

    private val ossService: IUploadServer by lazy { HttpManager.getService() }

    /**
     * 上传工具
     */
    private val uploadManager by lazy {
        UploadManager()
    }

    val albumDataLive = StarryPadDataBase.instance.albumDao().getAllAlbums()
    val recentAlbumDataLive = StarryPadDataBase.instance.albumDao().getRecentAlbums(6)

    /**
     * 删除一张笔记
     */
    suspend fun delPaint(paintEntity: PaintEntity) = withContext(Dispatchers.IO) {
        StarryPadDataBase.instance.albumDao().delPaint(paintEntity)
        File(paintEntity.paintPath).parentFile?.deleteRecursively()
    }

    /**
     * 重新排序笔记
     */
    suspend fun rePositionPaints(paintList: List<PaintEntity>):List<PaintEntity> {
        return withContext(Dispatchers.IO) {
            val rePosList = paintList.mapIndexed { index, paintEntity ->
                logTagD(TAG, "$paintEntity $index")
                val paintDir = File(paintEntity.paintPath).parentFile!!
                val albumName = paintDir.parentFile!!.name
                val paintDirName = paintDir.name
                if (paintDirName != index.toString()) {
                    val newPaintDir = File(paintDir.parentFile, index.toString())
                    paintDir.renameTo(newPaintDir)

                    val newPaintPath = paintEntity.paintPath.replace(
                        "${albumName}/${paintDirName}",
                        "${albumName}/${index}"
                    )
                    val newBgImgPath = paintEntity.bgImgPath?.replace(
                        "${albumName}/${paintDirName}",
                        "${albumName}/${index}"
                    )
                    val newDrawImgPath = paintEntity.drawImgPath.replace(
                        "${albumName}/${paintDirName}",
                        "${albumName}/${index}"
                    )
                    val newContentImgPath = paintEntity.contentImgPath.replace(
                        "${albumName}/${paintDirName}",
                        "${albumName}/${index}"
                    )

                    paintEntity.copy(
                        paintPath = newPaintPath,
                        bgImgPath = newBgImgPath,
                        drawImgPath = newDrawImgPath,
                        contentImgPath = newContentImgPath
                    )
                } else {
                    paintEntity
                }
            }

            StarryPadDataBase.instance.albumDao().insertOrUpdatePaints(rePosList)

            rePosList
        }
    }

    /**
     * 删除指定相册
     * 会将对应的图片数据一同删除掉
     */
    suspend fun delAlbums(vararg albums: AlbumEntity) {
        withContext(Dispatchers.IO) {
            albums.forEach { album ->
                val albumDir = File(album.dirPath)
                logTagD(TAG, "删除相册:${album.albumId}")
                // 删除数据库
                val paints = StarryPadDataBase.instance.albumDao().delAlbum(album)
                // 删除对应文件
                paints.forEach {
                    delPaint(it)
                }
                logTagV(TAG, "删除相册根路径:${albumDir.absolutePath}")
                albumDir.deleteRecursively()
            }
        }
    }

    /**
     * 更新打开时间
     */
    suspend fun updateOpenTime(album: AlbumEntity) {
        withContext(Dispatchers.IO) {
            val newEntity = album.copy(lastOpenTime = System.currentTimeMillis())
            StarryPadDataBase.instance.albumDao().updateAlbum(newEntity)
        }
    }

    suspend fun updatePaintByDrawFile(
        albumWithPaintList: AlbumWithPaintList,
        contentsIndexDir: List<File>
    ): AlbumWithPaintList = withContext(Dispatchers.IO) {
        // 从数据库获取数据, 以便获取到正确的ID
        val nowDBData =
            StarryPadDataBase.instance.albumDao().getAlbumsByID(albumWithPaintList.album.albumId)
        val newAlbum = nowDBData.album.copy(lastOpenTime = System.currentTimeMillis())

        val newPaintList = contentsIndexDir.map { indexDir ->
            val index = indexDir.name.toInt()
            val paintDir = File(newAlbum.dirPath, index.toString())
            if (!paintDir.exists()) {
                paintDir.mkdirs()
            }
            val (cachedPaintFile, cachedBgFile) = PageCacheRepository.getDrawFilePair(indexDir)

            val paintFile = File(paintDir, FILE_NAME_DRAW_IMG)
            var bgFile: File? = null
            // 将缓存中的文件移动到真实存储路径
            cachedPaintFile.copyTo(paintFile, true)
            cachedBgFile?.let {
                bgFile = File(paintDir, FILE_NAME_BG_IMG)
                cachedBgFile.copyTo(bgFile!!, true)
            }

            // 制作内容图片
            val contentFile = createContentImgFile(paintFile, bgFile)

            nowDBData.paintList.getOrNull(index) ?: PaintEntity(
                paintPath = paintFile.absolutePath,
                bgImgPath = bgFile?.absolutePath,
                drawImgPath = paintFile.absolutePath,
                contentImgPath = contentFile.absolutePath,
                createTime = System.currentTimeMillis(),
                albumCreatorId = newAlbum.albumId
            )
        }
        logTagV(TAG, "更新绘画信息:${newPaintList.size}")
        // 更新数据库信息
        StarryPadDataBase.instance.albumDao().insertOrCreateAlbumWithPaint(newAlbum, newPaintList)
        AlbumWithPaintList(newAlbum, newPaintList)
    }

    suspend fun createOrAlbumAndPaintByDrawFile(content: List<File>): AlbumWithPaintList =
        withContext(Dispatchers.IO) {
            val albumDirFile = createNewAlbumFile() // 创建基础文件夹
            // 相册信息
            val createTime = System.currentTimeMillis()
            val createTimeStr = getTimeStr(ALBUM_NAME_PATTERN, createTime)
            val album = AlbumEntity(
                createTime = createTime,
                createTimeStr = createTimeStr,
                dirPath = albumDirFile.absolutePath,
                lastOpenTime = createTime
            )

            val paintList = content.mapIndexed { index, indexDir ->

                val paintDir = File(albumDirFile, index.toString())
                if (!paintDir.exists()) {
                    paintDir.mkdirs()
                }

                val (cachedPaintFile, cachedBgFile) = PageCacheRepository.getDrawFilePair(indexDir)
                val paintFile = File(paintDir, FILE_NAME_DRAW_IMG)
                var bgFile: File? = null
                // 将缓存中的文件移动到真实存储路径
                cachedPaintFile.copyTo(paintFile, true)
                cachedBgFile?.let {
                    bgFile = File(paintDir, FILE_NAME_BG_IMG)
                    cachedBgFile.copyTo(bgFile!!, true)
                }

                // 制作内容图片
                val contentFile = createContentImgFile(paintFile, bgFile)

                PaintEntity(
                    paintPath = paintFile.absolutePath,
                    bgImgPath = bgFile?.absolutePath,
                    drawImgPath = paintFile.absolutePath,
                    contentImgPath = contentFile.absolutePath,
                    createTime = createTime,
                    albumCreatorId = album.albumId
                )
            }
            StarryPadDataBase.instance.albumDao().insertOrCreateAlbumWithPaint(album, paintList)
        }

    /**
     * 获取相册中的绘画信息
     * @param albumID 相册ID
     * @return 相册信息和绘画信息
     */
    suspend fun getAlbumsByID(albumID: Long): AlbumWithPaintList {
        return withContext(Dispatchers.IO) {
            StarryPadDataBase.instance.albumDao().getAlbumsByID(albumID)
        }
    }


    suspend fun uploadPaints(
        paints: List<PaintEntity>,
        listener: (finishSize: Long, uploadIndex: Int) -> Unit
    ): String {
        val folderName = uploadManager.upload(paints, listener)
        return withContext(Dispatchers.IO) {
            ossService.getQRCodeUrl(folderName).withCheck().body
        }
    }


    private fun createNewAlbumFile(): File {
        return File(paintDirFile, randomStr()).apply {
            mkdirs()
        }
    }

    private suspend fun createContentImgFile(drawImageFile: File, bgImageFile: File?): File {
        return if (bgImageFile == null) {
            // 白板模式则进行白边剪裁
            val drawBitmap = createPaletteContentBitmap(drawImageFile)
            val paletteContentBitmap = BitmapUtil.margeBitmap(drawBitmap, null)
            createPaletteContentImgFile(drawImageFile.parentFile!!, paletteContentBitmap)
        } else {
            // 标注模式则进行图像合成
            createMarkContentImgFile(drawImageFile, bgImageFile)
        }
    }

    private suspend fun createPaletteContentImgFile(parentDir: File, contentBitmap: Bitmap): File =
        withContext(Dispatchers.IO) {
            val contentFile = File(parentDir, FILE_NAME_CONTENT_IMAGE)
            contentBitmap.saveToFile(contentFile)
            contentFile
        }

    private suspend fun createMarkContentImgFile(drawImageFile: File, bgImageFile: File): File =
        withContext(Dispatchers.IO) {
            val drawImgBmp = BitmapFactory.decodeFile(drawImageFile.absolutePath)
            val bgImgBmp = BitmapFactory.decodeFile(bgImageFile.absolutePath)
            val contentBitmap = BitmapUtil.margeBitmap(drawImgBmp, bgImgBmp)
            val contentFile = File(drawImageFile.parentFile, FILE_NAME_CONTENT_IMAGE)
            contentBitmap.saveToFile(contentFile)

            contentFile
        }

    /**
     * 对白板模式下的图片进行剪裁
     */
    private suspend fun createPaletteContentBitmap(srcFile: File): Bitmap =
        withContext(Dispatchers.IO) {
            val options = BitmapFactory.Options()
            options.inJustDecodeBounds = true
            BitmapFactory.decodeFile(srcFile.absolutePath, options)
            val srcWidth = options.outWidth
            val srcHeight = options.outHeight
            logTagD(TAG, "srcWidth:$srcWidth, srcHeight:$srcHeight")

            if (srcWidth <= FILE_CONTENT_MIN_WIDTH && srcHeight <= FILE_CONTENT_MIN_HEIGHT) {
                // 图片太小, 不需要裁剪
                logTagD(TAG, "图片太小, 不需要裁剪:${srcFile.absolutePath}")
                return@withContext BitmapFactory.decodeFile(srcFile.absolutePath)
            }

            // 采样率
            val sampleSize = 8
            logTagV(TAG, "采样率:$sampleSize")

            options.inSampleSize = sampleSize
            options.inJustDecodeBounds = false
            val bitmap = BitmapFactory.decodeFile(srcFile.absolutePath, options)
            val contentRect = BitmapUtil.getContentRect(bitmap, srcWidth, srcHeight)
            logTagD(TAG, "内容范围:$contentRect")
            if (contentRect.width() * contentRect.height() / (srcWidth * srcHeight).toFloat() > 0.8F) {
                // 内容太小, 不需要裁剪
                logTagD(TAG, "内容范围很大, 直接使用原图:${srcFile.absolutePath}")
                return@withContext bitmap
            }
            // 进行剪裁
            val decoder = BitmapRegionDecoder.newInstance(srcFile.absolutePath, false)
            val contentBitmap = decoder.decodeRegion(contentRect, null)
            contentBitmap
        }
}