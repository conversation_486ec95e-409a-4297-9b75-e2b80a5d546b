package com.czur.starry.device.starrypad.net

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

/**
 * Created by 陈丰尧 on 2022/5/10
 */
data class OSSTokenEntity(
    @SerializedName("AccessKeyId")
    val accessKeyId: String,
    @SerializedName("AccessKeySecret")
    val accessKeySecret: String,
    @SerializedName("BucketName")
    val bucketName: String,
    @SerializedName("Expiration")
    val expiration: String,
    @SerializedName("Prefix")
    val prefix: String,
    @SerializedName("RequestId")
    val requestId: String,
    @SerializedName("SecurityToken")
    val securityToken: String,
    val status: String
)

@Parcelize
data class WPOTAServerInfo(
    @SerializedName("packageUrl")
    val downloadUrl: String?,
    @SerializedName("md5")
    val md5: String?,
    @SerializedName("fileSize")
    val fileSize: Long,
    @SerializedName("version")
    val versionName: String?
) : Parcelable {
    /**
     * 是否可以升级
     */
    val canUpdate: Boolean
        get() = !md5.isNullOrEmpty() && !downloadUrl.isNullOrEmpty()
}