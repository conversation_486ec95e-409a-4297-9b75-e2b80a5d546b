package com.czur.starry.device.starrypad.ui.settings

import android.app.Application
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.text.Spanned
import androidx.lifecycle.AndroidViewModel
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.img.QrCodeUtil
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.StarryDevLocale
import com.czur.starry.device.baselib.utils.saveToFile
import com.czur.starry.device.starrypad.R
import com.czur.starry.device.starrypad.devinfo.WPDevInfo
import com.czur.starry.device.starrypad.devinfo.WPDeviceInfoManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

/**
 * Created by 陈丰尧 on 2023/12/4
 */
private const val TAG = "WritePadViewModel"

private data class QRCodeInfo(val url: String, val md5: String)

private val qrCodeInfo = QRCodeInfo(
    "https://c.czur.com/m/a/emNb94E5up", "2d0f08fa31a1a784be974d969939e76e"
)
private val qrCodeInfoOversea =
    QRCodeInfo("https://c.czur.com/m/a/h9EvTktXoh", "cad73c0f27e901632af1b73502899fbc")

class WritePadDevInfoViewModel(application: Application) : AndroidViewModel(application) {

    // 手写板设备列表
    val writePadInfoListFlow = WPDeviceInfoManager.devInfoListFlow
        .map { devList ->
            // 排序:
            // 1. 先已连接,再未连接
            // 2. 相同连接状态下, 按名称排序
            devList.sortedWith(compareByDescending<WPDevInfo> { it.connected }.thenBy { it.name })
        }


    suspend fun refreshWritePadInfoList() {
        WPDeviceInfoManager.refreshAllDeviceInfo()
    }

    /**
     * 获取升级信息
     */
    suspend fun getReleaseNote(targetVersion: String): Spanned =
        WPDeviceInfoManager.getReleaseNote(targetVersion)

    /**
     * 获取购买手写板的二维码
     */
    suspend fun getBuyQrCode(): Bitmap {
        return withContext(Dispatchers.Default) {
            val info = when (Constants.starryHWInfo.salesLocale) {
                StarryDevLocale.Overseas -> qrCodeInfoOversea
                else -> qrCodeInfo
            }

            // 从缓存中读取 大概耗时4ms
            val cacheFile = File(globalAppCtx.cacheDir, info.md5)
            if (cacheFile.exists()) {
                runCatching {
                    BitmapFactory.decodeFile(cacheFile.absolutePath)
                }.onSuccess {
                    return@withContext it
                }.onFailure {
                    logTagW(TAG, "解析二维码失败", tr = it)
                }
            }

            // 生成二维码 大概耗时90ms
            val bitmap = QrCodeUtil.generateQrCodeBmp(info.url) {
                edge = 150
                bgColor = 0xFFE7E7E7.toInt()
                pointColor = 0xFF3D3D3D.toInt()
                logoConfig {
                    logoRes = R.drawable.img_qr_logo_buy
                }
            }
            launch {
                bitmap.saveToFile(cacheFile, format = Bitmap.CompressFormat.PNG)
            }
            return@withContext bitmap
        }
    }
}