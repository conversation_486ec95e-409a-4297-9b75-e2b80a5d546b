package com.czur.starry.device.starrypad.hardware.trans.bt

import android.annotation.SuppressLint
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.bluetooth.BluetoothServerSocket
import android.bluetooth.BluetoothSocket
import android.content.Context
import android.content.IntentFilter
import com.czur.czurutils.extension.num.toByteArray
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.KEY_ESHARE_PWD
import com.czur.starry.device.baselib.common.KEY_ESHARE_SSID
import com.czur.starry.device.baselib.utils.doWithoutCatch
import com.czur.starry.device.baselib.utils.prop.getSystemProp
import com.czur.starry.device.starrypad.devinfo.WPDeviceInfoManager
import com.czur.starry.device.starrypad.receiver.BTBoundChangeReceiver
import com.czur.starry.writepadlib.common.WPBTConstant
import com.czur.starry.writepadlib.proto.WPTransferData.StarryWifiInfo
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.IOException
import kotlin.time.Duration.Companion.seconds

/**
 * Created by 陈丰尧 on 2023/11/21
 */
@SuppressLint("MissingPermission")
class BTServer {
    companion object {
        const val TAG = "BTServer"
    }

    private val retryDelay = 2.seconds

    private val btBondChangeReceiver = BTBoundChangeReceiver()
    var onUserDeUnbindListener
        get() = btBondChangeReceiver.onUserDeUnbindListener
        set(value) {
            btBondChangeReceiver.onUserDeUnbindListener = value
        }

    private val scope = CoroutineScope(Dispatchers.IO)
    private val btManager = globalAppCtx.getSystemService(BluetoothManager::class.java)
    private val btAdapter = btManager.adapter
    private var serverSocket: BluetoothServerSocket? = null

    private var shouldLoop = true

    private var wifiInfo: StarryWifiInfo? = null


    fun startListener() {
        if (btAdapter == null || !btAdapter.isEnabled) {
            logTagE(TAG, "蓝牙不可用或未启用。")
            btAdapter.enable()
            scope.launch {
                delay(retryDelay)
                if (shouldLoop) {
                    logTagD(TAG, "重新启动蓝牙Server服务")
                    startListener()
                }
            }
            return
        }
        try {
            serverSocket = btAdapter.listenUsingInsecureRfcommWithServiceRecord(
                WPBTConstant.NAME,
                WPBTConstant.transUUID
            )
        } catch (e: IOException) {
            logTagE(TAG, "无法启动服务器: ${e.message}")
            scope.launch {
                delay(retryDelay)
                if (shouldLoop) {
                    logTagD(TAG, "重新启动蓝牙Server服务")
                    startListener()
                }
            }
            return
        }

        scope.launch {
            createServerSocket()

            logTagD(TAG, "开始蓝牙Server服务")
            while (shouldLoop) {
                logTagD(TAG, "等待下一个连接")
                try {
                    val socket = serverSocket?.accept() ?: break
                    transInfo2ClientAndClose(socket)
                } catch (e: Throwable) {
                    logTagE(TAG, "accept异常: ${e.message}", tr = e)
                    e.printStackTrace()
                    delay(retryDelay)
                    break
                }
            }
            logTagD(TAG, "退出循环, 关闭SocketServer")
            serverSocket?.close()
            if (shouldLoop) {
                logTagD(TAG, "重新启动蓝牙Server服务")
                delay(retryDelay) // 防止close和start发生资源争抢，导致crash。
                startListener()
            }
        }
    }

    /**
     * 创建ServerSocket
     */
    private suspend fun createServerSocket() {
        withContext(Dispatchers.IO) {
            while (serverSocket == null && shouldLoop && isActive) {
                try {
                    serverSocket = btAdapter.listenUsingInsecureRfcommWithServiceRecord(
                        WPBTConstant.NAME,
                        WPBTConstant.transUUID
                    )
                } catch (e: Throwable) {
                    logTagE(TAG, "listenUsingInsecureRfcommWithServiceRecord异常", tr = e)
                    delay(5.seconds)
                }
            }
        }
    }

    private suspend fun transInfo2ClientAndClose(clientSocket: BluetoothSocket) =
        withContext(Dispatchers.IO) {
            try {
                logTagD(
                    TAG,
                    "接入成功:${clientSocket.remoteDevice.name} - ${clientSocket.remoteDevice.address}"
                )
            } catch (e: Exception) {
                logTagE(TAG, "传输出错: ${e.message}")
            }
            val transferInfo = getWifiInfo().toByteArray() ?: byteArrayOf()
            delay(200)
            clientSocket.outputStream.let {
                val sizeBuf = transferInfo.size.toByteArray()
                it.write(sizeBuf)
                logTagD(TAG, "写入size:${transferInfo.size}")
                it.write(transferInfo)
                it.flush()
                it.close()
            }
            launch {
                delay(5.seconds)
                logTagV(TAG, "关闭SocketClient")
                doWithoutCatch {
                    clientSocket.close()
                }
            }
            logTagI(TAG, "传输完成")
        }

    private suspend fun getWifiInfo(): StarryWifiInfo {
        wifiInfo?.let {
            return it
        }
        val ssid = getSystemProp(KEY_ESHARE_SSID, "")
        val pwd = getSystemProp(KEY_ESHARE_PWD, "")

        logTagV(TAG, "getStarryWifiInfo ssid: $ssid, pwd: $pwd")

        return StarryWifiInfo.newBuilder()
            .setSsid(ssid)
            .setPassword(pwd)
            .build().also {
                wifiInfo = it
            }
    }

    fun stop() {
        logTagD(TAG, "停止蓝牙Server服务")
        shouldLoop = false
        doWithoutCatch {
            serverSocket?.close()
        }
    }

    fun registerBTBondChangeReceiver(context: Context) {
        val intentFilter = IntentFilter()
        intentFilter.addAction(BluetoothDevice.ACTION_BOND_STATE_CHANGED)
        context.registerReceiver(btBondChangeReceiver, intentFilter)
    }

    fun unregisterBTBondChangeReceiver(context: Context) {
        context.unregisterReceiver(btBondChangeReceiver)
    }
}