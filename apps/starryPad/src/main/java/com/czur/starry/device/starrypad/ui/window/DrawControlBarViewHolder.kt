package com.czur.starry.device.starrypad.ui.window

import android.annotation.SuppressLint
import android.content.Intent
import androidx.lifecycle.LifecycleService
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagI
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_VOICE_ASSISTANT
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.keyboard.injectKeyAPPSwitch
import com.czur.starry.device.baselib.utils.keyboard.injectKeyBack
import com.czur.starry.device.baselib.utils.keyboard.injectKeyHome
import com.czur.starry.device.baselib.utils.repeatCollectOnStart
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.starrypad.R
import com.czur.starry.device.starrypad.databinding.ViewHolderDrawControlBarBinding
import com.czur.starry.device.starrypad.vm.PaletteViewModel
import com.czur.starry.writepadlib.proto.WPTransferData
import com.czur.starry.writepadlib.proto.WPTransferData.BizBtnType
import com.czur.starry.writepadlib.proto.WPTransferData.Mode
import com.czur.starry.writepadlib.proto.WPTransferData.WPPaintColor
import com.czur.starry.writepadlib.util.getStarryColorValue
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * Created by 陈丰尧 on 2025/3/17
 */
private const val TAG = "DrawControlBarViewHolder"

class DrawControlBarViewHolder(
    val binding: ViewHolderDrawControlBarBinding,
    val scope: CoroutineScope,
    val cb: IControlBarCb,
    val paletteViewMode: PaletteViewModel,
    val mode: Mode
) : CoroutineScope by scope {
    private val colorSelIndexFlow = MutableStateFlow(0)
    private val colorSheetList by lazy {
        listOf(
            binding.color1,
            binding.color2,
            binding.color3,
            binding.color4,
        )
    }

    val currentColor: WPPaintColor
        get() = when (colorSelIndexFlow.value) {
            0 -> WPPaintColor.BLACK
            1 -> WPPaintColor.RED
            2 -> WPPaintColor.GREEN
            3 -> WPPaintColor.BLUE
            else -> WPPaintColor.BLACK
        }

    val penStateFlow = MutableStateFlow(WPTransferData.DrawEventType.DRAW_EVENT_TYPE_PEN)
    val penState
        get() = penStateFlow.value


    fun initViews() = binding.apply {
        // 1. Android系统按键
        backIv.setOnDebounceClickListener {
            logTagI(TAG, "back")
            launch {
                injectKeyBack()
            }
        }
        // home键
        homeIv.setOnDebounceClickListener {
            logTagI(TAG, "home")
            launch {
                injectKeyHome()
            }
        }
        // APPSwitch
        recentIv.setOnDebounceClickListener {
            logTagI(TAG, "APPSwitch")
            launch {
                injectKeyAPPSwitch()
            }
        }

        // 系统按键
        voiceAssistantIv.setOnDebounceClickListener {
            logTagI(TAG, "语音助手")
            val intent = Intent().apply {
                `package` = "com.czur.starry.device.voiceassistant"
                action = ACTION_BOOT_VOICE_ASSISTANT
                putExtra("key_message", "wake_up")
            }
            globalAppCtx.startService(intent)
        }
        laserPenIv.setOnDebounceClickListener {
            logTagI(TAG, "激光笔")
        }

        // 业务按钮
        exitIv.setOnDebounceClickListener {
            cb.sendLocalEvent(BizBtnType.BIZ_BTN_EXIT)
        }
        // 保存按钮
        saveIv.setOnDebounceClickListener {
            cb.sendLocalEvent(BizBtnType.BIZ_BTN_SAVE)
        }
        // 新建按钮
        newIv.gone(mode == Mode.MODE_MARK)
        newIv.setOnDebounceClickListener {
            cb.sendLocalEvent(BizBtnType.BIZ_BTN_NEW)
        }

        // 翻页按钮 是否显示
        pageGroup.gone(mode == Mode.MODE_MARK)
        pagePreClickView.setOnDebounceClickListener {
            logTagI(TAG, "上一页")
            cb.sendLocalEvent(BizBtnType.BIZ_BTN_PAGE_PRE)
        }
        pageNextClickView.setOnDebounceClickListener {
            logTagI(TAG, "下一页")
            cb.sendLocalEvent(BizBtnType.BIZ_BTN_PAGE_NEXT)
        }

        // 颜色选择
        color1.showColor = getStarryColorValue(WPPaintColor.BLACK)
        color2.showColor = getStarryColorValue(WPPaintColor.RED)
        color3.showColor = getStarryColorValue(WPPaintColor.GREEN)
        color4.showColor = getStarryColorValue(WPPaintColor.BLUE)

        colorSheetList.forEachIndexed { index, view ->
            view.setOnDebounceClickListener {
                logTagI(TAG, "颜色选择: $index")
                colorSelIndexFlow.value = index
            }
        }

        // 撤销
        unDoIv.setOnDebounceClickListener {
            logTagI(TAG, "撤销")
            cb.sendLocalEvent(BizBtnType.BIZ_BTN_UNDO)
        }
        // 重做
        reDoIv.setOnDebounceClickListener {
            logTagI(TAG, "重做")
            cb.sendLocalEvent(BizBtnType.BIZ_BTN_REDO)
        }
        // 删除
        delIv.setOnDebounceClickListener {
            logTagI(TAG, "删除")
            penStateFlow.value = WPTransferData.DrawEventType.DRAW_EVENT_TYPE_LINE_ERASER
        }
        penIv.setOnDebounceClickListener {
            logTagI(TAG, "画笔")
            penStateFlow.value = WPTransferData.DrawEventType.DRAW_EVENT_TYPE_PEN
        }
        // 清屏
        clearIv.setOnDebounceClickListener {
            logTagI(TAG, "清屏")
            paletteViewMode.mkCleanScreenUserInput()
        }
    }

    @SuppressLint("SetTextI18n")
    fun initData(service: LifecycleService) = service.apply {
        repeatCollectOnStart(paletteViewMode.pageInfoFlow) { (current, total) ->
            logTagI(TAG, "页码信息: $current/$total")
            binding.controlBarPageNumCurrentTv.text = "$current"
            binding.controlBarPageNumTotalTv.text = "$total"
        }

        repeatCollectOnStart(colorSelIndexFlow) {
            logTagI(TAG, "颜色选择: $it")
            colorSheetList.forEachIndexed { index, view ->
                view.isSelected = index == it
            }
        }

        repeatCollectOnStart(penStateFlow) {
            if (it == WPTransferData.DrawEventType.DRAW_EVENT_TYPE_LINE_ERASER) {
                binding.delIv.setImageResource(R.drawable.ic_control_bar_del_sel)
                binding.penIv.setImageResource(R.drawable.ic_control_bar_pen)
            } else {
                binding.delIv.setImageResource(R.drawable.ic_control_bar_del)
                binding.penIv.setImageResource(R.drawable.ic_control_bar_pen_sel)
            }
        }
    }
}