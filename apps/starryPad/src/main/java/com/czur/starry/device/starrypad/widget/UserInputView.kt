package com.czur.starry.device.starrypad.widget

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.VelocityTracker
import android.view.View
import com.czur.starry.writepadlib.common.PenAttitude
import com.czur.starry.writepadlib.common.UIConstant
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlin.math.pow

/**
 * Created by 陈丰尧 on 2023/10/30
 * 用来接收用户输入事件的View
 */
private const val TAG = "UserInputView"

class UserInputView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    // x,y,p,action
    private val _rawPointFlow = MutableSharedFlow<UserInputValue>(
        extraBufferCapacity = 8,
        onBufferOverflow = BufferOverflow.DROP_OLDEST
    )
    val userPointFlow = _rawPointFlow
        .distinctUntilChanged { old, new ->
            (old.action == MotionEvent.ACTION_UP && new.action == MotionEvent.ACTION_UP)   // 连续两个抬起事件也不再重复发送
                    || (new.action == MotionEvent.ACTION_MOVE && old.x == new.x && old.y == new.y && new.p <= old.p) // 位置相同,但是后面的压力小也不用发送, 因为看不见这个点
        }.map {
            UserInputValue(
                it.x - offsetX,
                it.y - offsetY,
                it.p,
                it.type,
                it.action
            )
        }

    private var lastBizAction = -1

    private var zoom = 0
    private var zoomFloor = 0
    private var offsetX = 0F
    private var offsetY = 0F
    private var boardWidth = 0
    private var boardHeight = 0

    // 当前画板的宽度
    private val currentBoardWidth: Float
        get() = boardWidth.toFloat() / (zoom / zoomFloor)

    private val scale: Float
        get() = currentBoardWidth / width

    private val _clickDownFlow = MutableStateFlow(false)
    val clickDownFlow = _clickDownFlow.asStateFlow()

    // 笔的姿态
    private var _penAttitudeFlow = MutableStateFlow(PenAttitude.NIB)
    val penAttitudeFlow = _penAttitudeFlow.asStateFlow()

    private val velocityTracker: VelocityTracker by lazy(LazyThreadSafetyMode.NONE) {
        VelocityTracker.obtain()
    }

    // 尾部变细功能是否开启
    var endTaperingEnable = UIConstant.END_TAPERING_ENABLE
    var endTaperingCoefficient = UIConstant.END_TAPERING_COEFFICIENT
    private var isBlockUserInput = false
    private var blockUserInputHandlerToken = Any()

    fun initBoardInfo(
        zoom: Int,
        zoomFloor: Int,
        offsetX: Float,
        offsetY: Float,
        width: Int,
        height: Int
    ) {
        this.zoom = zoom
        this.zoomFloor = zoomFloor
        this.offsetX = offsetX
        this.offsetY = offsetY
        this.boardWidth = width
        this.boardHeight = height
    }

    fun forceUseZoomAndDrag(zoomValue: Int, transX: Float, transY: Float) {
        this.zoom = zoomValue
        this.offsetX = transX
        this.offsetY = transY
    }

    /**
     * 阻塞用户输入
     */
    fun blockUserInput(timeout: Long) {
        handler.removeCallbacksAndMessages(blockUserInputHandlerToken)
        isBlockUserInput = true
        handler.postDelayed({
            isBlockUserInput = false
        }, blockUserInputHandlerToken, timeout)
    }


    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent): Boolean {
        if (isBlockUserInput) {
            return true
        }

        var toolType = event.getToolType(0)

        if (toolType != MotionEvent.TOOL_TYPE_STYLUS && toolType != MotionEvent.TOOL_TYPE_ERASER) {
            return true // 屏蔽掉非笔和橡皮擦的事件
        }
        velocityTracker.addMovement(event)

        // 处理点击事件
        if (event.action == MotionEvent.ACTION_DOWN) {
            _clickDownFlow.tryEmit(true)
        } else if (event.action == MotionEvent.ACTION_UP || event.action == MotionEvent.ACTION_CANCEL) {
            _clickDownFlow.tryEmit(false)
        }


        // 不需要处理多点触控, 只需要处理单点触控即可
        val x = event.x * scale   // 相对于全尺寸的底图来说的坐标
        val y = event.y * scale


        if ((lastBizAction == MotionEvent.ACTION_MOVE || lastBizAction == MotionEvent.ACTION_DOWN)
            && x < 0 || y < 0 || x > boardWidth || y > boardHeight
        ) {
            // 屏蔽掉超出范围的坐标
            _rawPointFlow.tryEmit(
                UserInputValue(
                    x.coerceIn(0F, boardWidth.toFloat()),
                    y.coerceIn(0F, boardHeight.toFloat()),
                    0F,
                    toolType,
                    MotionEvent.ACTION_UP
                )
            )
            lastBizAction = MotionEvent.ACTION_UP
            return true
        }
        val p = if (event.action == MotionEvent.ACTION_UP ||
            event.action == MotionEvent.ACTION_CANCEL
        ) {
            event.pressure
        } else {
            event.pressure
        }

        val action = if (event.action == MotionEvent.ACTION_CANCEL) {
            MotionEvent.ACTION_UP
        } else if (event.action == MotionEvent.ACTION_MOVE && lastBizAction == MotionEvent.ACTION_UP) {
            // 处理之前补充了超出范围的Up事件
            MotionEvent.ACTION_DOWN
        } else {
            event.action
        }

        if (endTaperingEnable) {
            sendEndTaperingEvent(action, x, y, p, toolType)
        } else {
            _rawPointFlow.tryEmit(
                UserInputValue(
                    x,
                    y,
                    p,
                    toolType,
                    action
                )
            )
        }
        lastBizAction = action

        return true
    }

    private fun sendEndTaperingEvent(
        action: Int,
        x: Float,
        y: Float,
        p: Float,
        toolType: Int
    ) {
        val sendAction = if (action == MotionEvent.ACTION_UP) {
            MotionEvent.ACTION_MOVE
        } else {
            action
        }
        _rawPointFlow.tryEmit(
            UserInputValue(
                x,
                y,
                p,
                toolType,
                sendAction
            )
        )
        if (action == MotionEvent.ACTION_UP) {
            velocityTracker.computeCurrentVelocity(200)
            val xVP = velocityTracker.xVelocity * p * endTaperingCoefficient
            val yVP = velocityTracker.yVelocity * p * endTaperingCoefficient

            val ax = x + xVP
            val ay = y + yVP
            velocityTracker.clear()
            _rawPointFlow.tryEmit(
                UserInputValue(
                    ax,
                    ay,
                    0.01F,
                    toolType,
                    MotionEvent.ACTION_UP
                )
            )
        }
    }
}

@JvmInline
value class UserInputValue(
    private val values: FloatArray = floatArrayOf(0F, 0F, 0F, 0F, 0F)
) {
    constructor(x: Float, y: Float, p: Float, type: Int, action: Int) : this(
        floatArrayOf(
            x.keepDecimals(), y.keepDecimals(), p.keepDecimals(3),
            type.toFloat(),
            action.toFloat()
        )
    )

    var x: Float
        set(value) {
            values[0] = value
        }
        get() = values[0]

    var y: Float
        set(value) {
            values[1] = value
        }
        get() = values[1]

    var p: Float
        set(value) {
            values[2] = value
        }
        get() = values[2]

    var type: Int
        set(value) {
            values[3] = value.toFloat()
        }
        get() = values[3].toInt()

    var action: Int
        set(value) {
            values[4] = value.toFloat()
        }
        get() = values[4].toInt()

    val isErase: Boolean
        get() = type != MotionEvent.TOOL_TYPE_STYLUS

    override fun toString(): String = "($x, $y, $p, $type, $action)"

}

private fun Float.keepDecimals(reservedDigits: Int = 1): Float {
    val scale = 10.0.pow(reservedDigits)
    return (this * scale).toInt() / scale.toFloat()
}
