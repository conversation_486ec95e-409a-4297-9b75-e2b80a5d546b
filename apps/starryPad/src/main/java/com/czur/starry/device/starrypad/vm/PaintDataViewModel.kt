package com.czur.starry.device.starrypad.vm

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.asFlow
import androidx.lifecycle.asLiveData
import androidx.lifecycle.map
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.baselib.utils.FlowMediatorLiveData
import com.czur.starry.device.baselib.utils.data.LiveDataDelegate
import com.czur.starry.device.baselib.utils.data.ReadOnlyLiveDataDelegate
import com.czur.starry.device.baselib.utils.getTimeStr
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.starrypad.db.entity.AlbumEntity
import com.czur.starry.device.starrypad.repository.PaintRepository
import com.czur.starry.device.starrypad.ui.adapter.ALBUM_VO_TYPE_DATA
import com.czur.starry.device.starrypad.ui.adapter.ALBUM_VO_TYPE_TITLE
import com.czur.starry.device.starrypad.ui.adapter.AlbumDataVO
import com.czur.starry.device.starrypad.ui.adapter.CREATE_TIME_BY_DAY_RECENT
import com.czur.starry.device.starrypad.ui.adapter.PaintTimePickerVO
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map

/**
 * Created by 陈丰尧 on 2022/4/27
 */
@OptIn(FlowPreview::class)
class PaintDataViewModel(application: Application) : AndroidViewModel(application) {
    companion object {
        private const val DATE_PATTERN = "yyyy.MM.dd"
        private const val DATE_PATTERN_PAINT = "MM.dd HH:mm:ss"
    }

    /**
     * 显示模式
     */
    enum class DisplayMode {
        BROWSE,     // 普通浏览模式
        DEL,        // 删除选择模式
        SHARE,       // 分享选择模式
        SAVE_LOCAL, // 保存到本地
    }

    /**
     * 数据库来的数据
     */
    private val paintDataLive = PaintRepository.albumDataLive

    // 最近数据
    private val recentPaintDataLive = PaintRepository.recentAlbumDataLive
    val hasPaintDataLive = paintDataLive.asFlow().map {
        it.isNotEmpty()
    }.distinctUntilChanged()

    private val mkPaintVOFlow = MutableStateFlow(System.currentTimeMillis())
    private val mkTimePickerFlow = MutableStateFlow(System.currentTimeMillis())

    // 显示数据
    val albumDataVOLive = FlowMediatorLiveData<List<AlbumDataVO>>(mkPaintVOFlow)

    // 日期标签
    val paintTimeLive = FlowMediatorLiveData<List<PaintTimePickerVO>>(mkTimePickerFlow)

    /**
     * 显示模式
     */
    private val _displayModeFlow = MutableStateFlow(DisplayMode.BROWSE)
    val displayModeFlow = _displayModeFlow.asStateFlow()
    var displayMode: DisplayMode
        get() = _displayModeFlow.value
        set(value) {
            _displayModeFlow.value = value
        }

    private val currentShowTimeStrLive = DifferentLiveData(CREATE_TIME_BY_DAY_RECENT)
    private var currentShowTimeStr by LiveDataDelegate(currentShowTimeStrLive)

    /**
     * 选中数量
     */
    val selCountLive = DifferentLiveData(0)
    var selCount by LiveDataDelegate(selCountLive)

    /**
     * 是否是已经全选上了
     */
    val isSelectAllLive = selCountLive.map {
        it >= (paintDataLive.value?.size ?: 0)
    }
    val isSelectAll: Boolean by ReadOnlyLiveDataDelegate(isSelectAllLive)


    init {
        albumDataVOLive.addSource(recentPaintDataLive, paintDataLive, displayModeFlow.asLiveData())
        paintTimeLive.addSource(albumDataVOLive, currentShowTimeStrLive)

        launch(Dispatchers.Default) {
            displayModeFlow.collect {
                if (it == DisplayMode.BROWSE) {
                    selCount = 0
                }
            }
        }

        launch(Dispatchers.IO) {
            mkPaintVOFlow.debounce(50).collect {
                createPaintVOFlow()
            }
        }

        launch(Dispatchers.IO) {
            mkTimePickerFlow.debounce(50).collect {
                createPaintTimeList()
            }
        }
    }

    /**
     * 更新日期选择列表
     */
    private fun createPaintTimeList() {
        val allPaintData = albumDataVOLive.value ?: emptyList()
        if (allPaintData.isEmpty()) {
            paintTimeLive.postValue(emptyList())
            return
        }

        val timeList = mutableListOf<PaintTimePickerVO>()
        var selTimeStr = currentShowTimeStr
        var currentTimeStr = ""
        var currentTimeCount = 0
        var currentTimeIndex = 0
        allPaintData.forEachIndexed { index, paintDataVO ->
            if (paintDataVO.type == ALBUM_VO_TYPE_TITLE) {
                if (selTimeStr == CREATE_TIME_BY_DAY_RECENT) {
                    // 如果当前是最近在上面, 则默认选中第一个
                    selTimeStr = paintDataVO.createTimeByDay
                }
                // 一天变量完成后, 将这一天的数据添加
                if (currentTimeStr.isNotEmpty()) {
                    timeList.add(
                        PaintTimePickerVO(
                            currentTimeStr,
                            currentTimeCount,
                            currentTimeIndex,
                            currentTimeStr == selTimeStr
                        )
                    )
                }

                currentTimeCount = 0
                currentTimeIndex = index
                currentTimeStr = paintDataVO.createTimeByDay
            } else {
                currentTimeCount++
            }
        }

        // 添加最后一天的数据
        timeList.add(
            PaintTimePickerVO(
                currentTimeStr,
                currentTimeCount,
                currentTimeIndex,
                currentTimeStr == selTimeStr
            )
        )

        paintTimeLive.postValue(timeList)

    }

    private fun createPaintVOFlow() {
        val recentList = recentPaintDataLive.value ?: emptyList()
        if (recentList.isEmpty()) {
            albumDataVOLive.postValue(emptyList())
            return
        }
        val allPaintData = paintDataLive.value ?: emptyList()
        if (allPaintData.isEmpty()) {
            albumDataVOLive.postValue(emptyList())
            return
        }
        // 添加最近使用数据
        val voList = mutableListOf<AlbumDataVO>()
        voList.add(AlbumDataVO.mkRecentTitle())

        var currentTimeStr = ""

        recentList.forEach {
            val timeStr = getTimeStr(DATE_PATTERN_PAINT, it.album.createTime)
            val vo = AlbumDataVO(ALBUM_VO_TYPE_DATA, timeStr, CREATE_TIME_BY_DAY_RECENT, it)
            voList.add(vo)
        }
        val timeSet = mutableSetOf<String>()
        // 添加全部数据
        allPaintData.forEach {
            val timeStr = getTimeStr(DATE_PATTERN, it.album.createTime)
            val paintTimeStr = getTimeStr(DATE_PATTERN_PAINT, it.album.createTime)
            if (timeStr !in timeSet) {
                // 添加标题
                voList.add(AlbumDataVO.mkTitle(timeStr))
                timeSet.add(timeStr)
                currentTimeStr = timeStr
            }
            // 添加数据
            val vo = AlbumDataVO(ALBUM_VO_TYPE_DATA, paintTimeStr, currentTimeStr, it)
            voList.add(vo)
        }


        // 更新显示
        albumDataVOLive.postValue(voList)

    }

    /**
     * 更新当前显示的画面
     */
    fun updateFirstVisibleItemPaintData(albumDataVO: AlbumDataVO) {
        currentShowTimeStr = albumDataVO.createTimeByDay
    }

    /**
     * 删除选中相册
     */
    suspend fun delAlbum(paintEntities: List<AlbumEntity>) {
        PaintRepository.delAlbums(*paintEntities.toTypedArray())
    }
}