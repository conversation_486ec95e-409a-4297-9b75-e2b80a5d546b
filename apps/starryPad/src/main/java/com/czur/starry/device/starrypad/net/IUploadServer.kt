package com.czur.starry.device.starrypad.net

import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.network.core.MiaoHttpEntity
import com.czur.starry.device.baselib.network.core.MiaoHttpGet
import com.czur.starry.device.baselib.network.core.MiaoHttpParam
import com.czur.starry.device.baselib.network.core.MiaoHttpPost

/**
 * Created by 陈丰尧 on 2022/5/10
 */
interface IUploadServer {
    @MiaoHttpGet("/api/starry/share/getOssTempToken")
    fun getToken(
        @MiaoHttpParam("sn") sn: String = Constants.SERIAL,
        clazz: Class<OSSTokenEntity> = OSSTokenEntity::class.java
    ): MiaoHttpEntity<OSSTokenEntity>

    @MiaoHttpPost("/api/starry/share/shareImageCode")
    fun getQRCodeUrl(
        @MiaoHttpParam("directory") directory: String,
        @MiaoHttpParam("sn") sn: String = Constants.SERIAL,
        clazz: Class<String> = String::class.java
    ): MiaoHttpEntity<String>
}