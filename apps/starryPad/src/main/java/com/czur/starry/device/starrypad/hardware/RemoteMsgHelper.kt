package com.czur.starry.device.starrypad.hardware

import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.starrypad.common.SERVER_DEV_ID
import com.czur.starry.device.starrypad.common.WPFuncOption
import com.czur.starry.device.starrypad.devinfo.WPDevInfo
import com.czur.starry.device.starrypad.hardware.trans.netty.INettyMsgSender
import com.czur.starry.writepadlib.proto.WPTransferData
import com.czur.starry.writepadlib.proto.WPTransferData.BizData
import com.czur.starry.writepadlib.proto.WPTransferData.ModeChangeReq
import com.czur.starry.writepadlib.proto.WPTransferData.NettyMessage
import com.czur.starry.writepadlib.proto.WPTransferData.UpgradeInfo
import com.czur.starry.writepadlib.util.AbsProtoBuilder
import com.czur.starry.writepadlib.util.devID
import com.google.common.util.concurrent.ThreadFactoryBuilder
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.selects.whileSelect
import java.util.concurrent.Executor
import java.util.concurrent.Executors

/**
 * Created by 陈丰尧 on 2023/12/21
 * 用来处理远端业务消息的
 */
object ServerProtoBuilder : AbsProtoBuilder() {
    override val devID: String
        get() = SERVER_DEV_ID // Server固定是StarryHub
}

interface IRemoteMsgHelper : INettyMsgSender {
    val remoteBizDataFlow: Flow<BizData>
    val remoteModeChangeReqFlow: Flow<ModeChangeReq>
    val clientInfoFlow: Flow<WPDevInfo>
    val remoteUpgradeInfoFlow: Flow<UpgradeInfo>
    val remoteProcessControlFlow: Flow<WPTransferData.ProcessControl>

    var nettySender: INettyMsgSender?

    fun onRemoteMsgReceived(msg: NettyMessage)

    /**
     * 远端客户端离线
     */
    fun onClintOffline(devID: String)

    suspend fun injectLocalMsg(msg: BizData)
}

class RemoteMsgHelper : IRemoteMsgHelper, CoroutineScope by MainScope() {
    companion object {
        private const val TAG = "RemoteMsgHelper"
    }

    override var nettySender: INettyMsgSender? = null

    private val remoteMsgChannel: Channel<NettyMessage> =
        Channel(4096 * WPFuncOption.FUNC_MULTIPLE_DEVICE_COUNT, onBufferOverflow = BufferOverflow.DROP_OLDEST)

    // 远端业务数据
    private val _remoteBizDataFlow = MutableSharedFlow<BizData>(
        extraBufferCapacity = 1024 * WPFuncOption.FUNC_MULTIPLE_DEVICE_COUNT,
        onBufferOverflow = BufferOverflow.DROP_OLDEST
    )
    override val remoteBizDataFlow = _remoteBizDataFlow.asSharedFlow()

    // 远端请求切换模式
    private val _remoteModeChangeReqFlow = MutableSharedFlow<ModeChangeReq>(
        extraBufferCapacity = 2,
        onBufferOverflow = BufferOverflow.DROP_OLDEST
    )
    override val remoteModeChangeReqFlow = _remoteModeChangeReqFlow.asSharedFlow()

    /**
     * 远端客户端信息改变
     */
    private val _clientInfoFlow = MutableSharedFlow<WPDevInfo>(
        extraBufferCapacity = 4 * WPFuncOption.FUNC_MULTIPLE_DEVICE_COUNT,
        onBufferOverflow = BufferOverflow.DROP_OLDEST
    )
    override val clientInfoFlow = _clientInfoFlow.asSharedFlow().distinctUntilChanged()

    /**
     * 远端升级相关的信息
     */
    private val _remoteUpgradeInfoFlow = MutableSharedFlow<UpgradeInfo>(
        extraBufferCapacity = 2 * WPFuncOption.FUNC_MULTIPLE_DEVICE_COUNT,
        onBufferOverflow = BufferOverflow.DROP_OLDEST
    )
    override val remoteUpgradeInfoFlow = _remoteUpgradeInfoFlow.asSharedFlow()

    /**
     * 远端控制信息
     */
    private val _remoteProcessControlFlow = MutableSharedFlow<WPTransferData.ProcessControl>(
        extraBufferCapacity = 1 * WPFuncOption.FUNC_MULTIPLE_DEVICE_COUNT,
        onBufferOverflow = BufferOverflow.DROP_OLDEST
    )
    override val remoteProcessControlFlow = _remoteProcessControlFlow.asSharedFlow()

    // 单独放到一个线程中处理, 避免阻塞,影响后续的消息处理
    private val msgThreadFactory = ThreadFactoryBuilder()
        .setPriority(Thread.MAX_PRIORITY)
        .setNameFormat("RemoteMsgHelper-%d")
        .build()
    private val msgDispatcher = Executors.newSingleThreadExecutor(msgThreadFactory).asCoroutineDispatcher()

    init {
        launch(msgDispatcher) {
            while (isActive) {
                val msg = remoteMsgChannel.receive()
                // 将Message发送到不同的Flow中
                when (msg.dataBodyCase) {
                    NettyMessage.DataBodyCase.MODE_CHANGE_REQ -> {
                        _remoteModeChangeReqFlow.emit(msg.modeChangeReq)    // 远端请求切换模式
                    }

                    NettyMessage.DataBodyCase.BIZ_DATA -> {
                        _remoteBizDataFlow.emit(msg.bizData)    // bizData
                    }

                    NettyMessage.DataBodyCase.CLIENT_INFO -> {
                        val wpDevInfo = WPDevInfo(
                            msg.clientInfo.devID,
                            msg.clientInfo.version,
                            msg.clientInfo.battery,
                            screenOn = msg.clientInfo.screenOn,
                            isClientOTARunning = msg.clientInfo.isOta,
                            connected = true,
                            msg.devID,
                        )
                        _clientInfoFlow.emit(wpDevInfo)    // clientInfo
                    }

                    NettyMessage.DataBodyCase.UPGRADE_INFO -> {
                        _remoteUpgradeInfoFlow.emit(msg.upgradeInfo)    // upgradeInfo
                    }

                    NettyMessage.DataBodyCase.PROCESS_CONTROL -> {
                        _remoteProcessControlFlow.emit(msg.processControl)    // processControl
                    }

                    else -> {
                        logTagV(TAG, "不关心业务类型: ${msg.dataBodyCase}")
                    }
                }
            }
        }
    }

    /**
     * 当远端消息到达
     */
    override fun onRemoteMsgReceived(msg: NettyMessage) {
        remoteMsgChannel.trySend(msg)
    }

    override suspend fun injectLocalMsg(msg: BizData) {
        _remoteBizDataFlow.emit(msg)
    }

    /**
     * 远端客户端离线
     */
    override fun onClintOffline(devID: String) {
        launch(Dispatchers.IO) {
            logTagD(TAG, "远端客户端离线: $devID")
            val wpDevInfo = WPDevInfo(
                name = devID,
                devID = devID,
            )
            _clientInfoFlow.emit(wpDevInfo)
        }
    }

    /**
     * 发送消息, 异步发送
     * @param msg 消息
     * @param devID 设备ID, 如果为null, 则发送给所有的客户端
     */
    override suspend fun sendMsg(msg: NettyMessage, devID: String?) =
        nettySender?.sendMsg(msg, devID) ?: Unit

    /**
     * 发送消息, 同步发送
     * @param msg 消息
     * @param devID 设备ID, 如果为null, 则发送给所有的客户端
     */
    override suspend fun sendMsgSync(msg: NettyMessage, devID: String?) =
        nettySender?.sendMsgSync(msg, devID) ?: Unit
}