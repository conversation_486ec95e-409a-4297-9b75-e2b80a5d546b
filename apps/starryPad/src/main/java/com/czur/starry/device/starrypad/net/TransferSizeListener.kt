package com.czur.starry.device.starrypad.net

/**
 * Created by 陈丰尧 on 2022/5/10
 */
class TransferSizeListener {
    private val sizeMap = mutableMapOf<String, Long>()
    private val totalSizeMap = mutableMapOf<String, Long>()

    @Volatile
    var transferSize: Long = 0L

    val totalSize: Long
        get() = sizeMap.values.sum()

    fun updateSize(ossKey: String, size: Long, totalSize: Long) {
        sizeMap[ossKey] = size
        totalSizeMap[ossKey] = totalSize
        transferSize = sizeMap.values.sum()
    }
}