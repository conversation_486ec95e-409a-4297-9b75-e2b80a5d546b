package com.czur.starry.device.starrypad.vm

import android.app.Application
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.bumptech.glide.Glide
import com.czur.czurutils.extension.RandomStrType
import com.czur.czurutils.extension.nextString
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.utils.basic.yes
import com.czur.starry.device.baselib.utils.collectByConsumerCapacity
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.starrypad.common.BoardBasicInfo
import com.czur.starry.device.starrypad.common.WPDrawConstant
import com.czur.starry.device.starrypad.db.entity.AlbumWithPaintList
import com.czur.starry.device.starrypad.hardware.ServerProtoBuilder
import com.czur.starry.device.starrypad.hardware.WritePadDeviceModeManager
import com.czur.starry.device.starrypad.repository.PageCacheRepository
import com.czur.starry.device.starrypad.repository.PaintRepository
import com.czur.starry.device.starrypad.repository.SettingRepository
import com.czur.starry.device.starrypad.repository.WPDrawRepository
import com.czur.starry.device.starrypad.util.PaletteState
import com.czur.starry.device.starrypad.util.PaletteStateHolder
import com.czur.starry.device.starrypad.util.toBytes
import com.czur.starry.device.starrypad.widget.UserInputValue
import com.czur.starry.writepadlib.common.UIConstant.PAGE_COUNT_MAX
import com.czur.starry.writepadlib.proto.WPTransferData
import com.czur.starry.writepadlib.proto.WPTransferData.BizData.BizDataBodyCase.*
import com.czur.starry.writepadlib.proto.WPTransferData.BizSyncUserInput
import com.czur.starry.writepadlib.proto.WPTransferData.DrawEventType
import com.czur.starry.writepadlib.proto.WPTransferData.WPPaintColor
import com.google.protobuf.ByteString
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.buffer
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterNot
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.shareIn
import kotlinx.coroutines.withContext
import kotlin.random.Random

/**
 * Created by 陈丰尧 on 2022/4/22
 */
private const val TAG = "PaletteViewModel"

const val STEP_INDEX_NONE = -2
const val STEP_INDEX_INIT = -1

class PaletteViewModel(application: Application) : AndroidViewModel(application) {
    private lateinit var serverMode: WPTransferData.Mode
    private var boardID: String = Random.nextString(8, RandomStrType.UPPERCASE)

    private var paintIndex = 0

    // 画板基础信息
    private val _boardInfoFlow: MutableStateFlow<BoardBasicInfo?> = MutableStateFlow(null)
    val boardInfoFlow = _boardInfoFlow.filterNotNull().buffer()
    private val boardInfo: BoardBasicInfo
        get() = _boardInfoFlow.value!!

    var zoom = WPDrawConstant.ZOOM_BASIC
        private set

    private var offsetX: Float = 0F   // 水平偏移量
    private var offsetY: Float = 0F    // 垂直偏移量

    private val _pageInfoFlow = MutableStateFlow(1 to 1)
    val pageInfoFlow = _pageInfoFlow.asStateFlow()

    /**
     * 当前页码 从1开始
     */
    private val pageIndex: Int
        get() = _pageInfoFlow.value.first
    private val pageCount: Int
        get() = _pageInfoFlow.value.second

    private var isCreatingNewPage = false   // 是否正在创建新的页面

    // 翻页信息存储类
    private val pageCacheRepository = PageCacheRepository()

    private val _savedPageDrawBitmapFlow = MutableSharedFlow<Bitmap?>(
        replay = 1
    )
    val savedPageDrawBitmapFlow = _savedPageDrawBitmapFlow.asSharedFlow()

    private val _pageBgBitmapFlow = MutableStateFlow<Bitmap?>(null)
    val pageBgBitmapFlow = _pageBgBitmapFlow.asSharedFlow()
    private val pageBgBitmap: Bitmap?
        get() = _pageBgBitmapFlow.value

    // 保存信息
    private var album: AlbumWithPaintList? = null

    // 已保存的绘画列表
    val savedPaintList
        get() = album?.paintList ?: emptyList()

    private var autoShareEnable = false

    // 是否自动分享, 未书写页面也要允许分享(杨光博)
    val autoShare
        get() = autoShareEnable

    private var isSaving = false    // 是否正在保存

    // 远端的业务信息
    val remoteBizDataFlow = WritePadDeviceModeManager
        .remoteBizDataFlow.filterNot {
            if (isSaving) {
                when (it.bizDataBodyCase) {
                    // 保存中的时候, 可以接收拉取消息
                    BIZ_SYNC -> false
                    PAINT_INFO -> false
                    else -> true
                }
            } else {
                false
            }// 如果正在保存, 则暂停处理其他业务消息
        }.shareIn(viewModelScope, SharingStarted.Eagerly)

    // 远端的用户输入信息
    private val remoteUserInputFlow = remoteBizDataFlow.filter {
        it.hasBizSyncUserInput()
    }.shareIn(viewModelScope, SharingStarted.Eagerly)

    private var currentSaveIndex = 0

    private var bgStep: WPTransferData.PaintStep? = null    // 背景图


    private val drawRepository: WPDrawRepository by lazy {
        WPDrawRepository(viewModelScope)
    }

    // 发送的步骤
    private val stepsFlow = MutableSharedFlow<WPTransferData.PaintStep>(extraBufferCapacity = 1)

    // 绘制步骤流
    val drawStepFlow = drawRepository.drawStepFlow
    private val paletteStateHolder = PaletteStateHolder()

    // 用户输入的Flow
    private val _userInputFlow = MutableSharedFlow<BizSyncUserInput>(
        extraBufferCapacity = Int.MAX_VALUE
    )
    val userInputFlow = _userInputFlow.asSharedFlow()

    init {
        launch {

            stepsFlow
                .collectByConsumerCapacity(viewModelScope)
                .collect {
                    broadcastSteps(it)
                }
        }

        // 处理远端的用户输入
        launch {
            remoteUserInputFlow.collect {
                if (paletteStateHolder.getState() == PaletteState.IDLE) {
                    paletteStateHolder.changeState(PaletteState.DRAWING).yes {
                        paletteStateHolder.rememberDrawing(it.devID)
                    }
                }
                if (paletteStateHolder.getState() == PaletteState.DRAWING) {
                    paletteStateHolder.rememberDrawing(it.devID)
                    drawRepository.onUserInputReceive(it.devID, it.bizSyncUserInput)
                }

            }
        }

        launch {
            drawStepFlow.collect {
                if (it.isFinish) {
                    paletteStateHolder.tryFinishDrawing(it.clientID)
                }
                addPaintStepContentDraw(it) // 同步到远端
            }
        }

        launch {
            autoShareEnable = SettingRepository.shareWhenExit()
            logTagV(TAG, "自动分享: $autoShare")
        }

        launch {
            userInputFlow.collect {
                drawRepository.onUserInputReceive("starry", it)
            }
        }
    }

    /**
     * 通过历史记录来初始化
     */
    suspend fun initByHistory(albumID: Long, currentIndex: Int) = coroutineScope {
        pageCacheRepository.clearCache()    // 清理之前遗留缓存
        val albumWithPaint = PaintRepository.getAlbumsByID(albumID)

        // 复制到缓存中的任务
        val copyDeferred = async {
            pageCacheRepository.copyAlbumToCache(albumWithPaint)
        }

        val currentPaint = albumWithPaint.paintList[currentIndex]
        val drawBmp = withContext(Dispatchers.IO) {
            BitmapFactory.decodeFile(currentPaint.drawImgPath)
        }
        val bgBmp = currentPaint.bgImgPath?.let {
            BitmapFactory.decodeFile(it)
        }
        serverMode = currentPaint.paintMode // 切换到当前模式
        initBgSteps(bgBmp)
        _boardInfoFlow.value = BoardBasicInfo.boardInfo(currentPaint.paintMode, boardID)
        bgBmp?.let {
            logTagV(TAG, "历史记录有背景图")
            _pageBgBitmapFlow.emit(it)
        }
        _pageInfoFlow.value = currentIndex + 1 to albumWithPaint.paintList.size // 页码信息
        _savedPageDrawBitmapFlow.emit(drawBmp)

        updateCurrentSaveIndex()
        WritePadDeviceModeManager.updateServerMode(serverMode)
        album = albumWithPaint

        copyDeferred.await()    // 等待复制完成
        launch {
            Glide.get(getApplication()).apply {
                withContext(Dispatchers.Main) {
                    clearMemory()
                }
                withContext(Dispatchers.IO) {
                    clearDiskCache()
                }
            }
        }
    }

    /**
     * 默认白板信息的初始化
     */
    suspend fun initByDefPalette() {
        pageCacheRepository.clearCache()    // 清理之前遗留缓存
        serverMode = WPTransferData.Mode.MODE_PALETTE   // 画板模式
        zoom = WPDrawConstant.ZOOM_BASIC
        initBgSteps(null)
        val defBoardInfo = BoardBasicInfo.boardInfo(WPTransferData.Mode.MODE_PALETTE, boardID)
        _boardInfoFlow.value = defBoardInfo
        updateCurrentSaveIndex()
    }

    /**
     * 标注模式的初始化
     */
    suspend fun initByDefMark(screenShot: Bitmap) {
        serverMode = WPTransferData.Mode.MODE_MARK  // 标注模式
        zoom = WPDrawConstant.ZOOM_BASIC
        initBgSteps(screenShot)
        logTagD(TAG, "设置截图背景图")
        _pageBgBitmapFlow.emit(screenShot)
        _boardInfoFlow.value = BoardBasicInfo.boardInfo(WPTransferData.Mode.MODE_MARK, boardID)

        updateCurrentSaveIndex()
    }

    private suspend fun initBgSteps(bgBitmap: Bitmap?) {
        paintIndex = 0  // 重置步骤索引

        bgBitmap?.let {
            logTagD(TAG, "设置截图背景图")
            val bgBitmapArr = it.toBytes(format = Bitmap.CompressFormat.WEBP_LOSSY, quality = 80)
            bgStep = WPTransferData.PaintStep.newBuilder()
                .setPaintStepContentImg(
                    WPTransferData.PaintStepContentImg
                        .newBuilder()
                        .setType(WPTransferData.PaintContentType.PAINT_CONTENT_TYPE_BG)
                        .setImgBytes(ByteString.copyFrom(bgBitmapArr))
                        .build()
                )
                .build()
        }
    }

    fun sendMode(devID: String? = null) {
        logTagD(TAG, "同步模式:${serverMode}到远端: $devID")
        WritePadDeviceModeManager.updateServerMode(serverMode)
    }

    /**
     * 同步当前绘画的信息
     */
    suspend fun broadcastPaintContent(
        currentBmp: Bitmap,
        withBg: Boolean = true,
        devID: String? = null
    ) {
        // 2. 添加新的绘制步骤
        val steps = mutableListOf<WPTransferData.PaintStep>()
        if (withBg) {
            bgStep?.let {
                steps.add(it)
            }
        }

        val imgBytes = ByteString.copyFrom(
            currentBmp.toBytes(
                format = Bitmap.CompressFormat.WEBP_LOSSY,  // 总体来说比较小
            )
        )

        val paintStepContentImg = WPTransferData.PaintStepContentImg.newBuilder()
            .setType(WPTransferData.PaintContentType.PAINT_CONTENT_TYPE_DRAW)
            .setImgBytes(imgBytes)
            .build()
        val step = WPTransferData.PaintStep.newBuilder()
            .setPaintStepContentImg(paintStepContentImg)
            .build()
        steps.add(step)
        val paintInfo = mkPaintInfo(steps)
        WritePadDeviceModeManager.sendMsgSync(ServerProtoBuilder.withBizData {
            this.paintInfo = paintInfo
        }, devID)
    }

    /**
     * 同步画板信息
     */
    suspend fun broadBoardInfo(devID: String? = null) {
        val info = WPTransferData.PaintInfo.newBuilder()
            .setDrawingBoardInfo(mkBoardInfo())
            .setStepFromIndex(STEP_INDEX_NONE)
            .setStepToIndex(STEP_INDEX_NONE)
            .build()
        WritePadDeviceModeManager.sendMsgSync(ServerProtoBuilder.withBizData {
            this.paintInfo = info
        }, devID)
    }

    /**
     * 同步数据给客户端
     * @param devID 客户端ID, 如果为空, 则同步给所有客户端
     */
    private suspend fun broadcastSteps(
        steps: List<WPTransferData.PaintStep>,
        devID: String? = null
    ) {
        val paintInfo = mkPaintInfo(steps)
        WritePadDeviceModeManager.sendMsgSync(ServerProtoBuilder.withBizData {
            this.paintInfo = paintInfo
        }, devID)
        if (paintInfo.stepsCount > 1) {
            logTagD(
                TAG,
                "同步多条步数:[${paintInfo.stepFromIndex},${paintInfo.stepToIndex}](${paintInfo.stepsCount}) - 到远端: $devID"
            )
        }
    }

    /**
     * 添加绘制步骤
     */
    private fun addPaintStepContentDraw(paintStepContentDraw: WPTransferData.PaintStepContentDraw) {
        val stepIndex = paintIndex + 1
        val step = WPTransferData.PaintStep.newBuilder()
            .setIndex(stepIndex)
            .setPaintStepContentDraw(paintStepContentDraw)
            .build()
        stepsFlow.tryEmit(step)
        paintIndex = stepIndex
    }

    /**
     * 上一页
     */
    suspend fun changePreviousPage(drawBitmap: Bitmap) {
        val targetIndex = pageIndex - 1
        if (targetIndex < 1) {
            logTagI(TAG, "已经到第一页, 无法翻页")
            return
        }
        WritePadDeviceModeManager.sendMsg(ServerProtoBuilder.withProcessControl(WPTransferData.ProcessControlEvent.DRAW_ACTION_PAGE_CHANGING))

        pageCacheRepository.savePageCache(pageIndex - 1, drawBitmap, pageBgBitmap)

        val targetCount = pageCount
        changePage(targetIndex, targetCount)
    }

    /**
     * 下一页
     */
    suspend fun changeNextPage(drawBitmap: Bitmap) {
        val targetIndex = pageIndex + 1
        if (targetIndex > pageCount) {
            logTagI(TAG, "已经到最大页数, 无法翻页")
            return
        }
        WritePadDeviceModeManager.sendMsg(ServerProtoBuilder.withProcessControl(WPTransferData.ProcessControlEvent.DRAW_ACTION_PAGE_CHANGING))

        pageCacheRepository.savePageCache(pageIndex - 1, drawBitmap, pageBgBitmap)
        changePage(targetIndex, pageCount)
    }

    /**
     * 创建新的页面
     */
    suspend fun createNewPage(drawBitmap: Bitmap) {
        logTagI(TAG, "创建新的页面")
        WritePadDeviceModeManager.sendMsg(ServerProtoBuilder.withProcessControl(WPTransferData.ProcessControlEvent.DRAW_ACTION_PAGE_CHANGING))

        pageCacheRepository.savePageCache(pageIndex - 1, drawBitmap, pageBgBitmap)

        val targetIndex = pageCount + 1 // 直接切换到最后一页
        changePage(targetIndex, targetIndex)
    }

    /**
     * 更新当前页面保存的索引
     */
    private fun updateCurrentSaveIndex() {
        currentSaveIndex = paintIndex
    }

    /**
     * 判断是否可以保存
     */
    fun canSave(): Boolean {
        if (currentSaveIndex != paintIndex) {
            logTagD(TAG, "当前步骤索引: $paintIndex, 保存索引: $currentSaveIndex")
            return true
        }
        if (pageCacheRepository.hasCacheFile) {
            logTagV(TAG, "有缓存文件, 可以保存")
            return true
        }
        return false
    }

    suspend fun doWithPaletteState(state: PaletteState, block: suspend () -> Unit) {
        paletteStateHolder.doWithState(state, block)
    }

    /**
     * 保存或者更新到文件
     */
    suspend fun saveOrUpdateToFile(drawImg: Bitmap): Boolean {
        return try {
            isSaving = true

            WritePadDeviceModeManager.sendMsg(ServerProtoBuilder.withProcessControl(WPTransferData.ProcessControlEvent.DRAW_ACTION_SAVING))

            pageCacheRepository.savePageCache(pageIndex - 1, drawImg, pageBgBitmap)
            val allCachedFiles = pageCacheRepository.getAllCachedIndexDir()
            allCachedFiles.forEach {
                logTagV(TAG, "保存的文件: $it")
            }

            val newAlbum = if (album != null) {
                // 如果已经保存过, 就不再保存了
                logTagV(TAG, "更新绘画")
                PaintRepository.updatePaintByDrawFile(album!!, allCachedFiles)
            } else {
                logTagV(TAG, "新的Paint")
                PaintRepository.createOrAlbumAndPaintByDrawFile(allCachedFiles)

            }
            album = newAlbum
            logTagD(TAG, "保存完成")
            updateCurrentSaveIndex()
            true
        } catch (e: Exception) {
            logTagE(TAG, "保存或者更新到文件失败", tr = e)
            false
        } finally {
            WritePadDeviceModeManager.sendMsg(ServerProtoBuilder.withProcessControl(WPTransferData.ProcessControlEvent.DRAW_ACTION_SAVE_FINISH))
            isSaving = false
        }
    }


    private suspend fun changePage(targetIndex: Int, targetCount: Int) {
        paintIndex = 0  // 重置步骤索引

        _pageInfoFlow.value = targetIndex to targetCount  // 更新页码信息

        val byteArr = pageCacheRepository.getSavedImgBytes(targetIndex - 1)
        val savedBitmap = if (byteArr != null) {
            // 如果有缓存, 则使用缓存
            logTagD(TAG, "使用缓存的绘制图片")
            BitmapFactory.decodeByteArray(byteArr, 0, byteArr.size)
        } else {
            logTagV(TAG, "没有之前缓存的数据, 是空白页") // 不会走进这个分支了, 一定会有一张空白图
            Bitmap.createBitmap(
                boardInfo.boardWidth,
                boardInfo.boardHeight,
                Bitmap.Config.ARGB_8888
            )
        }

        broadcastPaintContent(savedBitmap, false)
        _savedPageDrawBitmapFlow.emit(savedBitmap)
    }

    /**
     * 判断是否可以翻页
     * @param prePage 是否是向前翻页
     */
    fun canChangePage(prePage: Boolean): Boolean {
        return if (prePage) {
            // 2. 判断当前是否是第一页, 如果是第一页, 则不允许翻页
            pageIndex > 1
        } else {
            pageIndex < pageCount  // 如果当前已经到最大页数, 则不允许翻页
        }
    }

    /**
     * 发送翻页错误
     */
    fun sendPageChangeIllegal(devID: String) {
        launch {
            WritePadDeviceModeManager.sendMsg(
                ServerProtoBuilder.withProcessControl(
                    WPTransferData.ProcessControlEvent.DRAW_ACTION_PAGE_CHANGE_ILLEGAL
                ), devID
            )
        }
    }

    /**
     * 判断是否可以创建新的页面
     */
    fun canCreateNewPage(): Boolean {
        return pageCount < PAGE_COUNT_MAX
    }

    fun isCreatingNewPage(): Boolean {
        return isCreatingNewPage
    }

    fun setCreatingNewPage(isCreating: Boolean) {
        isCreatingNewPage = isCreating
    }

    /**
     * 发送创建新页面的错误
     */
    fun sendCreateNewPageError(devID: String) {
        launch {
            WritePadDeviceModeManager.sendMsg(
                ServerProtoBuilder.withProcessControl(
                    WPTransferData.ProcessControlEvent.DRAW_ACTION_PAGE_COUNT_LIMIT
                ), devID
            )
        }
    }

    fun resetServerModeMode() {
        logTagD(TAG, "广播退出标注模式")
        WritePadDeviceModeManager.resetServerMode()
    }

    /**
     * 创建一个清屏的用户输入
     */
    fun mkCleanScreenUserInput() {
        _userInputFlow.tryEmit(
            BizSyncUserInput.newBuilder()
                .setType(DrawEventType.DRAW_EVENT_TYPE_CLEAR_SCREEN)
                .build()
        )
    }

    /**
     * 将用户输入的数据发送给远端
     * @param point 用户输入的点 (x,y,p,action)
     */
    fun mkUserInput(
        point: UserInputValue,
        color: WPPaintColor,
        type: DrawEventType
    ) {
        val builder = BizSyncUserInput.newBuilder()
            .setX(point.x)
            .setY(point.y)
            .setAction(point.action)
            .setType(type)

        if (type == DrawEventType.DRAW_EVENT_TYPE_PEN) {
            // 只有笔模式才会携带颜色和压力信息
            builder.setPressure(point.p)
                .setColor(color)
        }
        _userInputFlow.tryEmit(builder.build())
    }


    private fun mkPaintInfo(steps: List<WPTransferData.PaintStep>): WPTransferData.PaintInfo {
        return WPTransferData.PaintInfo.newBuilder()
            .setDrawingBoardInfo(mkBoardInfo())
            .setStepFromIndex(steps.firstOrNull()?.index ?: STEP_INDEX_NONE)
            .setStepToIndex(steps.lastOrNull()?.index ?: STEP_INDEX_NONE)
            .addAllSteps(steps)
            .build()
    }

    private fun mkBoardInfo(): WPTransferData.DrawingBoardInfo {
        return WPTransferData.DrawingBoardInfo.newBuilder()
            .setPaintStepIndex(paintIndex)
            // 缩放信息
            .setZoomFloor(boardInfo.zoomFloor)
            .setZoomCeil(boardInfo.zoomCeil)
            .setZoom(zoom)
            // 位移信息
            .setOffsetX(offsetX)
            .setOffsetY(offsetY)
            // 尺寸信息
            .setBoardWidth(boardInfo.boardWidth)
            .setBoardHeight(boardInfo.boardHeight)
            // 页码信息
            .setPageIndex(pageIndex)
            .setPageCount(pageCount)
            .setBoardID(boardInfo.boardID)
            .build()
    }

    override fun onCleared() {
        super.onCleared()
        launch {
            logTagV(TAG, "清理缓存")
            pageCacheRepository.clearCache()
        }
    }
}