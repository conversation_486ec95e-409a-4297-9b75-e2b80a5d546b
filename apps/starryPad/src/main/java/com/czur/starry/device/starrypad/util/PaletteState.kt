package com.czur.starry.device.starrypad.util

import android.os.SystemClock
import androidx.lifecycle.AtomicReference
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.ONE_SECOND
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

/**
 * Created by 陈丰尧 on 2025/1/7
 */
private const val TAG = "PaletteStateHolder"

/**
 * 画板状态管理
 * 画板状态的不同, 会影响画板的行为,比如绘制中, 不能进行翻页操作
 */
class PaletteStateHolder {
    companion object {
        private const val DRAW_TIMEOUT = 10 * ONE_SECOND
    }

    // 画板状态
    private val state = AtomicReference(PaletteState.IDLE)
    private val mutex = Mutex()

    private val drawingMap = mutableMapOf<String, Long>()

    /**
     * 修改画板状态
     * @param newState 新状态, 只有当前状态是IDLE时才能修改
     * @return 是否修改成功
     */
    suspend fun changeState(newState: PaletteState): Boolean {
        return mutex.withLock {
            if (newState == PaletteState.IDLE) {
                drawingMap.clear()
                state.set(newState)
                return@withLock true
            }
            // Only update state if current state is IDLE
            if (getState() == PaletteState.IDLE) {
                state.set(newState)
                true
            } else {
                false
            }
        }
    }

    suspend fun doWithState(newState: PaletteState, block: suspend () -> Unit): Boolean {
        return mutex.withLock {
            if (newState == PaletteState.IDLE) {
                logTagW(TAG, "doWithState: newState is IDLE, do nothing")
                return@withLock true
            }
            // Only update state if current state is IDLE
            if (getState() == PaletteState.IDLE) {
                state.set(newState)
                block()
                state.set(PaletteState.IDLE)
                true
            } else {
                logTagW(TAG, "doWithState: current state is ${getState()}, do nothing")
                false
            }
        }
    }

    fun rememberDrawing(devID: String) {
        drawingMap[devID] = SystemClock.elapsedRealtime()
    }

    /**
     * 尝试结束绘制
     */
    suspend fun tryFinishDrawing(devID: String): Boolean {
        drawingMap.remove(devID)
        if (drawingMap.isEmpty()) {
            return changeState(PaletteState.IDLE)
        }
        return false
    }

    /**
     * 获取画板状态
     */
    fun getState(): PaletteState {
        val currentState = state.get()
        if (currentState == PaletteState.DRAWING) {
            val latestTime = drawingMap.values.maxOrNull() ?: Long.MIN_VALUE
            if (SystemClock.elapsedRealtime() - latestTime > DRAW_TIMEOUT) {
                // 防止绘制过程中掉线导致画板状态异常
                logTagW(TAG, "画板状态异常, 重置为IDLE latestTime: $latestTime")
                state.set(PaletteState.IDLE)
                return PaletteState.IDLE
            }
        }
        return currentState
    }
}


enum class PaletteState {
    IDLE,           // 空闲状态
    DRAWING,        // 绘制状态
    CONTROLLING,    // 控制状态: 翻页, 新建, 撤销, 恢复
}