package com.czur.starry.device.starrypad.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.starrypad.hardware.WritePadDeviceModeManager
import com.czur.starry.device.starrypad.hardware.trans.netty.WritePadNettyServer
import kotlinx.coroutines.runBlocking

private const val TAG = "ShutdownReceiver"

class ShutdownReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent?) {
        runBlocking {
            (WritePadDeviceModeManager.nettySender as? WritePadNettyServer)?.stopServer()
        }
    }

    fun register(context: Context) {
        logTagV(TAG, "注册关机广播")
        val intentFilter = IntentFilter()
        intentFilter.addAction(Intent.ACTION_SHUTDOWN)
        context.registerReceiver(this, intentFilter)
    }

    fun unregister(context: Context) {
        logTagV(TAG, "注销关机广播")
        context.unregisterReceiver(this)
    }
}