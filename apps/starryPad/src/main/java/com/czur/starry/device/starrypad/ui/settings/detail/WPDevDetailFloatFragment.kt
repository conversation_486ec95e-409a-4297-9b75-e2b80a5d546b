package com.czur.starry.device.starrypad.ui.settings.detail

import android.os.Bundle
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.lifecycleScope
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.v2.fragment.floating.CZVBFloatingFragment
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.setOnSuspendClickListener
import com.czur.starry.device.starrypad.databinding.FloatFragmentWpDevDetailBinding
import com.czur.starry.device.starrypad.devinfo.WPDevInfo
import com.czur.starry.device.starrypad.ui.settings.WritePadDevInfoViewModel
import kotlinx.coroutines.flow.map

/**
 * Created by 陈丰尧 on 2024/2/23
 */
class WPDevDetailFloatFragment : CZVBFloatingFragment<FloatFragmentWpDevDetailBinding>() {
    companion object {
        private const val TAG = "WPDevDetailFloatFragment"

        const val KEY_DEV_NAME = "KEY_DEV_NAME"
        const val BTN_DEL = 2
    }

    private val wpViewModel: WritePadDevInfoViewModel by activityViewModels()

    var onBtnClick: (suspend (btn: Int, devInfo: WPDevInfo) -> Unit)? = null

    private var currentDevInfo: WPDevInfo? = null

    override fun FloatingFragmentParams.initFloatingParams() {
        this.outSideDismiss = true
        this.floatingBgMode = FloatingBgMode.Dark
    }

    override fun FloatFragmentWpDevDetailBinding.initBindingViews() {
        closeIv.setOnClickListener {
            dismiss()
        }
        launch {  }
        // 手写板删除按钮
        delDevBtn.setOnSuspendClickListener(viewLifecycleOwner.lifecycleScope) {
            currentDevInfo?.let {
                onBtnClick?.invoke(BTN_DEL, it)
            }
            dismiss()
        }
    }

    override fun onDismiss() {
        super.onDismiss()
        onBtnClick = null
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        val devName = requireArguments().getString(KEY_DEV_NAME)
        if (devName.isNullOrEmpty()) {
            dismiss()
            logTagW(TAG, "没有设备名称")
            return
        }
        val targetDevFlow = wpViewModel.writePadInfoListFlow.map { list ->
            list.firstOrNull { it.name == devName }
        }
        repeatCollectOnResume(targetDevFlow) {
            if (it == null) {
                logTagW(TAG, "没有找到设备信息")
            }
            currentDevInfo = it
            binding.wpDeviceNameTv.text = it?.name ?: ""
        }
    }
}