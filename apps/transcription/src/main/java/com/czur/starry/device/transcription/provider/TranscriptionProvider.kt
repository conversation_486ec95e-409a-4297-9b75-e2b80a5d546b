package com.czur.starry.device.transcription.provider

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStore
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.data.provider.TransHandler
import com.czur.starry.device.baselib.data.provider.TransHandler.KEY_IS_SHOW_SUBTITLES
import com.czur.starry.device.baselib.data.provider.TransHandler.KEY_IS_STOP_TRANS
import com.czur.starry.device.baselib.data.provider.TransHandler.KEY_IS_TRANSLATING
import com.czur.starry.device.baselib.handler.SPContentHandler
import com.czur.starry.device.baselib.handler.SPContentProvider
import com.czur.starry.device.baselib.handler.createDefCorruptionHandler
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.baselib.utils.prop.setBooleanSystemProp

class TranscriptionProvider : SPContentProvider() {
    companion object {
        private const val TAG = "TranscriptionProvider"
        private const val MEETING_STATUS_PROP_KEY = "system.meeting.status"
    }

    override val Context.providerDataStore: DataStore<Preferences> by preferencesDataStore(
        name = "TranscriptionProvider",
        corruptionHandler = createDefCorruptionHandler("TranscriptionProvider")
    )
    override val targetHandler: SPContentHandler by lazy { TransHandler }


    override fun onSubQuery(key: String, defValue: String): String? {
        return when (key) {
            KEY_IS_TRANSLATING -> isTranslating().toString()
            KEY_IS_STOP_TRANS -> isStopTrans().toString()
            KEY_IS_SHOW_SUBTITLES -> isShowSubtitles().toString()
            else -> null
        }
    }

    private fun isTranslating(): Boolean {
        val statusCode = getBooleanSystemProp(KEY_IS_TRANSLATING, false)
        return statusCode
    }

    private fun setTranslating(isTranslating: Boolean) {
        setBooleanSystemProp(KEY_IS_TRANSLATING, isTranslating)
    }

    private fun isStopTrans(): Boolean {
        val statusCode = getBooleanSystemProp(KEY_IS_STOP_TRANS, true)
        return statusCode
    }

    private fun setStopTrans(stopTrans: Boolean) {
        setBooleanSystemProp(KEY_IS_STOP_TRANS, stopTrans)
    }

    private fun isShowSubtitles(): Boolean {
        val statusCode = getBooleanSystemProp(KEY_IS_SHOW_SUBTITLES, true)
        return statusCode
    }

    private fun setShowSubtitles(isShow: Boolean) {
        setBooleanSystemProp(KEY_IS_SHOW_SUBTITLES, isShow)

    }

    private fun setTranslatingStatus(meetingStatus: Boolean) {
        logTagV(TAG, "设置AI互译字幕状态为：$meetingStatus 调用者:${callingPackage}")
        setBooleanSystemProp(KEY_IS_TRANSLATING, meetingStatus)
    }

    override fun onSubUpdate(key: String, value: String): Boolean {
        if (key == KEY_IS_TRANSLATING) {
            setTranslatingStatus(value.toBoolean())
            return true
        }

        if (key == KEY_IS_STOP_TRANS) {
            val status = value.toBoolean()
            setStopTrans(status)
            return true
        }

        if (key == KEY_IS_SHOW_SUBTITLES) {
            setShowSubtitles(value.toBoolean())
            return true
        }


        return false
    }
}