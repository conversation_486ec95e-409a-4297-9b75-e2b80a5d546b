package com.czur.starry.device.transcription.util

import android.text.Layout
import android.text.StaticLayout
import android.widget.TextView
import com.czur.czurutils.log.logTagD


class FrontEllipsizeHelper(private val mTextView: TextView, private val mMaxLines: Int)  {
    private var mMaxLineReached = 0 // 记录达到过的最大行数
    private var mResetLineCount = false // 是否重置行数计数
    private val LINE_PADDING_MARKER = "\u200B" // 使用零宽度空格作为标记，不可见


    /**
     * 重置行数计数，在清空文本或网络错误时调用
     */
    fun resetLineCount() {
        mMaxLineReached = 0
        mResetLineCount = true
        // 确保TextView的文本被清空，避免残留空白行
        mTextView.text = ""
    }

    fun setText(historyContent: String, currentContent: String, isEndOfASentence: Boolean): String {
        var isOverMaxLine = false

        // 首先清除之前添加的换行符标记
        val cleanHistoryContent = removeLinePaddingMarkers(historyContent)

        val originalText = buildString {
            append(cleanHistoryContent)
            append(currentContent)
        }

        val paint = mTextView.paint
        val availableWidth = mTextView.width - mTextView.paddingLeft - mTextView.paddingRight
        if (availableWidth <= 0) {
            // If the width is not available yet, skip processing
            return ""
        }

        var resultText = ""
        // Create a StaticLayout to measure the text
        var layout = StaticLayout.Builder.obtain(
            originalText,
            0,
            originalText.length,
            paint,
            availableWidth
        )
            .setAlignment(Layout.Alignment.ALIGN_NORMAL)
            .setLineSpacing(0.0f, 1.0f) // 使用与TextView相同的行高倍数
            .setIncludePad(false)
            .build()

        // 注意：这里我们使用的是StaticLayout来测量行数，但实际显示时使用的是FixedLineHeightTextView
        // FixedLineHeightTextView会确保每行高度固定，无论是中文还是英文

        // 如果重置标志为true，重置最大行数记录
        if (mResetLineCount) {
            mMaxLineReached = 0
            mResetLineCount = false
        }

        // 更新达到过的最大行数
        if (layout.lineCount > mMaxLineReached) {
            mMaxLineReached = layout.lineCount
        }

        if (layout.lineCount > mMaxLines) {
            // 文本超过最大行数，需要截断
            val truncatedText = StringBuilder(originalText)
            isOverMaxLine = true

            // 按行删除而不是按字符删除
            while (layout.lineCount > mMaxLines) {
                // 获取第一行的结束位置
                val firstLineEnd = layout.getLineEnd(0)
                // 删除整个第一行
                truncatedText.delete(0, firstLineEnd)

                // 重新创建布局测量
                layout = StaticLayout.Builder.obtain(
                    truncatedText,
                    0,
                    truncatedText.length,
                    paint,
                    availableWidth
                )
                    .setAlignment(Layout.Alignment.ALIGN_NORMAL)
                    .setLineSpacing(0.0f, 1.0f) // 使用与TextView相同的行高倍数
                    .setIncludePad(false)
                    .build()

                // 如果文本变为空，则退出循环
                if (truncatedText.isEmpty()) break
            }
            resultText = truncatedText.toString()
        } else if (mMaxLineReached >= 2 && layout.lineCount < mMaxLineReached && !originalText.isBlank() && layout.lineCount < mMaxLines) {
            // 文本曾经达到过2行或更多，但现在行数减少了，需要补全
            // 但只在当前行数小于最大允许行数时才补全，避免在最后一行下面添加空白行
            resultText = ensureMinimumLines(originalText, Math.min(mMaxLineReached, mMaxLines), availableWidth, paint)
        } else {
            // 正常显示文本
            isOverMaxLine = false
            resultText = originalText.toString()
        }

        mTextView.text = resultText

        if (isOverMaxLine) { // 现在是超过最大行数处理后的状态
            return if (isEndOfASentence) {
                // 句子结尾就直接改变historyContent为historyContent+currentContent
                resultText
            } else {
                // 不是句子结尾返回historyContent的处理结果
                historyContent.longestCommonSubstring(resultText)
            }
        } else {
            return if (isEndOfASentence) {
                // 句子结尾就直接改变historyContent为historyContent+currentContent
                resultText
            } else {
                // 不是句子结尾返回historyContent的处理结果
                historyContent
            }
        }
    }

    /**
     * 确保文本至少有指定的最小行数，但不超过最大行数
     */
    private fun ensureMinimumLines(text: String, minLines: Int, availableWidth: Int, paint: android.text.TextPaint): String {
        if (text.isEmpty()) return text

        // 首先清除之前添加的换行符标记
        val cleanText = removeLinePaddingMarkers(text)

        // 创建布局测量当前文本的行数
        val layout = StaticLayout.Builder.obtain(
            cleanText,
            0,
            cleanText.length,
            paint,
            availableWidth
        )
            .setAlignment(Layout.Alignment.ALIGN_NORMAL)
            .setLineSpacing(0.0f, 1.0f)
            .setIncludePad(false)
            .build()

        val currentLineCount = layout.lineCount

        // 如果行数已经达到或超过最小行数，或者最小行数超过了最大允许行数，直接返回
        if (currentLineCount >= minLines || minLines > mMaxLines) return cleanText

        // 计算需要补充的行数，确保不会超过最大行数
        val linesToAdd = Math.min(minLines - currentLineCount, mMaxLines - currentLineCount)

        // 如果不需要添加行，直接返回
        if (linesToAdd <= 0) return cleanText

        // 构建补全后的文本
        val resultText = StringBuilder(cleanText)

        // 添加换行符来增加行数，并添加标记以便于后续识别
        for (i in 0 until linesToAdd) {
            resultText.append("\n$LINE_PADDING_MARKER") // 添加换行符和标记
        }

        return resultText.toString()
    }

    /**
     * 移除文本中的换行符标记
     */
    private fun removeLinePaddingMarkers(text: String): String {
        // 匹配换行符后跟着标记的模式
        val pattern = "\\n$LINE_PADDING_MARKER".toRegex()
        return text.replace(pattern, "")
    }

    /**
     * 创建带有空行的错误提示文本，使错误消息显示在第二行的中间
     */
    fun createErrorTextInSecondLine(errorText: String): String {
        // 添加空行使错误文本显示在第二行
        return "\n$LINE_PADDING_MARKER$errorText"
    }
}

fun String.longestCommonSubstring(other: String): String {
    if (this.isEmpty() || other.isEmpty()) return ""

    val maxLength = this.length.coerceAtMost(other.length)
    var longestSub = ""

    for (i in 0 until this.length) {
        for (j in 0 until other.length) {
            var k = 0
            while (i + k < this.length && j + k < other.length && this[i + k] == other[j + k]) {
                k++
            }
            if (k > longestSub.length) {
                longestSub = this.substring(i, i + k)
            }
        }
    }

    return longestSub
}
