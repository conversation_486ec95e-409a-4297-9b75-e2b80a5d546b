package com.czur.starry.device.transcription.widget

import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.TextView
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.BaseDialog
import com.czur.starry.device.baselib.utils.getString
import com.czur.starry.device.baselib.utils.view.findView
import com.czur.starry.device.transcription.R
import com.czur.starry.device.transcription.databinding.DialogGetTimeBinding
import kotlin.getValue

class GetGiftDialog() : BaseDialog() {

    var onGetGiftClick: (() -> Unit)? = null
    var onDismissListener: (() -> Unit)? = null
    var giftTimeStr: String = ""

    private val getTimeTv: View by findView(R.id.getTimeTv)
    private val giftDaysTv: TextView by findView(R.id.giftDaysTv)

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_get_time, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        getTimeTv.setOnClickListener {
            onGetGiftClick?.invoke()
            dismiss()
        }
        giftDaysTv.text = getString(R.string.str_translation_gift_days, giftTimeStr)

    }

    class Builder {
        private var confirmClickListener: (() -> Unit)? = null
        private var onDismissListener: (() -> Unit)? = null
        private var giftTimeStr: String = ""


        fun setGiftTimeStr(timeStr: String): Builder {
            giftTimeStr = timeStr
            return this
        }

        fun setConfirmClickListener(listener: () -> Unit): Builder {
            confirmClickListener = listener
            return this
        }

        fun setDismissListener(listener: () -> Unit): Builder {
            onDismissListener = listener
            return this
        }


        fun build(): GetGiftDialog {
            val dialog = GetGiftDialog()
            dialog.onGetGiftClick = confirmClickListener
            dialog.onDismissListener = onDismissListener
            dialog.giftTimeStr = giftTimeStr
            dialog.isBottom = false
            return dialog
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        onDismissListener?.invoke()
    }
}