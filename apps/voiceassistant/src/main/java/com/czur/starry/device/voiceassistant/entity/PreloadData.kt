package com.czur.starry.device.voiceassistant.entity

import com.google.gson.annotations.SerializedName
import kotlinx.serialization.Serializable

/**
*  author : <PERSON><PERSON><PERSON>
*  time   :2025/03/11
*/


@Serializable
data class PreloadData(
    @SerializedName("version")
    val version: String,
    @SerializedName("preload")
    val preload: List<PreloadItem>
)

@Serializable
data class PreloadItem(
    @SerializedName("key")
    val key: String,
    @SerializedName("text")
    val text: String,
    @SerializedName("url")
    val url: String
)

@Serializable
data class LocalPreItem(
    @SerializedName("key")
    val key: String,
    @SerializedName("text")
    val text: String,
    @SerializedName("absolutePath")
    val absolutePath: String
)
