package com.czur.starry.device.voiceassistant

import android.audioai.AudioAiServiceManager
import android.content.Context
import android.content.Intent
import android.hardware.display.DisplayManager
import android.os.Build
import android.os.PowerManager
import android.util.DisplayMetrics
import android.view.Display
import android.view.Gravity
import android.view.WindowManager
import androidx.lifecycle.LifecycleService
import androidx.lifecycle.lifecycleScope
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.Constants.starryHWInfo
import com.czur.starry.device.baselib.common.hw.StudioSeries
import com.czur.starry.device.baselib.data.provider.UserHandler
import com.czur.starry.device.baselib.utils.CZPowerManager
import com.czur.starry.device.baselib.utils.CZPowerManager.wakeUpScreen
import com.czur.starry.device.baselib.utils.InternetStatus
import com.czur.starry.device.baselib.utils.NetStatusUtil
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.voiceassistant.common.Constants.AUDIO_EVENT
import com.czur.starry.device.voiceassistant.common.Constants.CHAT_FINAL_EVENT
import com.czur.starry.device.voiceassistant.common.Constants.COMMAND_APP_NOT_INSTALL
import com.czur.starry.device.voiceassistant.common.Constants.COMMAND_CANNOT
import com.czur.starry.device.voiceassistant.common.Constants.COMMAND_CANNOT_SLEEP
import com.czur.starry.device.voiceassistant.common.Constants.COMMAND_HERE
import com.czur.starry.device.voiceassistant.common.Constants.COMMAND_NETWORK_ERROR
import com.czur.starry.device.voiceassistant.common.Constants.COMMAND_NOT_FOUND
import com.czur.starry.device.voiceassistant.common.Constants.COMMAND_OK
import com.czur.starry.device.voiceassistant.common.Constants.KEY_MESSAGE
import com.czur.starry.device.voiceassistant.common.Constants.POWER_OFF_EVENT
import com.czur.starry.device.voiceassistant.common.Constants.TIME_OUT_EVENT
import com.czur.starry.device.voiceassistant.common.Constants.TIME_OUT_MILLIS
import com.czur.starry.device.voiceassistant.common.Constants.TTS_FINISH_EVENT
import com.czur.starry.device.voiceassistant.common.Constants.VOICE_ACTIVITY_DETECTED
import com.czur.starry.device.voiceassistant.common.Constants.WAKE_UP_EVENT
import com.czur.starry.device.voiceassistant.common.Constants.WAKE_UP_PLAY_VIDEO
import com.czur.starry.device.voiceassistant.entity.CommandEvent
import com.czur.starry.device.voiceassistant.entity.CommandEventHandler
import com.czur.starry.device.voiceassistant.entity.LocalPreItem
import com.czur.starry.device.voiceassistant.entity.PreloadData
import com.czur.starry.device.voiceassistant.helper.InstallAppHelper
import com.czur.starry.device.voiceassistant.helper.LaunchAppHelper
import com.czur.starry.device.voiceassistant.helper.LaunchForNavigate
import com.czur.starry.device.voiceassistant.helper.LaunchResult
import com.czur.starry.device.voiceassistant.helper.SettingAppHelper
import com.czur.starry.device.voiceassistant.helper.SettingResult
import com.czur.starry.device.voiceassistant.manager.AiServiceCallback
import com.czur.starry.device.voiceassistant.manager.AmpProcessor
import com.czur.starry.device.voiceassistant.manager.AudioTrackManager.playPcmFile
import com.czur.starry.device.voiceassistant.manager.AudioTrackManager.playResource
import com.czur.starry.device.voiceassistant.manager.AudioTrackManager.playTtsStream
import com.czur.starry.device.voiceassistant.manager.AudioTrackManager.stopPlaybackSafely
import com.czur.starry.device.voiceassistant.manager.PreDataManager.getLocalPreVersion
import com.czur.starry.device.voiceassistant.manager.PreDataManager.getLocalPreloadItems
import com.czur.starry.device.voiceassistant.manager.PreDataManager.saveLocalPreVersion
import com.czur.starry.device.voiceassistant.util.FileUtil.getFilteredNameResList
import com.czur.starry.device.voiceassistant.util.NtpTimeChecker
import com.czur.starry.device.voiceassistant.util.PreloadUtil.processPreloadData
import com.czur.starry.device.voiceassistant.util.PreloadUtil.requestPreloadData
import com.czur.starry.device.voiceassistant.util.ShortcutUtil
import com.czur.starry.device.voiceassistant.view.VoiceAssistantPresentation
import com.czur.starry.device.voiceassistant.view.WindowUI
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean

/**
 *  author : WangHao
 *  time   :2025/01/17
 */


class VoiceAssistantService : LifecycleService(), CommandEventHandler {
    companion object {
        const val TAG = "VoiceAssistantService"


        private var initPreloadFinish: Boolean = false
        private var initInteractionFinish: Boolean = false

        //UI是否唤起状态
        var isWakeupState = AtomicBoolean(false)

        //UI隐藏过程中
        var isHidingView = AtomicBoolean(false)

        //执行命令中
        var isExecutingCommand = AtomicBoolean(false)

        //等待返回结果
        var isWaitingForResult = AtomicBoolean(false)

    }

    private val launchAppHelper by lazy { LaunchAppHelper(this, packageManager) }
    private val launchForNavigate by lazy { LaunchForNavigate(this, packageManager) }
    private val installAppHelper by lazy { InstallAppHelper(this, packageManager) }
    private val shortcutUtil by lazy { ShortcutUtil(this, this, launchForNavigate) }
    private val windowUI by lazy { WindowUI(this, lifecycleOwner = this, this) }
    private val ampProcessor = AmpProcessor(this, windowUI, this)
    private val voiceController by lazy { AiServiceCallback(this, windowUI, ampProcessor, this) }
    private val netStatusUtil by lazy { NetStatusUtil(this) }
    private val aiServiceManager by lazy { getSystemService("audioai") as? AudioAiServiceManager }


    private var settingsMenuPageList: MutableList<String>? = null
    private var functionList: List<String> = emptyList()

    //辅助屏幕 小圆屏
    private var presentation: VoiceAssistantPresentation? = null

    //预加载数据
    private var preloadItems: List<LocalPreItem> = emptyList()
    private var timeoutJob: Job? = null

    private val powerManager by lazy { getSystemService(Context.POWER_SERVICE) as PowerManager }
    private var wakeLock: PowerManager.WakeLock? = null

    private val wakeupScreenLock by lazy {
        CZPowerManager.createOneWakeLock("aiWakeUpScreen")
    }

    override fun onCreate() {
        super.onCreate()
        logTagD(TAG, "---onCreate-----")
        initializeDependencies()
        setupNetworkObserver()

        val settingPageList =
            resources.getStringArray(R.array.setting_page_list).toMutableList()
        settingsMenuPageList = getFilteredNameResList(this, settingPageList) as MutableList<String>?
        functionList = resources.getStringArray(R.array.function_list).toList()

    }


    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        intent?.let(::handleIntent)
        return super.onStartCommand(intent, flags, startId)
    }


    override fun onDestroy() {
        netStatusUtil.stopWatching()
        aiServiceManager?.run {
            interactionStop()
            unregisterCallback(voiceController)
        }
        wakeupScreenLock.release()
        syncStarryState(0)
        logTagD(TAG, "Service destroyed")
        super.onDestroy()
    }


    // region Private functions
    private fun initializeDependencies() {
        SettingAppHelper.init(this)
        windowUI.onCreate()
    }

    private fun setupNetworkObserver() {
        netStatusUtil.startWatching()
        netStatusUtil.internetStatusLive.observe(this) { status ->
            if (status == InternetStatus.CONNECT) {
                handleNetworkConnected()
            } else {
                if (initInteractionFinish) {
                    logTagD(TAG, "=======unregisterCallback")
                    aiServiceManager?.run {
                        interactionStop()
                        unregisterCallback(voiceController)
                    }
                }
            }
        }
    }

    private fun handleNetworkConnected() {
        lifecycleScope.launch {
            while (UserHandler.secret.isEmpty() || !NtpTimeChecker.checkTimeDiff()) delay(ONE_SECOND)
            initializeServiceComponents()
            initPreloadData()
        }
    }

    private fun initializeServiceComponents() {
        logTagD(TAG, "=====initServiceCallback=====")
        aiServiceManager?.run {
            if (!initInteractionFinish) {
                interactionInit(UserHandler.secret, Constants.isProductEnv)
                initInteractionFinish = true
            }
            registerCallback(voiceController)
            interactionStart()
        } ?: logTagE(TAG, "Failed to initialize AI Service Manager")
    }

    //同步小星状态
    private fun syncStarryState(state: Int) {
        logTagD(TAG, "=====syncStarryState=====")
        aiServiceManager?.setPlayStatus(state)
    }

    /**
     * 初始化预加载文件
     */
    private suspend fun initPreloadData() {
        if (initPreloadFinish) {
            return
        }
        val result = runCatching {
            requestPreloadData().getOrThrow()
        }
        result.onSuccess { data ->
            handlePreloadVersion(data)
            initPreloadFinish = true
        }.onFailure {
            logTagE(TAG, "Preload failed: ${it.message}")
        }
    }

    private fun handlePreloadVersion(data: PreloadData) {
        launch {
            logTagD(TAG, "===预加载数据===:$data")
            data.version?.takeIf { it != getLocalPreVersion() }?.let {
                processPreloadData(this@VoiceAssistantService, data)
                saveLocalPreVersion(it)
            }
            preloadItems = getLocalPreloadItems().orEmpty()
        }
    }


    private fun handleIntent(intent: Intent) {
        intent.getStringExtra(KEY_MESSAGE)?.let { action ->
            shortcutUtil.handleShortcut(action)
        }
    }

    override fun handleCommand(event: CommandEvent) {
        logTagD(TAG, "=====event=${event}=====")
        event?.let {
            classifyCommand(
                it.intent,
                it.target ?: "",
                it.params ?: "",
                it.isTouchPad,
                it.amplitude,
                it.isFinal
            )
        }
    }

    //指令分类的方法
    private fun classifyCommand(
        command: String,
        target: String = "",
        params: String = "",
        isTouchPad: Boolean = false,
        amplitude: Float = 0f,
        isFinal: Boolean = false

    ) {
        var timeout: Long? = null
        if (isExecutingCommand.get()) {
            logTagD(TAG, "===正在执行命令===")
            return
        }
        try {
            when (command) {
                WAKE_UP_EVENT -> {
                    handleWakeUpEvent(isTouchPad)
                }

                WAKE_UP_PLAY_VIDEO -> {
                    isWaitingForResult.set(false)
                    playWakeupHere()
                    timeout = ONE_SECOND * 5
                }

                getString(R.string.intent_exit) -> {
                    handleExitCommand()
                    return
                }

                getString(R.string.intent_nothing) -> {
                    isWaitingForResult.set(false)
                }

                CHAT_FINAL_EVENT -> {
                    timeout = ONE_SECOND * 5
                    isWaitingForResult.set(false)
                }

                POWER_OFF_EVENT -> {
                    handlePowerOffEvent()
                }

                AUDIO_EVENT -> {
                    if (isHidingView.get() || !isWakeupState.get()) {
                        return
                    }
                    timeout = ONE_SECOND * 7
                    isWaitingForResult.set(true)
                }

                TTS_FINISH_EVENT -> {
                    handleTtsFinishEvent(isFinal)
                }

                TIME_OUT_EVENT -> {
                    handleTimeoutEvent()
                }

                VOICE_ACTIVITY_DETECTED -> {
                    if (!isWakeupState.get()) {
                        return
                    }
                    updateAmplitudeVisualization(amplitude)
                    return
                }

                else -> {
                    if (isHidingView.get() || !isWakeupState.get()) {
                        logTagD(TAG, "=====isHidingView,isWakeupState===${isHidingView.get()},${isWakeupState.get()}=")
                        return
                    }
                    timeoutJob?.cancel()
                    isExecutingCommand.set(true)
                    isWaitingForResult.set(false)
                    launch {
                        handleStandardCommand(command, target, params)
                    }
                    return
                }
            }
        } catch (e: Exception) {
            logTagE(TAG, "Command processing failed $e")
            handleCommandProcessingError(e)
        }
        timeout?.let { resetCountdown(it) } ?: resetCountdown()
    }


    private fun handleExitCommand() {
        isExecutingCommand.set(true)
        isWaitingForResult.set(false)
        timeoutJob?.cancel()
        hideStarryUI()
        isExecutingCommand.set(false)
    }

    private fun handlePowerOffEvent() {
        timeoutJob?.cancel()
        isExecutingCommand.set(false)
        isWaitingForResult.set(false)
        hideStarryUI()
    }

    private fun handleTtsFinishEvent(isFinal: Boolean) {
        if (isFinal) {
            logTagD(TAG, "===继续对话==")
            isWaitingForResult.set(false)
            playServiceStream(true)
            resetCountdown()
            return
        }
        isWaitingForResult.set(true)
    }

    private fun handleTimeoutEvent() {
        isExecutingCommand.set(true)
        timeoutJob?.cancel()
        handleCommandEvent(COMMAND_NETWORK_ERROR)
    }

    private fun handleCommandProcessingError(e: Exception) {
        isExecutingCommand.set(true)
        isWaitingForResult.set(false)
        timeoutJob?.cancel()
        handleCommandEvent(COMMAND_CANNOT)
    }

    //更新振幅
    private fun updateAmplitudeVisualization(value: Float) {
        launch(Dispatchers.Main) {
            windowUI.setScaleYFactor(value)
            presentation?.setScaleYFactor(value)
        }
    }


    //唤醒事件处理
    private fun handleWakeUpEvent(isTouchPad: Boolean) {
        launch(Dispatchers.Main) {
            // 等待 UI 结束隐藏动画
            awaitUIHideCompletion()
            // 若为触控板唤醒且屏幕息屏，唤醒屏幕返回
            if (isTouchPad && isScreenOff()) {
                wakeUpScreen(TAG)
                return@launch
            }
            //每次调用确保息屏timeout重新计时
            wakeupScreenLock.acquire()
            if (isWakeupState.get()) {
                handleAlreadyWakeupState(isTouchPad)
                return@launch
            }
            // 执行 UI
            showStarryUI()
            handleTouchPadAfterShowUI(isTouchPad)
        }

    }

    /**
     * 等待 UI 隐藏完成
     */
    private suspend fun awaitUIHideCompletion() {
        while (isHidingView.get()) {
            delay(200)
        }
    }

    /**
     * 处理已经处于唤醒状态的情况
     */
    private suspend fun handleAlreadyWakeupState(isTouchPad: Boolean) {
        // 若不是触控板唤醒，或者触控板唤醒且正在播报，则触发打断
        if (!isTouchPad || isWaitingForResult.get()) {
            handleCommand(CommandEvent(WAKE_UP_PLAY_VIDEO))
        }
        if (isTouchPad) {
            delay(500)
            logTagD(TAG, "=====触控板唤醒== 1===")
            playServiceStream(true)
        }
    }

    /**
     * 在显示 UI 后处理触控板相关操作
     */
    private suspend fun handleTouchPadAfterShowUI(isTouchPad: Boolean) {
        if (isTouchPad) {
            delay(500)
            logTagD(TAG, "=====触控板唤醒==2===")
            playServiceStream(true)
        }
    }

    private suspend fun handleStandardCommand(command: String, target: String, params: String) =
        withContext(Dispatchers.IO) {
            val shouldFeedback = when (command) {
                getString(R.string.intent_open) -> {
                    val result =
                        launchAppHelper.launchAppByName(target, params, settingsMenuPageList!!)
                    when (result) {
                        LaunchResult.SUCCESS -> handleCommandEvent(COMMAND_OK)
                        LaunchResult.NOT_INSTALL -> handleCommandEvent(COMMAND_APP_NOT_INSTALL)
                        LaunchResult.NOT_FOUND -> handleCommandEvent(COMMAND_NOT_FOUND)
                    }
                }

                getString(R.string.intent_open_navigate) -> {
                    launchForNavigate.launchAndNavigate(
                        target,
                        params
                    )
                    handleCommandEvent(COMMAND_OK)
                }

                getString(R.string.intent_appstore) -> {
                    installAppHelper.startAppStoreForInstall(
                        target,
                        params
                    )
                    handleCommandEvent(COMMAND_OK)
                }

                getString(R.string.intent_setting) -> {
                    val result = SettingAppHelper.setAppParams(target, params, functionList)
                    when (result) {
                        SettingResult.SUCCESS -> handleCommandEvent(COMMAND_OK)
                        SettingResult.FAILED -> handleCommandEvent(COMMAND_CANNOT)
                        SettingResult.CANT_EXECUTE -> handleCommandEvent(COMMAND_CANNOT_SLEEP)
                    }
                }

                getString(R.string.intent_nothing) -> {
                    handleCommandEvent(COMMAND_CANNOT)
                }

                else -> {
                    handleCommandEvent(COMMAND_CANNOT)
                }
            }

        }


    private fun handleCommandEvent(event: String) {
        launch {
            playPreloadData(event)
            delay(ONE_SECOND * 3)
            hideStarryUI()
            isExecutingCommand.set(false)
        }
    }

    private fun resetCountdown(timeoutMillis: Long = TIME_OUT_MILLIS) {
        timeoutJob?.cancel() // 取消旧任务
        timeoutJob = launch {
            try {
                delay(timeoutMillis)
                logTagD(TAG, "=====倒计时结束==hideStarryUI===")
                if (isWaitingForResult.get()) {
                    handleCommand(CommandEvent(TIME_OUT_EVENT))
                    isWaitingForResult.set(false)
                } else {
                    hideStarryUI()
                }
            } finally {
                if (isActive.not()) {
                    logTagD(TAG, "任务被提前终止")
                }
            }

        }
    }

    //显示UI
    private fun showStarryUI() {
        isWakeupState.set(true)
        if (starryHWInfo.series == StudioSeries) {
            bootSmallRoundScreen()
        }
        windowUI.show()
        syncStarryState(1)
    }

    private fun hideStarryUI() {
        if (isWakeupState.get()) {
            exitSmallRoundScreen()
            windowUI.hide()
        }
        stopPlaybackSafely()
        syncStarryState(0)
        wakeupScreenLock.release()
    }

    //手动停流
    private fun playServiceStream(isOpen: Boolean) {
        aiServiceManager?.interactionTrigger(isOpen)
    }

    //唤醒启动先播放音频在播放字幕
    private fun playWakeupHere() {
        try {
            stopPlaybackSafely()
            var item: LocalPreItem? = null
            if (!preloadItems.isEmpty()) {
                item = preloadItems?.firstOrNull { it.key == COMMAND_HERE }
            }
            if (item != null) {
                launch {
                    playPcmFile(File(item.absolutePath))
                    windowUI.addHereMessage(item.text)
                    windowUI.startAddMessage()
                    windowUI.restartTtsMessage()
                    playTtsStream()
                }
            } else {
                launch {
                    windowUI.addHereMessage(getString(R.string.voice_command_hello))
                    playResource(this@VoiceAssistantService, R.raw.here)
                    windowUI.startAddMessage()
                    windowUI.restartTtsMessage()
                    playTtsStream()
                }
            }

        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    //预加载播放文字和语音同步
    private fun playPreloadData(command: String) {
        stopPlaybackSafely()
        val item = preloadItems?.firstOrNull { it.key == command }
        if (item != null) {
            launch {
                windowUI.addHereMessage(item.text)
                playPcmFile(File(item.absolutePath))
            }
        } else {
            playDefaultResource(command)
        }

    }

    private fun playDefaultResource(command: String) {
        launch {
            when (command) {
                COMMAND_OK -> {
                    windowUI.addHereMessage(getString(R.string.voice_command_okay))
                    playResource(this@VoiceAssistantService, R.raw.okay)
                }

                COMMAND_CANNOT -> {
                    windowUI.addHereMessage(getString(R.string.voice_command_sorry))
                    playResource(this@VoiceAssistantService, R.raw.cant_do_that_yet)
                }

                COMMAND_APP_NOT_INSTALL -> {
                    windowUI.addHereMessage(getString(R.string.voice_command_app_not_install))
                    playResource(this@VoiceAssistantService, R.raw.app_not_installed)
                }

                COMMAND_NETWORK_ERROR -> {
                    windowUI.addHereMessage(getString(R.string.voice_command_network_error))
                    playResource(this@VoiceAssistantService, R.raw.network_error)
                }

                COMMAND_NOT_FOUND -> {
                    windowUI.addHereMessage(getString(R.string.voice_command_not_found))
                    playResource(this@VoiceAssistantService, R.raw.not_found)
                }

                COMMAND_CANNOT_SLEEP -> {
                    windowUI.addHereMessage(getString(R.string.voice_command_cannot_sleep))
                    playResource(this@VoiceAssistantService, R.raw.in_peripheral_mode)
                }
            }
        }
    }

    private fun getContext(display: Display): Context {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            createWindowContext(
                display,
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
                null
            )
        } else {
            // 兼容低版本方案
            applicationContext.createDisplayContext(display)
        }
    }


    //启动小圆屏Dialog
    private fun bootSmallRoundScreen() {
        val displayManager = getSystemService(DISPLAY_SERVICE) as DisplayManager // 移除冗余限定名
        val displays = displayManager.displays
        var targetDisplayId = -1
        var targetDisplay: Display? = null

        for (display in displays) {
            val metrics = DisplayMetrics()
            // 使用替代方法获取显示指标
            display.getRealMetrics(metrics)
            if (metrics.widthPixels == 360 && metrics.heightPixels == 360) {
                targetDisplayId = display.displayId
                targetDisplay = display
                break
            }
        }
        if (targetDisplayId != -1) {

            targetDisplay?.let { display ->
                presentation = VoiceAssistantPresentation(
                    context = getContext(display),  // 使用Activity的Context
                    display = display,
                    lifecycleScope = this.lifecycleScope
                )
                presentation?.window?.apply {
                    setGravity(Gravity.CENTER)
                }
                runCatching {
                    presentation?.show()
                }.onFailure { e ->
                    logTagE(TAG, "Display failed: ${e.stackTraceToString()}")
                }
                logTagD(TAG, "找到了尺寸为 360x360 的屏幕，Dialog已显示")
            }
        } else {
            logTagD(TAG, "没有找到尺寸为 360x360 的屏幕")
        }
    }


    //退出小圆屏UI
    private fun exitSmallRoundScreen() {
        presentation?.stopAnimations()
    }


    /**
     * 判断屏幕是否处于息屏状态
     */
    private fun isScreenOff(): Boolean {
        return if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.KITKAT_WATCH) {
            !powerManager.isInteractive
        } else {
            !powerManager.isScreenOn
        }
    }
}