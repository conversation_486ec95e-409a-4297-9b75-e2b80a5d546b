package com.czur.starry.device.voiceassistant.util

import android.content.Context
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.apache.commons.net.ntp.NTPUDPClient
import org.apache.commons.net.ntp.TimeInfo
import java.net.InetAddress

/**
 *  author : WangHao
 *  time   :2025/04/25
 */


object NtpTimeChecker {
    private const val TAG = "NtpTimeChecker"
    private const val NTP_SERVER = "ntp2.aliyun.com"
    private const val TIMEOUT_MS = 3000L
    private const val TIMEOUT_DIFF = 2000L

    /**
     * 获取网络时间与本地时间差值
     * @return Pair(网络时间, 时间差毫秒数) 或 null
     */
    suspend fun checkTimeDiff(): Boolean {
        return try {
            withContext(Dispatchers.IO) {
                val client = NTPUDPClient().apply {
                    defaultTimeout = TIMEOUT_MS.toInt()
                }

                try {
                    val address = InetAddress.getByName(NTP_SERVER)
                    val info: TimeInfo = client.getTime(address)
                    info.computeDetails() // 计算网络延迟

                    val ntpTime = info.returnTime
                    val localTime = System.currentTimeMillis()
                    val offset = info.offset ?: 0
                    val adjustedNtpTime = ntpTime + offset
                    logTagD(TAG,"==========NTP时间: $ntpTime, 本地时间: $localTime, 时间差: ${adjustedNtpTime - localTime}")
                    adjustedNtpTime - localTime < TIMEOUT_DIFF
                } catch (e: Exception) {
                    logTagE(TAG, "NTP查询失败: ${e.javaClass.simpleName}")
                    false
                } finally {
                    client.close()
                }
            }
        } catch (e: Exception) {
            logTagE(TAG, "全局异常: ${e.message}")
            false
        }
    }

}