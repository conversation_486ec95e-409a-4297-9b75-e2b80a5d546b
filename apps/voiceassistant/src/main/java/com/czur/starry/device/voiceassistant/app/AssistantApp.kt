package com.czur.starry.device.voiceassistant.app

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStore
import com.czur.starry.device.baselib.base.listener.StarryApp
import com.czur.starry.device.baselib.handler.createDefCorruptionHandler
import kotlin.properties.Delegates

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2025/01/17
 */


class AssistantApp : StarryApp() {


    companion object {
        private const val DATA_STORE_NAME = "PreloadData"

        var instance: AssistantApp by Delegates.notNull()

        fun getApp(): AssistantApp {
            return instance
        }
    }

    val dataStore: DataStore<Preferences> by preferencesDataStore(
        name = DATA_STORE_NAME,
        corruptionHandler = createDefCorruptionHandler(DATA_STORE_NAME)
    )

    override fun onCreate() {
        super.onCreate()
        instance = this
    }
}