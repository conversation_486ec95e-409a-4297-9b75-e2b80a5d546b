package com.czur.starry.device.voiceassistant.widget

import android.animation.Animator
import android.animation.ObjectAnimator
import android.content.Context
import android.graphics.Color
import android.os.Handler
import android.os.Looper
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2025/02/24
 *  跑马灯 滚动文字
 */

private const val TAG = "MarqueeTextView"

class MarqueeTextView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : AppCompatTextView(context, attrs, defStyleAttr) {

    private val handler = Handler(Looper.getMainLooper())
    private val firstLine = StringBuilder()
    private val secondLine = StringBuilder()
    private var firstLineText = ""
    private var secondLineText = ""
    private var firstLineColor = Color.WHITE
    private var secondLineColor = Color.WHITE
    private var textToDisplay = ""
    private var currentIndex = 0
    private var isRunning = false
    private var delayPerChar = 50L // 每个字符显示的延迟时间，单位毫秒
    private val maxLineLength = 13 // 每行的最大字符数
    private lateinit var displayNextCharRunnable: Runnable
    private lateinit var displayFirstCharRunnable: Runnable
    private lateinit var displaySecondCharRunnable: Runnable
    private var speaker: Speaker = Speaker.D
    private val mutex = Mutex() // 协程互斥锁

    //人机说话者
    private enum class Speaker { D, P }

    // 哪行字显示
    private enum class CharState { FIRST, SECOND, ALL }

    init {
        displayFirstCharRunnable = Runnable {
            displayCharInLine(
                firstLine,
                CharState.FIRST,
                displaySecondCharRunnable,
                displayFirstCharRunnable
            )
        }
        displaySecondCharRunnable = Runnable {
            displayCharInLine(
                secondLine,
                CharState.SECOND,
                displayFirstCharRunnable,
                displaySecondCharRunnable
            )
        }
        displayNextCharRunnable = Runnable {
            if (currentIndex < textToDisplay.length) {
                if (textToDisplay.length < maxLineLength) {
                    if (firstLineText.isNullOrEmpty()) {
                        handler.postDelayed(displayFirstCharRunnable, delayPerChar)
                    } else if (secondLineText.isNullOrEmpty()) {
                        handler.postDelayed(displaySecondCharRunnable, delayPerChar)
                    } else {
                        resetLines()
                    }

                } else {
                    resetLines()
                }
            } else {
                // 文本显示完毕，停止动画
                stopMarquee()
            }
        }
    }


    private fun displayCharInLine(
        line: StringBuilder,
        charState: CharState,
        nextRunnable: Runnable,
        runnable: Runnable
    ) {
        if (currentIndex < textToDisplay.length) {
            val currentChar = textToDisplay[currentIndex]
            if (line.length < maxLineLength) {
                line.append(currentChar)
                updateTextAndColor(charState)
                currentIndex++
                handler.postDelayed(
                    runnable,
                    delayPerChar
                ) // Post this runnable again to display the next character
            } else {
                // Line is full, switch to the next runnable
                handler.postDelayed(nextRunnable, delayPerChar)
            }
        } else {
            stopMarquee()
        }
    }

    private fun resetLines() {
        firstLine.clear()
        secondLine.clear()
        updateTextAndColor(CharState.ALL)
        handler.postDelayed(displayFirstCharRunnable, delayPerChar)
    }

    // 更新TextView的文本和颜色
    private fun updateTextAndColor(charState: CharState = CharState.ALL) {
        when (charState) {
            CharState.FIRST -> {
                firstLineText = firstLine.toString()
                firstLineColor = getTextColor()
            }

            CharState.SECOND -> {
                secondLineText = secondLine.toString()
                secondLineColor = getTextColor()
            }

            CharState.ALL -> {
                firstLineText = firstLine.toString()
                secondLineText = secondLine.toString()
                firstLineColor = getTextColor()
                secondLineColor = firstLineColor
            }
        }

        val spannable = SpannableString("$firstLineText\n$secondLineText")
        // 设置第一行颜色
        spannable.setSpan(
            ForegroundColorSpan(firstLineColor),
            0,
            firstLineText.length,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        // 设置第二行颜色
        spannable.setSpan(
            ForegroundColorSpan(secondLineColor),
            firstLineText.length + 1, // 加1是因为有换行符
            firstLineText.length + 1 + secondLineText.length,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        // 设置文本，包含近似居中的处理
        text = spannable
    }

    private fun getTextColor(): Int {
        return when (speaker) {
            Speaker.D -> Color.WHITE
            Speaker.P -> Color.parseColor("#97E5FF")
        }
    }

    // 开始逐字显示
    private fun startMarquee() {
        visibility = VISIBLE
        isRunning = true
        handler.postDelayed(displayNextCharRunnable, delayPerChar)
    }

    // 停止逐字显示
    private fun stopMarquee() {
        isRunning = false
        handler.removeCallbacks(displayNextCharRunnable)
        handler.removeCallbacks(displayFirstCharRunnable)
        handler.removeCallbacks(displaySecondCharRunnable)
    }


    suspend fun setMarqueeText(text: String, isPerson: Boolean = false, isComplete: Boolean = false)  = withContext(Dispatchers.Main){
        mutex.withLock {
            while (isRunning) {
                delay(10)
            }
            // 更新要显示的文本
            textToDisplay = text
            speaker = if (isPerson) Speaker.P else Speaker.D
            // 重置当前显示的文本和索引
            firstLine.clear()
            secondLine.clear()
            currentIndex = 0
            // 开始新的动画
            startMarquee()
        }
    }


    fun startAnimation() {
        val scaleXAnimator = ObjectAnimator.ofFloat(this, "scaleX", 0f, 1f)
        val scaleYAnimator = ObjectAnimator.ofFloat(this, "scaleY", 0f, 1f)
        // 设置动画的持续时长为500毫秒
        scaleXAnimator.duration = 100
        scaleYAnimator.duration = 100
        scaleXAnimator.start()
        scaleYAnimator.start()
    }

    fun stopAnimation() {
        val scaleXAnimator = ObjectAnimator.ofFloat(this, "scaleX", 0f)
        val scaleYAnimator = ObjectAnimator.ofFloat(this, "scaleY", 0f)
        // 设置动画的持续时长为500毫秒
        scaleXAnimator.duration = 500
        scaleYAnimator.duration = 500
        // 为动画添加监听器，以便在动画结束时隐藏视图
        val animatorListener = object : Animator.AnimatorListener {
            override fun onAnimationStart(p0: Animator) {
            }

            override fun onAnimationEnd(p0: Animator) {
                firstLineText = ""
                secondLineText = ""
                stopMarquee()
                visibility = GONE
            }

            override fun onAnimationCancel(p0: Animator) {
            }

            override fun onAnimationRepeat(p0: Animator) {
            }

        }

        // 为缩放动画设置监听器
        scaleXAnimator.addListener(animatorListener)
        scaleYAnimator.addListener(animatorListener)

        scaleXAnimator.start()
        scaleYAnimator.start()
    }

    // 重写onDetachedFromWindow方法以在视图被销毁时停止动画
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        stopMarquee()
    }
}