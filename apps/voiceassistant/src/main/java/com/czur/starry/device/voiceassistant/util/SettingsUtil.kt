package com.czur.starry.device.voiceassistant.util

import android.content.Context
import android.content.Intent
import android.media.tv.tuner.frontend.IsdbtFrontendSettings.MODE_AUTO
import android.net.wifi.WifiManager
import android.os.PowerManager
import android.os.SystemProperties
import android.provider.Settings
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.hw.Q1Series
import com.czur.starry.device.baselib.common.hw.Q2Series
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.fw.proxy.SystemManagerProxy
import com.czur.starry.device.baselib.utils.prop.setSystemProp
import com.czur.starry.device.otalib.OTAHandler.systemTrackModeStatus
import com.czur.starry.device.voiceassistant.helper.Adjustable
import com.czur.starry.device.voiceassistant.helper.SettingAppHelper.TAG
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 *  author : WangHao
 *  time   :2025/02/26
 */


object SettingsUtil {
    private const val PIC_SIZE_MIN = 51
    private const val PIC_SIZE_MAX = 100
    private const val PIC_SIZE_LATENCY_TIME = 500L

    // 梯形矫正在设定中的key
    private const val KEY_AUTO_KEYSTONE = "settingAutoKeystone"
    private const val AUTO_KEYSTONE_ON = 1
    private const val AUTO_KEYSTONE_OFF = 0
    private const val PIC_SIZE_DEFAULT = 100

    private const val FOCUS_ACTION: String = "com.czur.starry.device.settings.startFocus"

    private const val KEY_AUTO_FOCUS = "settingAutoFocus"
    private const val AUTO_FOCUS_ON = 1
    private const val AUTO_FOCUS_OFF = 0
    private val forceAutoCorrectIntent: Intent = Intent("com.czur.keystone.ACTION.START").apply {
        `package` = "com.czur.keystone"
        putExtra("forceAutoCorrection", true)
    }
    private val systemManager by lazy { SystemManagerProxy() }

    private val showScreenTone: Boolean by lazy {
        systemManager.getDlp3439LooksMode() != SystemManagerProxy.LooksMode.LOOKS_MODE_NOT_SUPPORTED
    }
    private var pm: PowerManager? = null
    private var wifiManager: WifiManager? = null

    /*
   画面缩放直接设定property，底层已经实装好，并在500ms内自动刷新画面。
   设定范围51-100,默认100。。
   setprop persist.vendor.overscan.main "overscan 80,80,80,80"
    */
    private const val PIC_SIZE_VALUE_PREFIX = "overscan"
    private const val SYSTEM_KEY_PIC_SIZE = "persist.vendor.overscan.main"


    /**
     * 拾音设置
     */
    fun updateAGCLevel(agcLevel: SettingUtil.CameraAndMicSetting.AudioAlgoAGCLevel) {
        MainScope().launch {
            SettingUtil.CameraAndMicSetting.setAudioAlgoAGCLevel(agcLevel)
            delay(ONE_SECOND)
            systemTrackModeStatus = true
        }
    }

    /**
     * 噪声抑制
     */
    fun updateNSLevel(nsLevel: SettingUtil.CameraAndMicSetting.AudioAlgoNSLevel) {
        MainScope().launch {
            SettingUtil.CameraAndMicSetting.setAudioAlgoNSLevel(nsLevel)
            delay(ONE_SECOND)
            systemTrackModeStatus = true
        }
    }


    /**
     * 摄像头画面模式
     * @param mode
     */
    fun updateCameraMode(mode: SystemManagerProxy.TrackMode) {
        MainScope().launch {
            systemManager.setTrackMode(mode)
            delay(ONE_SECOND)
            systemTrackModeStatus = true
        }
    }

    /**
     * 办公模式
     * @param mode
     */
    fun updateDisplayMode(mode: SystemManagerProxy.LooksMode) {
        setScreenTone(mode)
    }


    /**
     * 画面大小
     */
    fun updateScreenSize(paramsRes: Adjustable = Adjustable.MAX, percent: Int = 0) {
        MainScope().launch {
            val newSize = if (percent == 0) {
                calculateNewSize(paramsRes)
            } else {
                percent
            }
            if (newSize in PIC_SIZE_MIN..PIC_SIZE_MAX) {
                setPicSizeAndHandleConsequences(newSize)
            } else {
                logTagE(TAG, "计算出的画面尺寸不在有效范围内: $newSize")
            }
            delay(ONE_SECOND)
            systemTrackModeStatus = true
        }
    }


    /**
     * 自动对焦
     */
    fun doAutoFocus(context: Context = CZURAtyManager.appContext) {
        val intent = Intent().apply {
            setPackage("com.hysd.hyscreen.VAF2S")
            setClassName("com.hysd.hyscreen", "com.hysd.hyscreen.VAF2S")
            setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            putExtra("mode", 73)
            putExtra("afswitch", 1)
            putExtra("keyswitch",0)
            putExtra("dmode", 0)
        }
        context.startService(intent)
    }

    /**
     * 设置自动对焦开关状态
     */
     fun setAutoFocusStatus(autoFocus: Boolean)  {
         MainScope().launch(Dispatchers.IO) {
             val focusStatus = if (autoFocus) AUTO_FOCUS_ON else AUTO_FOCUS_OFF
             Settings.Global.putInt(
                 CZURAtyManager.appContext.contentResolver,
                 KEY_AUTO_FOCUS,
                 focusStatus
             )
             if (Constants.starryHWInfo.series == Q2Series) {
                 // Q2 需要设置给华研
                 setSystemProp("persist.hysd.af.switch", focusStatus)
             }
         }
    }

    /**
     * 设置自动梯形矫正状态
     */
     fun setAutoKeystoneStatus(autoKeystone: Boolean)  {
         MainScope().launch(Dispatchers.IO) {
             val keystoneStatus = if (autoKeystone) AUTO_KEYSTONE_ON else AUTO_KEYSTONE_OFF
             Settings.Global.putInt(
                 CZURAtyManager.appContext.contentResolver,
                 KEY_AUTO_KEYSTONE, keystoneStatus
             )
             if (Constants.starryHWInfo.series == Q2Series) {
                 setSystemProp("persist.hysd.keystone.switch", keystoneStatus)
             }
         }
    }

    /**
     * 设置WIFI开关状态
     */
    fun setWifiStatus(wifiStatus: Boolean) {
        MainScope().launch(Dispatchers.Default) {
            wifiManager = CZURAtyManager.appContext.getSystemService(Context.WIFI_SERVICE) as WifiManager
            wifiManager?.setWifiEnabled(wifiStatus)
        }
    }


    private fun getPowerManager(): PowerManager {
        if (pm == null) {
            pm = CZURAtyManager.appContext
                .getSystemService<PowerManager>(PowerManager::class.java)
        }
        return pm!!
    }

    // 计算新尺寸的函数
    private suspend fun calculateNewSize(paramsRes: Adjustable): Int {
        val currentSize = getPicSize()
        return when (paramsRes) {
            Adjustable.ADD -> (currentSize * 1.1).toInt()
            Adjustable.SUB -> (currentSize * 0.9).toInt()
            Adjustable.MAX -> PIC_SIZE_MAX
            Adjustable.MIN -> PIC_SIZE_MIN
            else -> currentSize
        }
    }

    // 设置尺寸并处理后续操作的函数
    private suspend fun setPicSizeAndHandleConsequences(size: Int) {
        if (size in PIC_SIZE_MIN..PIC_SIZE_MAX) {
            val valueStr = "$PIC_SIZE_VALUE_PREFIX $size,$size,$size,$size"
            try {
                SystemProperties.set(SYSTEM_KEY_PIC_SIZE, valueStr)
                if (getAutoKeystoneStatus()) {
                    logTagV(TAG, "自动梯形校正开启,触发一次梯形校正")
                    doKeystone()
                }
                delay(PIC_SIZE_LATENCY_TIME)
            } catch (e: Exception) {
                logTagE(TAG, "设置画面尺寸错误($size):", tr = e)
            }
        }

    }

    /**
     * 执行一次梯形校正
     */
    private fun doKeystone(context: Context = CZURAtyManager.appContext) {
        context.startService(forceAutoCorrectIntent)
    }

    /**
     * 获取自动梯形矫正状态
     */
    private suspend fun getAutoKeystoneStatus() = withContext(Dispatchers.IO) {
        val keystoneStatus = Settings.Global.getInt(
            CZURAtyManager.appContext.contentResolver,
            KEY_AUTO_KEYSTONE,
            AUTO_KEYSTONE_ON
        )
        keystoneStatus == AUTO_KEYSTONE_ON
    }


    /**
     * 获取画面尺寸
     * @return 画面尺寸百分比
     */
    private suspend fun getPicSize(): Int = withContext(Dispatchers.IO) {
        val picSizeValue = SystemProperties.get(SYSTEM_KEY_PIC_SIZE)
        parsePisSizeStr(picSizeValue)
    }

    /**
     * 解析系统画面尺寸的值
     * 格式为:overscan 80,80,80,80
     */
    private fun parsePisSizeStr(picSizeStr: String): Int {
        val regex = "[1-9][0-9]*".toRegex()
        val result = regex.find(picSizeStr)?.value
        // 解析失败,返回默认值
        return result?.toInt() ?: PIC_SIZE_DEFAULT
    }


    private fun setScreenTone(mode: SystemManagerProxy.LooksMode) {
        if (showScreenTone.not()) {
            logTagI(TAG, "不支持显示模式设置")
            return
        }
        MainScope().launch {
            if (systemManager.getDlp3439LooksMode() == mode) {
                logTagW(TAG, "显示模式没有改变, 不重新设置")

            } else {
                logTagD(TAG, "设置显示模式:${mode} ")
                val result = systemManager.saveDlp3439Looks(1000, mode)
            }
        }
    }
}