package com.czur.starry.device.voiceassistant.manager

import androidx.lifecycle.LifecycleOwner
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.voiceassistant.common.Constants.VOICE_ACTIVITY_DETECTED
import com.czur.starry.device.voiceassistant.entity.CommandEvent
import com.czur.starry.device.voiceassistant.entity.CommandEventHandler
import com.czur.starry.device.voiceassistant.view.WindowUI
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.channels.consumeEach
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import org.json.JSONObject
import java.util.concurrent.atomic.AtomicReference

/**
 *  author : <PERSON><PERSON><PERSON>
 *  time   :2025/03/31
 */
private const val TAG = "AmpProcessor"
class AmpProcessor(
    private val lifecycleOwner: LifecycleOwner,
    private val windowUI: WindowUI,
    private val eventHandler: CommandEventHandler
) {
    // 异步处理管道配置
    private val rawAmpChannel = Channel<Float>(Channel.UNLIMITED)
    private val processedAmpChannel = Channel<Float>(Channel.UNLIMITED)

    // 动态校准参数
    private val minAmp = AtomicReference(0f)
    private val maxAmp = AtomicReference(1f)

    private val voiceThreshold = 0.05f //语音检测阈值

    // 数据聚合窗口
    private val buffer = ArrayDeque<Float>()
    private val bufferLock = Mutex()

    init {
        startProcessingPipeline()
    }

    // 启动异步处理流水线
    private fun startProcessingPipeline() {
        lifecycleOwner.launch(Dispatchers.Default) {
            // 第一阶段：原始数据预处理
            launch {
                rawAmpChannel.consumeEach { rawValue ->
                    processRawValue(rawValue)
                }
            }

            // 第二阶段：聚合处理
            launch {
                while (isActive) {
                    delay(ONE_SECOND) // 1秒间隔
                    bufferLock.withLock {
                        if (buffer.isNotEmpty()) {
                            val average = buffer.average().toFloat()
                            buffer.clear()
                            processedAmpChannel.send(average)
                        }
                    }
                }
            }

            // 第三阶段：阈值判断与分发
            launch {
                processedAmpChannel.consumeEach { normalizedValue ->
                    handleNormalizedValue(normalizedValue)
                }
            }
        }
    }

    // JSON解析入口
    fun onRawJsonData(json: String) {
        lifecycleOwner.launch(Dispatchers.IO) {
            try {
                logTagD(TAG, "======json================$json")
                val ampValue = parseJsonToAmp(json)
                rawAmpChannel.send(ampValue)
            } catch (e: Exception) {
                logTagE("AmpProcessor", "JSON解析失败 $e")
            }
        }
    }

    // JSON解析实现
    private fun parseJsonToAmp(json: String): Float {
        val jsonObject = JSONObject(json)
        return jsonObject.getDouble("audioAmp").toFloat().also {
            if (it < 0) throw IllegalArgumentException("振幅值不能为负")
        }
    }

    // 原始数据处理（含动态校准）
    private suspend fun processRawValue(raw: Float) {
        // 动态更新极值
        minAmp.getAndUpdate { minOf(it, raw) }
        maxAmp.getAndUpdate { maxOf(it, raw) }

        // 缓冲存储
        bufferLock.withLock {
            buffer.addLast(normalize(raw))
        }
    }

    // 归一化处理
    private fun normalize(raw: Float): Float {
        val currentMin = minAmp.get()
        val currentMax = maxAmp.get()
        return if (currentMax - currentMin < 0.0001f) {
            0f // 避免除零错误
        } else {
            (raw - currentMin) / (currentMax - currentMin)
        }
    }

    // 阈值处理逻辑
    private fun handleNormalizedValue(normalized: Float) {
        logTagD(TAG, "===========handleNormalizedValue===========$normalized")
        if (normalized > voiceThreshold) {
            eventHandler.handleCommand(
                CommandEvent(
                    VOICE_ACTIVITY_DETECTED,
                    amplitude = normalized + 1
                )
            )
        }
    }


    // 校准接口
    fun calibrateMicrophone(backgroundNoiseSamples: List<Float>) {
        lifecycleOwner.launch(Dispatchers.Default) {
            val noiseFloor = backgroundNoiseSamples.average().toFloat()
            minAmp.set(noiseFloor * 0.9f) // 保留10%安全余量
            maxAmp.set(noiseFloor * 5f)   // 初始假设最大值为噪声5倍
        }
    }
}
