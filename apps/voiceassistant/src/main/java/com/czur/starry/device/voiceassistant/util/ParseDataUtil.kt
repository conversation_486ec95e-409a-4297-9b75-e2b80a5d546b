package com.czur.starry.device.voiceassistant.util

import android.security.keymaster.OperationResult
import com.czur.czurutils.log.logTagE
import com.czur.starry.device.baselib.data.provider.StarryDataProvider
import com.czur.starry.device.baselib.data.provider.StarryDataProvider.gson
import com.czur.starry.device.voiceassistant.entity.AmpData
import com.czur.starry.device.voiceassistant.entity.AudioMetaData
import com.czur.starry.device.voiceassistant.entity.ChatMsgData
import com.czur.starry.device.voiceassistant.entity.InstructionResponse
import com.czur.starry.device.voiceassistant.entity.TtsMsgData
import com.czur.starry.device.voiceassistant.entity.WakeUpResult
import com.google.gson.Gson
import kotlinx.serialization.json.Json

/**
 *  author : WangHao
 *  time   :2025/03/13
 */


object ParseDataUtil {

    private val gson by lazy { G<PERSON>() }
    /***
     * 解析唤醒结果
     * @param result
     * @return
     */
    fun parseWakeUpResult(result: String?): WakeUpResult? {
        return try {
            parseJsonSafe(result, gson)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 解析指令
     * @param result
     * @return
     */
    fun parseInstruct(result: String): InstructionResponse? {
        return try {
            parseJsonSafe(result, gson)
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 解析AudioMetaData
     * @param result
     * @return
     */
    fun parseAudioMetaData(result: String?): AudioMetaData? {
        return try {
            parseJsonSafe(result, gson)
        } catch (e: Exception) {
            logTagE("ParseDataUtil", "parseAudioMetaData:${e.message}")
            null
        }
    }

    /**
     * 解析ChatMsg
     * @param result
     * @return
     */
     fun parseChatMsg(result: String?): ChatMsgData? {
        return try {
            parseJsonSafe(result, gson)
        } catch (e: Exception) {
            logTagE("ParseDataUtil", "parseChatMsg:${e.message}")
            null
        }
    }

    /**
     * 解析TtsMsg
     * @param result
     * @return
     */
    fun parseTtsMsg(result: String?): TtsMsgData? {
        return try {
            parseJsonSafe(result, gson)
        } catch (e: Exception) {
            logTagE("ParseDataUtil", "parseChatMsg:${e.message}")
            null
        }
    }

    /**
     * 解析onMicAmp
     */
    fun parseMicAmp(result: String?): AmpData? {
        return try {
            parseJsonSafe(result, gson)
        } catch (e: Exception) {
            logTagE("ParseDataUtil", "parseMicAmp:${e.message}")
            null
        }
    }

    private inline fun <reified T> parseJsonSafe(
        json: String?,
        gson: Gson = Gson(),
        onError: (Throwable) -> Unit = { e ->
           logTagE("ParseDataUtil","解析 ${T::class.simpleName} 失败: ${e.message}")
        }
    ): T? {
        if (json.isNullOrEmpty()) return null
        return try {
            gson.fromJson(json, T::class.java)
        } catch (e: Exception) {
            onError(e)
            null
        }
    }

}

