package com.czur.starry.device.voiceassistant.helper

import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_BYOM_GUIDE
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_FILE
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_NOTICE_SETTING
import com.czur.starry.device.baselib.common.BootParam.BOOT_KEY_PAGE_MENU_NAME
import com.czur.starry.device.baselib.common.BootParam.BOOT_KEY_PAGE_MENU_NAVIGATE
import com.czur.starry.device.file.filelib.FileHandlerLive.unReadFileName
import com.czur.starry.device.voiceassistant.R
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 *  author : Wang<PERSON>ao
 *  time   :2025/02/10
 *  打开并执行功能
 */


class LaunchForNavigate(private val context: Context, private val packageManager: PackageManager) {

    /**
     * 打开应用并执行
     */
    fun launchAndNavigate(appName: String, params: String = "") {
        val launchMap = mapOf(
            context.getString(R.string.app_name_file) to {
                if (params == context.getString(R.string.voice_command_recent_file)) {
                    startAppByAction(
                        ACTION_BOOT_FILE,
                        context.getString(R.string.file_folders_czur),
                        navigate = "recent"
                    )
                } else {
                    startAppByAction(
                        ACTION_BOOT_FILE,
                        context.getString(R.string.file_folders_czur),
                        navigate = params
                    )
                }
            },
            context.getString(R.string.app_name_byom) to {
                startAppByAction(
                    ACTION_BOOT_BYOM_GUIDE,
                    navigate = params
                )
            },
            context.getString(R.string.app_name_local_record) to {
                launchAppByName(appName, params, params)
            },
            context.getString(R.string.voice_favorite_app) to {
                startAppByAction(
                    ACTION_BOOT_NOTICE_SETTING,
                    context.getString(R.string.voice_favorite_app),
                    params
                )
            },
            context.getString(R.string.app_name_settings) to {
                when (params) {
                    context.getString(R.string.voice_reboot_wifi) ->
                        startAppByAction(
                            ACTION_BOOT_NOTICE_SETTING,
                            context.getString(R.string.page_menu_wifi),
                            context.getString(R.string.voice_reboot_wifi)
                        )

                    context.getString(R.string.voice_change_wallpaper) ->
                        startAppByAction(
                            ACTION_BOOT_NOTICE_SETTING,
                            context.getString(R.string.page_menu_wallpaper),
                            context.getString(R.string.voice_change_wallpaper)
                        )

                    context.getString(R.string.voice_change_recovery) ->
                        startAppByAction(
                            ACTION_BOOT_NOTICE_SETTING,
                            context.getString(R.string.page_menu_clear_data),
                            context.getString(R.string.voice_change_recovery)
                        )
                    context.getString(R.string.voice_change_feedback_log_submit) ->
                        startAppByAction(
                            ACTION_BOOT_NOTICE_SETTING,
                            context.getString(R.string.voice_change_feedback_log),
                            context.getString(R.string.voice_change_feedback_log_submit)
                        )
                }
            }
        )

        launchMap[appName]?.invoke()
    }

    private fun launchAppByName(appName: String, pageMenu: String = "", navigate: String = "") {
        val packages = packageManager.getInstalledApplications(PackageManager.GET_META_DATA)
        val matchingPackages = packages.filter {
            packageManager.getApplicationLabel(it) == appName && it.packageName.contains("czur")
        }
        val intent = packageManager.getLaunchIntentForPackage(matchingPackages.first().packageName)
        if (intent != null) {
            if (navigate.isNotEmpty()) {
                intent.putExtra(BOOT_KEY_PAGE_MENU_NAME, pageMenu)
            }
            if (navigate.isNotEmpty()) {
                intent.putExtra(BOOT_KEY_PAGE_MENU_NAVIGATE, navigate)
            }
            intent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP)
            context.startActivity(intent)
        }
    }

    /**
     * 通过action打开应用
     */
    private fun startAppByAction(
        bootAction: String,
        pageMenu: String = "",
        navigate: String = ""
    ) {
        val intent = Intent().apply {
            action = bootAction
            if (pageMenu.isNotEmpty()) {
                putExtra(BOOT_KEY_PAGE_MENU_NAME, pageMenu)
            }
            if (navigate.isNotEmpty()) {
                putExtra(BOOT_KEY_PAGE_MENU_NAVIGATE, navigate)
            }
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }
        context.startActivity(intent)
    }
}