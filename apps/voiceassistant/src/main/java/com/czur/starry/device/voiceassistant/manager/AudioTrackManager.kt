package com.czur.starry.device.voiceassistant.manager

import android.content.Context
import android.media.AudioFormat
import android.media.AudioManager
import android.media.AudioTrack
import androidx.lifecycle.AtomicReference
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.starry.device.voiceassistant.VoiceAssistantService
import com.czur.starry.device.voiceassistant.entity.MessageData
import com.czur.starry.device.voiceassistant.entity.TtsMessageData
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.channels.consumeEach
import kotlinx.coroutines.ensureActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import okio.IOException
import java.io.File
import java.io.FileInputStream
import java.util.concurrent.CancellationException
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger

/**
 *  author : WangHao
 *  time   :2025/03/27
 */


object AudioTrackManager {
    private const val TAG = "AudioTrackManager"

    private const val BUFFER_SIZE = 1024
    private val mutex = Mutex()
    private var playbackJob: Job? = null
    val ttsAudioQueue = AtomicReference<Channel<ByteArray>>(Channel(Channel.UNLIMITED))
    val asrQueue = AtomicReference<Channel<MessageData>>(Channel(Channel.UNLIMITED))
    val ttsMessageQueue = AtomicReference<Channel<TtsMessageData>>(Channel(Channel.UNLIMITED))
    val hasChatMsgData = AtomicBoolean(false) // 用于判断是否有tts消息数据返回

    //判断当前状态
    suspend fun isAudioPlaying(): Boolean {
        return mutex.withLock {
            !ttsAudioQueue.get().isEmpty
        }
    }


    //停止播放
    private fun stopPlayTTS() {
        // 2. 关闭旧 Channel
        val oldTtsQueue = ttsMessageQueue.get()
        oldTtsQueue.close(CancellationException("Channel closed by stop"))
        // 3. 原子替换新 Channel
        ttsMessageQueue.set(Channel(Channel.UNLIMITED))

        val oldStringQueue = asrQueue.get()
        oldStringQueue.close(CancellationException("Channel closed by stop"))
        // 3. 原子替换新 Channel
        asrQueue.set(Channel(Channel.UNLIMITED))

        // 1. 立即终止播放协程
        playbackJob?.cancel() // 触发协程取消
        playbackJob = null

        // 2. 关闭旧 Channel
        val oldChannel = ttsAudioQueue.get()
        oldChannel.close(CancellationException("Channel closed by stop"))
        // 3. 原子替换新 Channel
        ttsAudioQueue.set(Channel(Channel.UNLIMITED))
    }

    fun stopPlaybackSafely() {
        CoroutineScope(Dispatchers.Default).launch {
            stopPlayTTS()
        }
    }

    // 播放实时数据流（TTS 使用）
    fun playTtsStream() {
        playbackJob = CoroutineScope(Dispatchers.IO).launch {
          val audioTrack = createAudioTrack().apply { play() }
            try {
                ttsAudioQueue.get().consumeEach { chunk ->
                    audioTrack?.write(chunk, 0, chunk.size)
                    ensureActive()  // 及时响应取消
                }
            } catch (e: Exception) {
                logTagE(TAG, "Playback error: ${e.message}")
            } finally {
                // 检查 AudioTrack 状态，确保可以停止和释放
                if (audioTrack.state == AudioTrack.STATE_INITIALIZED) {
                    if (audioTrack.playState == AudioTrack.PLAYSTATE_PLAYING) {
                        audioTrack.stop()
                    }
                    audioTrack.release()
                }
            }
        }
    }

    /**
     * 播放预加载音频
     */
    suspend fun playPcmFile(file: File) = withContext(Dispatchers.IO){

        val audioTrack = createAudioTrack()
        try {
            val buffer = ByteArray(file.length().toInt())
            FileInputStream(file).use { fis ->
                fis.read(buffer)
            }

            audioTrack.play()
            audioTrack.write(buffer, 0, buffer.size)
        } catch (e: IOException) {
            logTagE(VoiceAssistantService.TAG, "File read error: ${e.message}")
        } finally {
            audioTrack.stop()
            audioTrack.release() // 释放资源‌
        }
    }

    // 播放 Raw 资源
    suspend fun playResource(context: Context, resId: Int) = withContext(Dispatchers.IO){
        val audioTrack = createAudioTrack()
        audioTrack.play()
        try {
            context.resources.openRawResource(resId).use { inputStream ->
                val buffer = ByteArray(BUFFER_SIZE)
                var read: Int
                while (inputStream.read(buffer).also { read = it } != -1) {
                    audioTrack.write(buffer, 0, read)
                }
            }
        } finally {
            audioTrack.stop()
            audioTrack.release()
        }
    }

    private fun createAudioTrack(): AudioTrack {
        val sampleRate = 16000
        val channelConfig = AudioFormat.CHANNEL_OUT_MONO
        val audioFormat = AudioFormat.ENCODING_PCM_16BIT

        val bufferSize = AudioTrack.getMinBufferSize(
            sampleRate, channelConfig, audioFormat
        ) * 2

        val audioTrack = AudioTrack(
            AudioManager.STREAM_MUSIC,
            sampleRate, channelConfig, audioFormat,
            bufferSize, AudioTrack.MODE_STREAM
        )
        return audioTrack
    }
}