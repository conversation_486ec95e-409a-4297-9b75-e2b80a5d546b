package com.czur.starry.device.voiceassistant.manager

import android.content.Context
import android.content.res.AssetFileDescriptor
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import com.czur.starry.device.baselib.utils.getString
import com.czur.starry.device.voiceassistant.R
import com.czur.starry.device.voiceassistant.app.AssistantApp
import com.czur.starry.device.voiceassistant.entity.LocalPreItem
import com.google.gson.Gson
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import java.io.IOException

/**
 *  author : WangHao
 *  time   :2025/03/11
 */


object PreDataManager {
    private val dataStore by lazy {
        AssistantApp.instance.dataStore
    }
    private val LOCAL_VERSION_KEY = stringPreferencesKey("local_preload_version")
    private val LOCAL_PRELOAD_KEY = stringPreferencesKey("local_preload_items")
    private val gson by lazy { Gson() }

    /**
     * 保存预加载数据到本地
     */
    suspend fun saveLocalPreloadItems(items: List<LocalPreItem>) {
        val json = gson.toJson(items)
        dataStore.edit { preferences ->
            preferences[LOCAL_PRELOAD_KEY] = json
        }
    }

    /**
     * 获取本地预加载数据
     */
    suspend fun getLocalPreloadItems(): List<LocalPreItem>? {
        try {
            val json = dataStore.data.map { preferences ->
                preferences[LOCAL_PRELOAD_KEY]
            }.first()
            return gson.fromJson(json, Array<LocalPreItem>::class.java).toList()
        }catch (e:Exception){
            e.printStackTrace()
            return null
        }

    }

    /**
     * 获取本地预加载版本号
     */
    suspend fun getLocalPreVersion(): String? {
        return dataStore.data.map { preferences ->
            preferences[LOCAL_VERSION_KEY]
        }.first()
    }

    /**
     * 保存本地预加载版本号
     */
    suspend fun saveLocalPreVersion(version: String) {
        dataStore.edit { preferences ->
            preferences[LOCAL_VERSION_KEY] = version
        }
    }
}