package com.czur.starry.device.localmeetingrecord.monitor.audio.record

import android.media.MediaCodec
import android.media.MediaFormat
import android.media.MediaMuxer
import com.czur.czurutils.log.logI
import com.czur.starry.device.localmeetingrecord.MainActivity
import java.io.File
import java.nio.ByteBuffer

/**
 * Created by 陈丰尧 on 2024/10/24
 */
class CZMicAudioMuxer {
    private var mediaMuxer: MediaMuxer? = null

    private var videoTrackIndex = -1

    val hasAudioTrack:<PERSON>olean
        get() = videoTrackIndex != -1

    fun prepare(outputFile: File) {
        videoTrackIndex = -1
        mediaMuxer =
            MediaMuxer(outputFile.absolutePath, MediaMuxer.OutputFormat.MUXER_OUTPUT_MPEG_4)
    }

    fun addAudioTrack(mediaFormat: MediaFormat) {
        videoTrackIndex = mediaMuxer?.addTrack(mediaFormat) ?: -1
        if (videoTrackIndex != -1) {
            mediaMuxer?.start()
        }
    }

    /**
     * 写入音频数据
     */
    fun writeAudioData(buffer: ByteBuffer, bufferInfo: MediaCodec.BufferInfo) {
        mediaMuxer?.writeSampleData(videoTrackIndex, buffer, bufferInfo)
    }

    private val TAG = "CZMicAudioMuxer"
    fun stopAndRelease(){
        logI(TAG, "stopRecord--stopAndRelease---start")
        mediaMuxer?.stop()
        mediaMuxer?.release()
        logI(TAG, "stopRecord--stopAndRelease---end")
        videoTrackIndex = -1
    }
}