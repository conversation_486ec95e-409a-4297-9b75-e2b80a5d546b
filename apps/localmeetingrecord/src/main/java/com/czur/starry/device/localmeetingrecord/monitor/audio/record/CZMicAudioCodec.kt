package com.czur.starry.device.localmeetingrecord.monitor.audio.record

import android.media.AudioFormat
import android.media.MediaCodec
import android.media.MediaCodecInfo
import android.media.MediaFormat
import android.os.SystemClock
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.localmeetingrecord.Config
import com.czur.starry.device.localmeetingrecord.monitor.PtsManager
import java.io.BufferedOutputStream
import java.io.File
import java.nio.ByteBuffer
import java.util.concurrent.ArrayBlockingQueue
import kotlin.concurrent.thread

/**
 * Created by 陈丰尧 on 2024/10/23
 */
private const val TAG = "CZMicAudioRecord"

class CZMicAudioCodec(
    private val ptsManager: CZMicAudioPtsManager
) {
    private val POISON_PILL = ByteArray(0)
    private var audioCodec: MediaCodec? = null
    private var bufferInfo: MediaCodec.BufferInfo? = null

    // 数据缓冲队列
    private val bufferQueue = ArrayBlockingQueue<ByteArray>(10)
    private var audioBytePerSample = 0
    private var encodedSize = 0L

    private var waitForFinish = false

    private val micAudioMuxer = CZMicAudioMuxer()

    fun prepare(outputFile: File) {
        val encodeFormat = MediaFormat.createAudioFormat(
            MediaFormat.MIMETYPE_AUDIO_AAC,
            Config.AUDIO_SAMPLE_RATE,
            1
        )

        val bitDepth = when (Config.AUDIO_FORMAT) {
            AudioFormat.ENCODING_PCM_8BIT -> 8
            AudioFormat.ENCODING_PCM_16BIT -> 16
            AudioFormat.ENCODING_PCM_32BIT -> 32
            else -> 16
        }
        waitForFinish = false
        audioBytePerSample = 1 * (bitDepth / 8)
        encodedSize = 0
        bufferInfo = MediaCodec.BufferInfo()

        encodeFormat.setInteger(MediaFormat.KEY_BIT_RATE, Config.AUDIO_ENCODING_BIT_RATE)
        encodeFormat.setInteger(
            MediaFormat.KEY_AAC_PROFILE,
            MediaCodecInfo.CodecProfileLevel.AACObjectLC
        )
        encodeFormat.setInteger(MediaFormat.KEY_MAX_INPUT_SIZE, 8192)
        audioCodec = MediaCodec.createEncoderByType(MediaFormat.MIMETYPE_AUDIO_AAC)
        audioCodec?.configure(encodeFormat, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE)
        audioCodec?.start()


        micAudioMuxer.prepare(outputFile)

        thread {
            while (true) {
                val data = bufferQueue.take()
                if (data === POISON_PILL) {
                    waitForFinish = true
                    break
                }
                logTagD(TAG, "encodeStart")
                encodeData(data)
                logTagD(TAG, "encodeFinish")
            }
        }

        bootOutputThread()
    }

    fun putData(data: ByteArray) {
        bufferQueue.put(data)
    }

    fun stop() {
        logTagV(TAG, "stop")
        bufferQueue.put(POISON_PILL)
    }

    private fun release() {
        micAudioMuxer.stopAndRelease()
        audioCodec?.stop()
        audioCodec?.release()
        audioCodec = null
        bufferInfo = null
        logTagD(TAG, "releaseFinish")
    }

    private fun encodeData(data: ByteArray) {
        val audioEncoder = audioCodec ?: return
        val inputBufIndex = audioEncoder.dequeueInputBuffer(-1)
//        logTagD(TAG, "inputBufIndex: $inputBufIndex")
        if (inputBufIndex >= 0) {
            val inputBuffer = audioEncoder.getInputBuffer(inputBufIndex) ?: return
            inputBuffer.clear()
            inputBuffer.put(data)
            audioEncoder.queueInputBuffer(inputBufIndex, 0, data.size, ptsManager.getPts(), 0)
            encodedSize += data.size.toLong()
        }

    }

    private fun bootOutputThread() {
        val info = bufferInfo ?: return
        val audioEncoder = audioCodec ?: return

        thread {
            while (!waitForFinish) {
                val outputBufIndex = audioEncoder.dequeueOutputBuffer(info, 1000)
                if (outputBufIndex == MediaCodec.INFO_TRY_AGAIN_LATER) {
                    continue
                } else if (outputBufIndex == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED) {
                    micAudioMuxer.addAudioTrack(audioEncoder.outputFormat)
                    continue
                } else if (outputBufIndex >= 0) {
                    logTagD(TAG, "outputBufIndex: $outputBufIndex")

                    if (info.size != 0 && micAudioMuxer.hasAudioTrack) {
                        val outBitSize = info.size
                        val outputBuffer = audioEncoder.getOutputBuffer(outputBufIndex) ?: continue
                        outputBuffer.position(info.offset)
                        outputBuffer.limit(info.offset + outBitSize)

                        micAudioMuxer.writeAudioData(outputBuffer, info)
                        outputBuffer.clear()
                    }
                    audioEncoder.releaseOutputBuffer(outputBufIndex, false)
                }
            }
            logTagD(TAG, "outputThreadFinish")
            release()
        }
    }
}