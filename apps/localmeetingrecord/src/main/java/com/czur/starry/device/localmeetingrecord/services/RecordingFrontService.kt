package com.czur.starry.device.localmeetingrecord.services

import android.content.Intent
import android.os.Binder
import android.os.IBinder
import androidx.core.app.NotificationCompat
import androidx.lifecycle.LifecycleService
import com.czer.starry.device.meetlib.MeetingHandler
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.createNotificationChannel
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.localmeetingrecord.R
import com.czur.starry.device.sharescreen.esharelib.SimpleEShareCallback
import com.eshare.serverlibrary.api.EShareServerSDK
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay

/**
 * 前台服务,用于
 * 监听app当前显示状态
 */
class RecordingFrontService : LifecycleService() {
    private val TAG = "RecordingFrontService"

    companion object {
        private const val CHANNEL_ID = "LocalMeetingRecordChannel"
        private const val SHOW_MEETING_WINDOW_DELAY = ONE_SECOND

    }
    var byomIsPairing = false
    val eShareSDK = EShareServerSDK.getSingleton(CZURAtyManager.appContext)
    private var mClientCount = 0
    private var notifyPairStateJob: Job? = null



    private val eShareCallback = object : SimpleEShareCallback() {
        override fun notifyPairState(pairState: Int) {
            super.notifyPairState(pairState)
            // 如果在2和3之间,调用了request 不处理
            if (pairState == 2) {
                byomIsPairing = true
                notifyPairStateJob = launch {
                     delay(5000)
                    byomIsPairing = false
                 }
            } else {
                notifyPairStateJob?.cancel()
                byomIsPairing = false
            }
        }

        override fun showRequestAlert(clientIp: String?, clientName: String?, clientType: Int) {

            if (!MeetingHandler.localMeetingRecordCanEShare) {
                logTagD(TAG, "会议录制中,不允许投屏")
                toast(getString(R.string.dialog_refuse_eshare))
                //会议录制中,不允许投屏
            }
        }
    }

    private val mBinder: IBinder = RecordBinder()

    var mAppVisibleChangedListener: AppVisibleChangedListener? = null

    override fun onCreate() {
        super.onCreate()
        logTagD(TAG, "onCreate")
        eShareSDK.registerCallback(eShareCallback)

        mkForeground()
        watchAppState()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)
        return START_NOT_STICKY // 不需要被kill后重建Service
    }

    override fun onBind(intent: Intent): IBinder {
        super.onBind(intent)
        mClientCount++
        return mBinder
    }

    /**
     * 监控App的状态
     */
    private fun watchAppState() {
        launch {
            CZURAtyManager.czurAppStateFlow.collect { state ->
                logTagI(TAG, "MeetingAppState:${state}")
                when (state) {
                    CZURAtyManager.CZURAppState.VISIBLE -> doWhenAppVisible()
                    CZURAtyManager.CZURAppState.INVISIBLE -> doWhenAppInvisible()
                    else -> logTagI(TAG, "MeetingAppState:${state}")
                }
            }
        }
    }

    /**
     * 当App可见
     */
    private fun doWhenAppVisible() {
        mAppVisibleChangedListener?.onAppVisibleChanged(true)
    }

    /**
     * 当App不可见
     */
    private fun doWhenAppInvisible() {
        mAppVisibleChangedListener?.onAppVisibleChanged(false)
    }

    override fun onDestroy() {
        super.onDestroy()
        eShareSDK.unregisterCallback(eShareCallback)
    }

    override fun onUnbind(intent: Intent?): Boolean {
        mClientCount--
        return super.onUnbind(intent)
    }

    fun isServiceAlive(): Boolean {
        return mClientCount > 0
    }

    /**
     * 变成前台服务
     */
    private fun mkForeground() {
        createNotificationChannel(CHANNEL_ID, CHANNEL_ID)
        val notification: NotificationCompat.Builder = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("LocalMeeting Recording")
            .setSmallIcon(R.drawable.baselib_icon_msg)
            .setOnlyAlertOnce(true)
            .setOngoing(true)
        startForeground(1, notification.build())
    }

    inner class RecordBinder : Binder() {
        val recordService: RecordingFrontService
            get() = this@RecordingFrontService

    }

    interface AppVisibleChangedListener {
        fun onAppVisibleChanged(vis: Boolean)
    }
}