<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="PxUsage">
    <!--    从中间弹出:-->
    <style name="AnimCenter" parent="@android:style/Animation.Dialog">
        <item name="android:windowEnterAnimation">@anim/dialog_center_in</item>
        <item name="android:windowExitAnimation">@anim/dialog_center_out</item>
    </style>

    <style name="user_default_dialog" parent="@style/Theme.AppCompat.Dialog">
        <item name="android:windowFrame">@android:color/transparent</item>
        <!-- 边框 -->
        <item name="android:windowIsFloating">true</item>
        <!-- 是否浮现在activity之上 -->
        <item name="android:windowIsTranslucent">true</item>
        <!-- 半透明 -->
        <item name="android:windowNoTitle">true</item>
        <!-- 无标题 -->
        <item name="android:background">@drawable/bg_global_mark_dialog_corner</item>
        <!-- 背景色 -->
        <item name="android:windowBackground">@color/white</item>
        <!-- 背景透明 -->
        <item name="android:backgroundDimEnabled">true</item>
        <!-- 模糊 -->
        <item name="android:windowFullscreen">false</item>
        <!-- 全屏 -->
    </style>

    <style name="TVMainRightOtpItem">
        <item name="android:layout_width">0px</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center</item>
        <item name="android:minHeight">40px</item>
        <item name="android:paddingHorizontal">20px</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">24px</item>
        <item name="android:textStyle">bold</item>
    </style>
</resources>