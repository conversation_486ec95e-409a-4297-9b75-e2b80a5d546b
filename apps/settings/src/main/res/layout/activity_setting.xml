<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_main"
    tools:context=".SettingActivity">

    <View
        android:id="@+id/menuBgView"
        android:layout_width="400px"
        android:layout_height="match_parent"
        android:background="@drawable/bg_menu"
        app:layout_constraintLeft_toLeftOf="parent" />

    <com.czur.uilib.CZTitleBar
        android:id="@+id/titleBar"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        app:baselib_titlebar_title="@string/app_name"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="@id/menuBgView"
        app:layout_constraintTop_toTopOf="parent" />


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/menuRv"
        android:layout_width="0px"
        android:layout_height="0px"
        android:overScrollMode="never"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="@id/menuBgView"
        app:layout_constraintTop_toBottomOf="@id/titleBar" />

    <FrameLayout
        android:id="@+id/container"
        android:layout_width="0px"
        android:layout_height="0px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@+id/menuRv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>