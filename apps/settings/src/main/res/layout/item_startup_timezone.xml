<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="100px"
    tools:background="@color/base_bg_color"
    tools:ignore="PxUsage,RtlHardcoded,RtlSymmetry"
    tools:viewBindingIgnore="true">

    <View
        android:id="@+id/clickBgView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

    <Space
        android:id="@+id/leftSpace"
        android:layout_width="30px"
        android:layout_height="match_parent"/>

    <TextView
        android:id="@+id/timeZoneIndexTv"
        android:layout_width="30px"
        android:layout_height="30px"
        android:gravity="center"
        android:includeFontPadding="false"
        android:textColor="@color/white"
        android:textSize="20px"
        android:textStyle="bold"
        app:bl_corners_radius="15px"
        app:bl_solid_color="#33FFFFFF"
        app:layout_constraintBottom_toBottomOf="@id/timeZoneNameTv"
        app:layout_constraintLeft_toRightOf="@id/leftSpace"
        app:layout_constraintTop_toTopOf="@id/timeZoneNameTv"
        tools:background="#33FFFFFF"
        tools:text="A" />

    <View
        android:id="@+id/leftClickView"
        android:layout_width="45px"
        android:layout_height="match_parent"
        app:layout_constraintLeft_toRightOf="@id/leftSpace" />

    <TextView
        android:id="@+id/timeZoneNameTv"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginTop="15px"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:includeFontPadding="false"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintLeft_toRightOf="@id/leftClickView"
        app:layout_constraintRight_toLeftOf="@id/timeZoneSelIv"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="哥斯达黎加" />

    <TextView
        android:id="@+id/timeZoneGMTTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="15px"
        android:includeFontPadding="false"
        android:textSize="20px"
        android:textStyle="normal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/leftClickView"
        tools:text="东八区" />

    <ImageView
        android:id="@+id/timeZoneSelIv"
        android:layout_width="31px"
        android:layout_height="wrap_content"
        android:layout_marginEnd="30px"
        android:paddingLeft="5px"
        android:src="@drawable/ic_select"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>