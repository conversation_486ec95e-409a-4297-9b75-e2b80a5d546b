<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/white"
    tools:ignore="PxUsage,RtlHardcoded">

    <com.czur.starry.device.settings.widget.SettingItemBg
        android:id="@+id/bgItem"
        android:layout_width="900px"
        android:layout_height="252px"
        app:ignoreHover="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:position="single" />

    <TextView
        android:id="@+id/titleItemTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30px"
        android:layout_marginTop="30px"
        android:text="@string/label_ai_assistant_voice_wakeup"
        android:textColor="@color/text_common"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="@id/bgItem"
        app:layout_constraintTop_toTopOf="@id/bgItem" />

    <com.czur.uilib.CZSwitch
        android:id="@+id/totalSwitch"
        android:layout_width="95px"
        android:layout_height="45px"
        android:layout_marginRight="30px"
        app:layout_constraintRight_toRightOf="@id/bgItem"
        app:layout_constraintTop_toTopOf="@id/titleItemTv" />

    <TextView
        android:id="@+id/hintItemTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="40px"
        android:text="@string/label_ai_assistant_voice_wakeup_hint"
        android:textColor="@color/text_common"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="@id/titleItemTv"
        app:layout_constraintTop_toBottomOf="@id/titleItemTv" />

    <com.czur.uilib.CZSwitch
        android:id="@+id/voiceWakeupSwitch"
        android:layout_width="76px"
        android:layout_height="40px"
        app:layout_constraintRight_toRightOf="@id/totalSwitch"
        app:layout_constraintTop_toTopOf="@id/hintItemTv" />

    <TextView
        android:id="@+id/onlyTextTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30px"
        android:text="@string/label_ai_assistant_only_text"
        android:textColor="@color/text_common"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="@id/hintItemTv"
        app:layout_constraintTop_toBottomOf="@id/hintItemTv" />

    <com.czur.uilib.CZSwitch
        android:id="@+id/onlyTextSwitch"
        android:layout_width="76px"
        android:layout_height="40px"
        app:layout_constraintRight_toRightOf="@id/voiceWakeupSwitch"
        app:layout_constraintTop_toTopOf="@id/onlyTextTv" />


    <com.czur.starry.device.settings.widget.SettingItemBg
        android:id="@+id/bgHintItem"
        android:layout_width="900px"
        android:layout_height="206px"
        android:layout_marginTop="30px"
        app:ignoreHover="true"
        app:layout_constraintLeft_toLeftOf="@+id/bgItem"
        app:layout_constraintRight_toRightOf="@+id/bgItem"
        app:layout_constraintTop_toBottomOf="@+id/bgItem"
        app:position="single" />

    <TextView
        android:id="@+id/bgHintTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20px"
        android:text="@string/str_hint_ai_assistant_title"
        android:textColor="@color/text_common"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="@id/onlyTextTv"
        app:layout_constraintTop_toTopOf="@id/bgHintItem" />

    <TextView
        android:id="@+id/bgHintItemTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15px"
        android:text="@string/str_hint_ai_assistant_content"
        android:textColor="@color/text_common"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="@id/bgHintTitleTv"
        app:layout_constraintTop_toBottomOf="@id/bgHintTitleTv" />

</androidx.constraintlayout.widget.ConstraintLayout>