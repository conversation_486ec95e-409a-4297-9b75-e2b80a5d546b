<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:paddingHorizontal="10px"
    tools:ignore="PxUsage,HardcodedText">

    <!--  50%  -->
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/centerGuideline"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.25" />

    <TextView
        android:id="@+id/showAllTv"
        android:layout_width="wrap_content"
        android:layout_height="0px"
        android:gravity="center"
        android:includeFontPadding="false"
        android:text="显示全部图标"
        android:textColor="@color/black"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/showAllSwitch"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/showAllSwitch" />

    <com.czur.uilib.CZSwitch
        android:id="@+id/showAllSwitch"
        android:layout_width="95px"
        android:layout_height="45px"
        android:layout_margin="10px"
        app:layout_constraintRight_toRightOf="@id/centerGuideline"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/removeAPKInstallLimitTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20px"
        android:text="解除APK安装限制"
        android:textColor="@color/black"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="@id/showAllTv"
        app:layout_constraintTop_toBottomOf="@id/showAllTv" />

    <com.czur.uilib.CZSwitch
        android:id="@+id/removeAPKInstallLimitSwitch"
        android:layout_width="95px"
        android:layout_height="45px"
        android:layout_margin="10px"
        app:layout_constraintBottom_toBottomOf="@id/removeAPKInstallLimitTv"
        app:layout_constraintRight_toRightOf="@id/centerGuideline"
        app:layout_constraintTop_toTopOf="@id/removeAPKInstallLimitTv" />

    <TextView
        android:id="@+id/eyeProtectionTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20px"
        android:text="护眼模式"
        android:textColor="@color/black"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="@id/removeAPKInstallLimitTv"
        app:layout_constraintTop_toBottomOf="@id/removeAPKInstallLimitTv" />

    <com.czur.uilib.CZSwitch
        android:id="@+id/eyeProtectionSwitch"
        android:layout_width="95px"
        android:layout_height="45px"
        android:layout_margin="10px"
        app:layout_constraintBottom_toBottomOf="@id/eyeProtectionTv"
        app:layout_constraintRight_toRightOf="@id/centerGuideline"
        app:layout_constraintTop_toTopOf="@id/eyeProtectionTv" />

    <TextView
        android:id="@+id/enableLangTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20px"
        android:text="强制开启语言选择"
        android:textColor="@color/black"
        android:textSize="24px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="@id/eyeProtectionTv"
        app:layout_constraintTop_toBottomOf="@id/eyeProtectionTv" />

    <com.czur.uilib.CZSwitch
        android:id="@+id/enableLangSwitch"
        android:layout_width="95px"
        android:layout_height="45px"
        android:layout_margin="10px"
        app:layout_constraintBottom_toBottomOf="@id/enableLangTv"
        app:layout_constraintRight_toRightOf="@id/centerGuideline"
        app:layout_constraintTop_toTopOf="@id/enableLangTv" />

    <com.czur.uilib.btn.CZButton
        android:id="@+id/rebootBtn"
        android:layout_width="300px"
        android:layout_height="80px"
        android:layout_margin="10px"
        android:text="重启"
        android:textSize="30px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent" />


    <!--  启动项  -->
    <androidx.constraintlayout.helper.widget.Flow
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="10px"
        android:layout_marginVertical="10px"
        android:orientation="vertical"
        app:constraint_referenced_ids="startSettingBtn,startLogCollectBtn,startDeviceTestBtn,startStressTestBtn,openADBBtn"
        app:flow_horizontalAlign="start"
        app:flow_verticalBias="0"
        app:flow_verticalGap="10px"
        app:flow_verticalStyle="packed"
        app:flow_wrapMode="aligned"
        app:layout_constraintLeft_toRightOf="@id/centerGuideline"
        app:layout_constraintTop_toTopOf="parent" />

    <Button
        android:id="@+id/startSettingBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="启动原生设置" />

    <Button
        android:id="@+id/startLogCollectBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="启动日志收集" />

    <Button
        android:id="@+id/startDeviceTestBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="启动厂测应用" />

    <Button
        android:id="@+id/startStressTestBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="启动老化应用" />

    <Button
        android:id="@+id/openADBBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="打开adb调试" />


</androidx.constraintlayout.widget.ConstraintLayout>