<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@color/bg_main"
    tools:ignore="PxUsage">

    <ImageView
        android:id="@+id/refreshBtIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="56px"
        android:layout_marginRight="30px"
        android:src="@drawable/ic_refresh"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginTop="20px" />

    <TextView
        android:id="@+id/myBTDeviceTv"
        style="@style/title_text_style"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/bt_my_device_title"
        app:layout_constraintBottom_toTopOf="@id/bindBtDevicesRv"
        app:layout_constraintLeft_toLeftOf="@id/bindBtDevicesRv"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/otherBTDeviceTv"
        style="@style/title_text_style"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/bt_other_devices_title"
        app:layout_constraintLeft_toLeftOf="@id/otherBtDevicesRv"
        app:layout_constraintTop_toTopOf="@id/myBTDeviceTv" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/bindBtDevicesRv"
        android:layout_width="600px"
        android:layout_height="750px"
        android:layout_marginTop="30px"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/otherBtDevicesRv"
        app:layout_constraintTop_toBottomOf="@id/myBTDeviceTv"
        tools:listitem="@layout/item_bt_bind_devices" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/otherBtDevicesRv"
        android:layout_width="600px"
        android:layout_height="750px"
        android:layout_marginLeft="40px"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="@id/bindBtDevicesRv"
        app:layout_constraintLeft_toRightOf="@id/bindBtDevicesRv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/bindBtDevicesRv" />


</androidx.constraintlayout.widget.ConstraintLayout>