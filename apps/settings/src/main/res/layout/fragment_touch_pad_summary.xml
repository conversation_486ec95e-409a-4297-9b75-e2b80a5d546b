<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    tools:background="@color/bg_main"
    tools:ignore="PxUsage,RtlHardcoded">

    <com.czur.starry.device.settings.widget.SettingItemBg
        android:id="@+id/summaryInfoBg"
        android:layout_width="800px"
        android:layout_height="80px"
        app:ignoreHover="true"
        app:position="single" />

    <ImageView
        android:id="@+id/touchPadIconIv"
        android:layout_width="40px"
        android:layout_height="40px"
        android:layout_marginLeft="30px"
        android:src="@drawable/ic_bl_touch_pad"
        app:layout_constraintBottom_toBottomOf="@id/summaryInfoBg"
        app:layout_constraintLeft_toLeftOf="@id/summaryInfoBg"
        app:layout_constraintTop_toTopOf="@id/summaryInfoBg" />

    <TextView
        android:id="@+id/touchPadNameTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="14px"
        android:includeFontPadding="false"
        android:text="@string/touch_control"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/summaryInfoBg"
        app:layout_constraintLeft_toRightOf="@id/touchPadIconIv"
        app:layout_constraintTop_toTopOf="@id/summaryInfoBg" />

    <com.czur.starry.device.settings.widget.BatteryView
        android:id="@+id/touchPadBatteryView"
        android:layout_width="48px"
        android:layout_height="24px"
        android:layout_marginLeft="22px"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/summaryInfoBg"
        app:layout_constraintLeft_toRightOf="@id/touchPadNameTv"
        app:layout_constraintTop_toTopOf="@id/summaryInfoBg" />

    <TextView
        android:id="@+id/touchPadPowerTv"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15px"
        android:layout_marginRight="30px"
        android:gravity="start"
        android:includeFontPadding="false"
        android:maxLines="2"
        android:text="@string/str_touchpad_get_batterying"
        android:textColor="@color/white"
        android:textSize="24px"
        app:layout_constraintBottom_toBottomOf="@id/summaryInfoBg"
        app:layout_constraintLeft_toRightOf="@id/touchPadBatteryView"
        app:layout_constraintRight_toLeftOf="@+id/touchPadConnectInfoTv"
        app:layout_constraintTop_toTopOf="@id/summaryInfoBg" />

    <TextView
        android:id="@+id/touchPadConnectInfoTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="35px"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/summaryInfoBg"
        app:layout_constraintRight_toRightOf="@id/summaryInfoBg"
        app:layout_constraintTop_toTopOf="@id/summaryInfoBg"
        tools:text="@string/str_touch_pad_connected" />

    <ImageView
        android:id="@+id/touchPadConnectTagIv"
        android:layout_width="24px"
        android:layout_height="15px"
        android:layout_marginRight="11px"
        android:src="@drawable/ic_touch_pad_connect_tag"
        app:layout_constraintBottom_toBottomOf="@id/summaryInfoBg"
        app:layout_constraintRight_toLeftOf="@id/touchPadConnectInfoTv"
        app:layout_constraintTop_toTopOf="@id/summaryInfoBg" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/touchPadConnectGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="invisible"
        app:constraint_referenced_ids="touchPadConnectTagIv,touchPadPowerTv" />


    <Space
        android:id="@+id/_space"
        android:layout_width="0px"
        android:layout_height="20px" />

    <com.czur.starry.device.settings.widget.SettingItemBg
        android:layout_width="0px"
        android:layout_height="0px"
        app:ignoreHover="true"
        app:layout_constraintBottom_toBottomOf="@id/bottomBarrier"
        app:layout_constraintEnd_toEndOf="@id/summaryInfoBg"
        app:layout_constraintStart_toStartOf="@id/summaryInfoBg"
        app:layout_constraintTop_toBottomOf="@id/_space" />


    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/bottomBarrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="touchControlAutoWeakGroup,touchControlPairGroup" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/touchControlAutoWeakGroup"
        android:layout_width="800px"
        android:layout_height="wrap_content"
        android:minHeight="120px"
        android:paddingLeft="27px"
        android:paddingRight="27px"
        android:visibility="visible">

        <TextView
            android:id="@+id/dockWakeTitleTv"
            style="@style/TVSettingItemTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="27px"
            android:layout_weight="1"
            android:includeFontPadding="false"
            android:text="@string/touch_control_auto_wake_up"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.czur.uilib.CZSwitch
            android:id="@+id/dockWakeSwitch"
            android:layout_width="94px"
            android:layout_height="44px"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/dockWakeTitleTv" />

        <TextView
            style="@style/TVSettingItemContentBlack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="27px"
            android:maxWidth="600px"
            android:text="@string/touch_control_auto_wake_up_hint"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/dockWakeTitleTv" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/touchControlPairGroup"
        android:layout_width="800px"
        android:layout_height="wrap_content"
        android:paddingLeft="27px"
        android:paddingRight="27px"
        android:paddingBottom="27px">

        <TextView
            android:id="@+id/pairTitleTv"
            style="@style/TVSettingItemTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="27px"
            android:includeFontPadding="false"
            android:text="@string/touch_control_rematch"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/pairHintTv"
            style="@style/TVSettingItemContentBlack2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:lineSpacingMultiplier="1.1"
            android:maxWidth="700px"
            android:text="@string/touch_control_rematch_hint"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/pairTitleTv" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <Space
        android:id="@+id/space2"
        android:layout_width="10px"
        android:layout_height="26px"
        android:visibility="visible" />

    <Space
        android:id="@+id/newVersionSpace"
        style="@style/new_version_space_style"
        android:visibility="visible" />

    <TextView
        android:id="@+id/newVersionTv"
        style="@style/TVSettingItemTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:text="新版本: v9.7.30" />

    <TextView
        android:id="@+id/versionTv"
        style="@style/TVSettingItemContent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        tools:text="当前版本: v9.0" />

    <Space
        android:id="@+id/showNewVersionSpace1"
        android:layout_width="0px"
        android:layout_height="20px"
        android:visibility="visible" />

    <com.czur.starry.device.settings.widget.SettingItemBg
        android:id="@+id/showNewVersionInfoBg"
        android:layout_width="800px"
        android:layout_height="80px"
        android:layout_marginBottom="50px"
        app:ignoreHover="true"
        app:layout_constraintBottom_toTopOf="@+id/updateBtn"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <TextView
        android:id="@+id/showNewTv"
        style="@style/TVSettingItemTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30px"
        android:includeFontPadding="false"
        android:text="@string/str_touch_pad_check_new_info"
        app:layout_constraintBottom_toBottomOf="@id/showNewVersionInfoBg"
        app:layout_constraintLeft_toLeftOf="@id/showNewVersionInfoBg"
        app:layout_constraintTop_toTopOf="@id/showNewVersionInfoBg" />

    <ImageView
        android:id="@+id/showNewIv"
        android:layout_width="11px"
        android:layout_height="19px"
        android:layout_marginRight="30px"
        android:src="@drawable/ic_setting_item_next"
        app:layout_constraintBottom_toBottomOf="@id/showNewVersionInfoBg"
        app:layout_constraintRight_toRightOf="@id/showNewVersionInfoBg"
        app:layout_constraintTop_toTopOf="@id/showNewVersionInfoBg" />

    <Space
        android:id="@+id/showNewVersionSpace2"
        android:layout_width="0px"
        android:layout_height="5px"
        android:visibility="visible" />

    <com.czur.uilib.btn.CZButton
        android:id="@+id/updateBtn"
        android:layout_width="300px"
        android:layout_height="80px"
        android:layout_marginBottom="50px"
        android:text="@string/str_touch_pad_update_btn"
        android:textSize="30px"
        app:colorStyle="blueWhite"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <com.czur.uilib.btn.CZButton
        android:id="@+id/startPairBtn"
        android:layout_width="300px"
        android:layout_height="80px"
        android:layout_marginBottom="50px"
        android:text="@string/touch_control_start_pair"
        android:textSize="30px"
        android:visibility="gone"
        app:colorStyle="blueWhite"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />


    <androidx.constraintlayout.helper.widget.Flow
        android:id="@+id/allFlow"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:constraint_referenced_ids="summaryInfoBg,_space,touchControlAutoWeakGroup,touchControlPairGroup,space2,newVersionSpace,newVersionTv,versionTv,showNewVersionSpace1,showNewVersionInfoBg,showNewVersionSpace2"
        app:flow_verticalStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/newVersionGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="newVersionSpace,newVersionTv,showNewVersionSpace1,showNewVersionInfoBg,showNewVersionSpace2,updateBtn,showNewTv,showNewIv" />

</androidx.constraintlayout.widget.ConstraintLayout>