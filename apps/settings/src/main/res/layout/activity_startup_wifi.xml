<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/primary_blue"
    tools:ignore="PxUsage"
    tools:viewBindingIgnore="true">

    <TextView
        android:id="@+id/startupWifiTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="177px"
        android:text="@string/str_startup_wifi_sel"
        android:textColor="@color/white"
        android:textSize="48px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <FrameLayout
        android:id="@+id/centerContentFl"
        android:layout_width="match_parent"
        android:layout_height="500px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.czur.uilib.btn.CZButton
        android:id="@+id/startUpWifiPreTv"
        style="@style/start_up_btn"
        android:layout_marginBottom="60px"
        android:text="@string/startup_pre_step"
        app:colorStyle="NegativeInBlue"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/startUpWifiNextTv" />

    <com.czur.uilib.btn.CZButton
        android:id="@+id/startUpWifiNextTv"
        style="@style/start_up_btn"
        android:layout_marginLeft="30px"
        android:layout_marginBottom="60px"
        android:text="@string/startup_next_step"
        app:colorStyle="PositiveInBlue"
        app:layout_constraintLeft_toRightOf="@id/startUpWifiPreTv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/startUpWifiPreTv" />

    <LinearLayout
        android:id="@+id/startUpWifiSkipView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="100px"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="@id/startUpWifiPreTv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/startUpWifiPreTv">

        <TextView
            android:id="@+id/startUpWifiSkipTxView"
            android:layout_width="0px"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/str_skip"
            android:textColor="@color/white"
            android:textSize="36px"
            android:textStyle="bold" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="30px"
            android:layout_gravity="center_vertical"
            android:src="@drawable/skip_arrow_right"
            android:layout_marginLeft="10px"
            app:layout_constraintLeft_toRightOf="@id/startUpWifiSkipTxView"
            />

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>