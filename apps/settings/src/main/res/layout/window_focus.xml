<?xml version="1.0" encoding="utf-8"?><!--对焦画面-->
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="1920px"
    android:layout_height="1080px"
    tools:ignore="PxUsage">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitStart"
        android:src="@drawable/img_focus_bg" />

    <com.czur.uilib.CZTitleBar
        android:id="@+id/titleBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/manualFocusTitleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="50px"
        android:text="@string/manual_focus"
        android:textColor="@color/white"
        android:textSize="36px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/focusHintTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="30px"
        android:text="@string/focus_hint_auto"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/adjustModeFineSpace"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_goneMarginBottom="128px" />

    <TextView
        android:id="@+id/focusingHintTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/focusHintTv" />

    <Space
        android:id="@+id/adjustModeFineSpace"
        android:layout_width="324px"
        android:layout_height="50px"
        android:layout_marginBottom="80px"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        />

    <com.czur.uilib.choose.CZImageCheckBox
        android:id="@+id/adjustModeFineBox"
        style="@style/CBNormalSize"
        app:checkedImg="@drawable/file_icon_focus_checked"
        app:unCheckedImg="@drawable/file_icon_focus_unchecked"
        app:layout_constraintLeft_toLeftOf="@id/adjustModeFineSpace"
        app:layout_constraintBottom_toBottomOf="@id/adjustModeFineSpace"
        app:layout_constraintTop_toTopOf="@id/adjustModeFineSpace" />

    <TextView
        android:id="@+id/adjustModeFineTv"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="16px"
        android:text="@string/focus_adjust_fine"
        app:layout_constraintBottom_toBottomOf="@id/adjustModeFineBox"
        app:layout_constraintLeft_toRightOf="@id/adjustModeFineBox"
        app:layout_constraintTop_toTopOf="@id/adjustModeFineBox" />

    <com.czur.uilib.choose.CZImageCheckBox
        android:id="@+id/adjustModeNormalBox"
        style="@style/CBNormalSize"
        android:layout_marginRight="16px"
        app:checked="true"
        app:checkedImg="@drawable/file_icon_focus_checked"
        app:unCheckedImg="@drawable/file_icon_focus_unchecked"
        app:layout_constraintRight_toLeftOf="@id/adjustModeNormalTv"
        app:layout_constraintBottom_toBottomOf="@id/adjustModeNormalTv"
        app:layout_constraintTop_toTopOf="@id/adjustModeNormalTv"/>

    <TextView
        android:id="@+id/adjustModeNormalTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        android:text="@string/focus_adjust_normal"
        app:layout_constraintBottom_toBottomOf="@id/adjustModeFineSpace"
        app:layout_constraintLeft_toRightOf="@id/adjustModeFineSpace"
        app:layout_constraintRight_toRightOf="@id/adjustModeFineSpace" />


    <ImageView
        android:id="@+id/focusZoomOutIv"
        android:layout_width="100px"
        android:layout_height="100px"
        android:layout_marginLeft="100px"
        android:src="@drawable/ic_focus_zoom_out"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/focusZoomInIv"
        android:layout_width="100px"
        android:layout_height="100px"
        android:layout_marginRight="100px"
        android:src="@drawable/ic_focus_zoom_in"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/manualCorrectionVi"
        android:layout_width="0px"
        android:layout_height="30px"
        android:layout_marginRight="52px"
        android:layout_marginTop="50px"
        app:layout_constraintLeft_toLeftOf="@+id/manualCorrectionViTv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/manualCorrectionViIv"
        android:layout_width="7px"
        android:layout_height="11px"
        android:src="@drawable/ic_manual_correction"
        app:layout_constraintBottom_toBottomOf="@id/manualCorrectionVi"
        app:layout_constraintRight_toRightOf="@id/manualCorrectionVi"
        app:layout_constraintTop_toTopOf="@id/manualCorrectionVi" />

    <TextView
        android:id="@+id/manualCorrectionViTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="10px"
        android:textColor="@color/white"
        android:textSize="30px"
        android:textStyle="bold"
        android:text="@string/manual_keystone_correction"
        app:layout_constraintBottom_toBottomOf="@id/manualCorrectionVi"
        app:layout_constraintTop_toTopOf="@id/manualCorrectionVi"
        app:layout_constraintRight_toLeftOf="@id/manualCorrectionViIv" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/manualFocusGroup"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="manualCorrectionViTv,manualCorrectionViIv,adjustModeFineBox,adjustModeFineTv,adjustModeNormalBox,adjustModeNormalTv,focusZoomInIv,focusZoomOutIv,manualFocusTitleTv,titleBar" />

</androidx.constraintlayout.widget.ConstraintLayout>