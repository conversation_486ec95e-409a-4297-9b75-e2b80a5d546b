<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="300px"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    app:bl_corners_radius="10px"
    app:bl_solid_color="#F1F3FE"
    tools:background="#F1F3FE"
    tools:ignore="PxUsage">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            style="@style/memory_tv_remind_title"
            android:text="@string/memory_remaind_time_tv" />

        <View
            android:layout_width="10px"
            android:layout_height="20px"
            android:layout_gravity="center_vertical"
            android:background="@color/bg_main_blue" />
    </FrameLayout>

    <com.czur.starry.device.settings.widget.CleanRoomRemindItem
        android:id="@+id/remindItemTimeNone"
        android:layout_width="match_parent"
        android:layout_height="60px"
        app:cleanRoomItemText="@string/memory_remaind_none" />

    <com.czur.starry.device.settings.widget.CleanRoomRemindItem
        android:id="@+id/remindItemTimeEveryday"
        android:layout_width="match_parent"
        android:layout_height="60px"
        app:cleanRoomItemText="@string/memory_remaind_everyday" />

    <com.czur.starry.device.settings.widget.CleanRoomRemindItem
        android:id="@+id/remindItemTime3Day"
        android:layout_width="match_parent"
        android:layout_height="60px"
        app:cleanRoomItemText="@string/memory_remaind_3day" />

    <com.czur.starry.device.settings.widget.CleanRoomRemindItem
        android:id="@+id/remindItemTime7Day"
        android:layout_width="match_parent"
        android:layout_height="60px"
        app:cleanRoomItemText="@string/memory_remaind_7day" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10px">

        <TextView
            style="@style/memory_tv_remind_title"
            android:text="@string/memory_remain_memory" />

        <View
            android:layout_width="10px"
            android:layout_height="20px"
            android:layout_gravity="center_vertical"
            android:background="@color/bg_main_blue" />
    </FrameLayout>

    <com.czur.starry.device.settings.widget.CleanRoomRemindItem
        android:id="@+id/remindItemLeftSmall"
        android:layout_width="match_parent"
        android:layout_height="60px"
        app:cleanRoomItemText="@string/memory_remain_300" />

    <com.czur.starry.device.settings.widget.CleanRoomRemindItem
        android:id="@+id/remindItemLeftMid"
        android:layout_width="match_parent"
        android:layout_height="60px"
        app:cleanRoomItemText="@string/memory_remain_500" />

    <com.czur.starry.device.settings.widget.CleanRoomRemindItem
        android:id="@+id/remindItemLeftLarge"
        android:layout_width="match_parent"
        android:layout_height="60px"
        app:cleanRoomItemText="@string/memory_remain_1g"
        app:itemPosition="bottom"/>

</LinearLayout>