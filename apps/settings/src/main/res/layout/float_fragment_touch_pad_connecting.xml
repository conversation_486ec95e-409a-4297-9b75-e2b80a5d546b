<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="650px"
    android:layout_height="550px"
    tools:ignore="PxUsage">

    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:bl_corners_radius="40px"
        app:bl_solid_color="@color/white"
        tools:background="@color/white" />

    <TextView
        android:id="@+id/titleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30px"
        android:text="@string/title_touch_control_pairing"
        android:textColor="@color/text_common"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/infoTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="20px"
        android:paddingBottom="5px"
        android:textColor="@color/text_common"
        app:layout_constraintBottom_toTopOf="@id/centerImgIv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleTv" />

    <ImageView
        android:id="@+id/centerImgIv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/img_touch_pad_connecting"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.czur.uilib.btn.CZButton
        android:id="@+id/stopPairBtn"
        android:layout_width="300px"
        android:layout_height="80px"
        android:layout_marginBottom="30px"
        android:text="@string/touch_control_stop_pair"
        android:textSize="30px"
        app:colorStyle="blueWhite"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>