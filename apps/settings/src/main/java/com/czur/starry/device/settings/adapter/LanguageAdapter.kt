package com.czur.starry.device.settings.adapter

import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagW
import com.czur.czurutils.log.logTagV
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.StarryDevLocale

import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.manager.LanguageManager
import com.czur.uilib.UI_DISABLE_ALPHA
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * Created by 陈丰尧 on 2/7/21
 */
class LanguageAdapter(private val scope: CoroutineScope) : RecyclerView.Adapter<BaseVH>() {

    companion object {
        const val TAG = "LanguageAdapter"
    }

    private val locales = LanguageManager.localeList
    var selectPos = 0
        private set

    init {
        selectPos = initSelect()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
        BaseVH(R.layout.item_language, parent)

    override fun onBindViewHolder(holder: BaseVH, position: Int) {
        val locale = locales[position]
        if (position != selectPos) {
            holder.itemView.background = null
            holder.setTextColorRes(R.color.text_common, R.id.langTv)
                .setTextColorRes(R.color.text_common, R.id.langUiTv)
        } else {
            holder.itemView.setBackgroundColor(holder.context.getColor(R.color.bg_setting_rv_item_sel))
            holder.setTextColorRes(R.color.text_white, R.id.langTv)
                .setTextColorRes(R.color.text_white, R.id.langUiTv)
        }
        holder.setText(LanguageManager.getDisplayName(locale), R.id.langTv)
            .visible(position == selectPos, R.id.langSelIv)
            .setText(LanguageManager.getDisplayNameInUiLang(locale), R.id.langUiTv)
    }

    override fun getItemCount() = locales.size

    fun updateSelect(pos: Int) {
        if (pos == selectPos) {
            return
        }
        val oldSel = selectPos
        selectPos = pos
        notifyItemChanged(oldSel)
        notifyItemChanged(selectPos)
    }

    fun getSelect() = locales[selectPos]!!

    /**
     * 初始化语言信息
     */
    private fun initSelect(): Int {
        val select = LanguageManager.getCheckedLocale()
        var result = locales.indexOf(select)
        logTagD(TAG, "当前选中的语言:$result")
        if (result < 0) {
            logTagW(TAG, "当前的语言,不在语言列表中!!, 恢复默认")
            result = 0
            scope.launch(Dispatchers.IO) {
                logTagV(TAG, "恢复默认语言的选择")
                LanguageManager.updateLanguage(locales[0])
            }
        }
        return result
    }
}