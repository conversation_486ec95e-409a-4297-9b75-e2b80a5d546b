package com.czur.starry.device.settings.widget

import android.content.Context
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Gravity.CENTER_VERTICAL
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.widget.AppCompatTextView
import com.czur.starry.device.settings.R
import com.czur.uilib.color

/**
 * Created by 陈丰尧 on 2023/8/7
 */

private const val ITEM_TEXT_SIZE = 24F
private const val RADIUS = 10F  // 圆角半径

private enum class CleanRoomItemPosition(val value: Int) {
    TOP(0),
    MID(1),
    BOTTOM(2),
    SINGLE(3);
}

class CleanRoomRemindItem @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0,
) : LinearLayout(context, attrs, defStyleAttr) {

    private val textUnSelColor: Int by color(R.color.text_common)
    private val textSelColor: Int by color(R.color.white)
    private val bgUnSelColor: Int by color(R.color.transparent)
    private val bgSelColor: Int by color(R.color.bg_main_blue)

    var isSelect: Boolean = false
        set(value) {
            field = value
            updateUI()
        }

    private val itemText: String
    private val textView: TextView
    private val selIv: ImageView

    private var itemPosition = CleanRoomItemPosition.MID.value

    init {
        orientation = HORIZONTAL
        setPadding(25, 0, 25, 0)
        gravity = CENTER_VERTICAL

        val ta = context.obtainStyledAttributes(attrs, R.styleable.CleanRoomRemindItem)
        itemText = ta.getString(R.styleable.CleanRoomRemindItem_cleanRoomItemText) ?: ""
        itemPosition = ta.getInt(R.styleable.CleanRoomRemindItem_itemPosition,
            CleanRoomItemPosition.MID.value
        )
        ta.recycle()

        textView = AppCompatTextView(context)
        textView.text = itemText
        textView.setTextSize(TypedValue.COMPLEX_UNIT_PX, ITEM_TEXT_SIZE)
        textView.setTextColor(textUnSelColor)
        addView(textView, LayoutParams(0, LayoutParams.WRAP_CONTENT, 1F))

        selIv = ImageView(context)
        selIv.setImageResource(R.drawable.ic_select)
        selIv.visibility = GONE
        addView(selIv, LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT))

        updateUI()
    }

    private fun updateUI() {
        if (isSelect) {
            textView.setTextColor(textSelColor)
            selIv.visibility = VISIBLE

            when (itemPosition) {
                CleanRoomItemPosition.BOTTOM.value -> {
                    val hoverDrawable = GradientDrawable().apply {
                        setColor(bgSelColor)
                        cornerRadii = floatArrayOf(
                            0F, 0F, 0F, 0F,
                            RADIUS, RADIUS, RADIUS, RADIUS
                        )
                    }
                    background = hoverDrawable
                }
                else -> {
                    setBackgroundColor(bgSelColor)
                }
            }

        } else {
            textView.setTextColor(textUnSelColor)
            setBackgroundColor(bgUnSelColor)
            selIv.visibility = GONE
        }
    }
}