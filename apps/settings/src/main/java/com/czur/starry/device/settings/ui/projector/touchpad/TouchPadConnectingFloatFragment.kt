package com.czur.starry.device.settings.ui.projector.touchpad

import android.content.Intent
import android.graphics.PorterDuff
import android.graphics.PorterDuffColorFilter
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.v2.fragment.floating.CZVBFloatingFragment
import com.czur.starry.device.baselib.utils.ONE_MIN
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.lifecycle.AutoRemoveLifecycleObserver
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.view.CenteredImageSpan
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.databinding.FloatFragmentTouchPadConnectingBinding
import kotlinx.coroutines.delay

/**
 * Created by 陈丰尧 on 2025/3/18
 */
private const val TAG = "TouchPadConnectingFloatFragment"
private const val KEY_EXTRAS_STATUS = "state"
private const val ACTION_DOCKED = "com.android.action.CZUR_BTB_DOCKED"

class TouchPadConnectingFloatFragment :
    CZVBFloatingFragment<FloatFragmentTouchPadConnectingBinding>() {

    override fun CZVBFloatingFragment<FloatFragmentTouchPadConnectingBinding>.FloatingFragmentParams.initFloatingParams() {
        viewLifecycleObserver = object : AutoRemoveLifecycleObserver {
            override fun onStart(owner: LifecycleOwner) {
                super.onStart(owner)
                sendBroadcast(true)
            }

            override fun onStop(owner: LifecycleOwner) {
                super.onStop(owner)
                sendBroadcast(false)
                dismiss()
            }
        }
    }

    private fun sendBroadcast(dockState: Boolean) {
        logTagD(TAG, "发送模拟广播:dockState:Boolean")
        val intent = Intent(ACTION_DOCKED)
        intent.`package` = "com.czur.starry.device.settings"
        intent.putExtra(KEY_EXTRAS_STATUS, dockState)
        requireContext().sendBroadcast(intent)
    }

    override fun FloatFragmentTouchPadConnectingBinding.initBindingViews() {
        val expand = 3
        val iconId = R.drawable.ic_touch_pad_switch_key
        val imgDrawable =
            ContextCompat.getDrawable(requireContext(), iconId)?.apply {
                setBounds(0, 0, minimumWidth + expand, minimumHeight + expand)
                colorFilter = PorterDuffColorFilter(
                    ContextCompat.getColor(requireContext(), R.color.setting_item_bg_selected),
                    PorterDuff.Mode.SRC_IN
                )
            }

        val pairText = getString(R.string.touch_control_connecting_info)
        setForceImage(imgDrawable!!, infoTv, pairText)

        stopPairBtn.setOnDebounceClickListener {
            dismiss()
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        launch {
            delay(ONE_MIN)
            logTagV(TAG, "超时关闭")
            binding.stopPairBtn.performClick()
        }
    }

    private fun setForceImage(imgDrawable: Drawable, tv: TextView, text: String) {
        val placeholderCharacter = "[TouchControl]"
        val index = text.indexOf(placeholderCharacter)
        val span = SpannableString(text)

        val imgSpan = CenteredImageSpan(imgDrawable)
        span.setSpan(
            imgSpan,
            index,
            index + placeholderCharacter.length,
            Spannable.SPAN_INCLUSIVE_EXCLUSIVE
        )

        tv.text = span
    }
}