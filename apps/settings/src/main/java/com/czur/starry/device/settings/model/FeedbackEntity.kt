package com.czur.starry.device.settings.model

import kotlinx.serialization.Serializable


/**
 * Created by 陈丰尧 on 2023/8/14
 */
@Serializable
data class FeedbackEntity(
    val categoryKey: String,
    val name: String,
    val feedbackItems: List<FeedbackItem>,
    var refreshTime : Long = 0
) {
    companion object {
        fun createDefault(): FeedbackEntity {
            return FeedbackEntity(
                categoryKey = "",
                name = "",
                feedbackItems = emptyList()
            )
        }
    }
}

@Serializable
data class FeedbackItem(
    val name: String,
    val feedbackKey: String,
    val categoryKey: String,
    var refreshTime: Long = 0
)