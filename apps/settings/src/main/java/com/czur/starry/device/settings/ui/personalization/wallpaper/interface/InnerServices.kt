package com.czur.starry.device.settings.ui.personalization.wallpaper.`interface`

import com.czur.starry.device.baselib.network.core.MiaoHttpEntity
import com.czur.starry.device.baselib.network.core.MiaoHttpGet
import com.czur.starry.device.baselib.network.core.MiaoHttpParam
import com.czur.starry.device.settings.ui.personalization.wallpaper.bean.CustomImageEntity
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type

interface InnerServices {
    @MiaoHttpGet("/api/ota/starryShare/getUnreceivedImage")
    fun getUnreceivedImage(
        @MiaoHttpParam("sn") sn: String,
        @MiaoHttpParam("imageType") code: Int,
        type: Type = object :
            TypeToken<List<CustomImageEntity>>() {}.type
    ): MiaoHttpEntity<CustomImageEntity>

    @MiaoHttpGet("/api/ota/starryShare/imageReceived")
    fun responseImageReceived(
        @MiaoHttpParam("id") id: String,
        clazz: Class<Int> = Int::class.java
    ): MiaoHttpEntity<Int>

    @MiaoHttpGet("/api/starry/share/shareUrl")
    fun getImageUploadUrl(
        clazz: Class<String> = String::class.java
    ): MiaoHttpEntity<String>
}


