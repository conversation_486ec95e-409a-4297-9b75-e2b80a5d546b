package com.czur.starry.device.settings.ui.personalization.apps

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import androidx.fragment.app.viewModels
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.utils.addItemDecorationDrawable
import com.czur.starry.device.baselib.utils.closeDefChangeAnimations
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.lifecycle.AutoRemoveLifecycleObserver
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.repeatOnResume
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.SettingMainViewModel
import com.czur.starry.device.settings.adapter.LauncherFavAppAdapter
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentFavAppsBinding
import kotlinx.coroutines.delay

/**
 * Created by 陈丰尧 on 2023/7/4
 */
private const val TAG = "LauncherFavAppsFragment"

class LauncherFavAppsFragment : BaseBindingMenuFragment<FragmentFavAppsBinding>() {
    private val installedAdapter = LauncherFavAppAdapter(LauncherFavAppAdapter.Type.INSTALL)
    private val favouriteAdapter = LauncherFavAppAdapter(LauncherFavAppAdapter.Type.FAVOURITE)

    private val favAppViewModel: LauncherFavAppsViewModel by viewModels()
    private val mainViewModel: SettingMainViewModel by viewModels({ requireActivity() })

    override fun FragmentFavAppsBinding.initBindingViews() {
        installAppsRv.apply {
            closeDefChangeAnimations()
            adapter = installedAdapter
            layoutManager = GridLayoutManager(requireContext(), 3)
            addItemDecorationDrawable(
                RecyclerView.VERTICAL,
                R.drawable.shape_fav_app_divider_y
            )
            doOnItemClick(doubleClickOnSame = true) { vh, view ->
                when (view.id) {
                    R.id.favAppActionIv -> {
                        launch {
                            favAppViewModel.addFavApp(installedAdapter.getData(vh.bindingAdapterPosition))
                        }
                        true
                    }

                    else -> false
                }
            }
        }

        favAppsRv.apply {
            closeDefChangeAnimations()
            adapter = favouriteAdapter
            layoutManager = LinearLayoutManager(requireContext())
            addItemDecorationDrawable(
                RecyclerView.VERTICAL,
                R.drawable.shape_fav_app_divider_y
            )
            doOnItemClick(doubleClickOnSame = true) { vh, view ->
                when (view.id) {
                    R.id.favAppActionIv -> {
                        launch {
                            favAppViewModel.delFavApp(favouriteAdapter.getData(vh.bindingAdapterPosition))
                        }
                        true
                    }

                    else -> false
                }
            }
        }

        repeatCollectOnResume(mainViewModel.currentViewNavigateFlow) {
            logTagV(TAG, "需要添加的常用应用: $it")
            try {
                while (installedAdapter.getDataList().isEmpty()) {
                    delay(1000)
                }
                if (favouriteAdapter.getDataList().size < 4) {
                    val position =
                        installedAdapter.getPositionFromAppName(installedAdapter.getDataList(), it)
                    if (position != -1) {
                        favAppViewModel.addFavApp(installedAdapter.getData(position))
                    }
                    favAppViewModel.refresh()
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
            mainViewModel.onNavigateReset()
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        repeatOnResume {
            favAppViewModel.refresh()
        }

        repeatCollectOnResume(favAppViewModel.couldChooseApps) {
            installedAdapter.setData(it)
        }

        repeatCollectOnResume(favAppViewModel.favAppsFlow) {
            favouriteAdapter.setData(it)
        }
        repeatCollectOnResume(favAppViewModel.favAppsFullFlow) { full ->
            installedAdapter.showActionIv = !full
        }

        viewLifecycleOwner.lifecycle.addObserver(object : AutoRemoveLifecycleObserver {
            private val installReceiver = InstallReceiver()
            override fun onCreate(owner: LifecycleOwner) {
                super.onCreate(owner)
                val intentFilter = IntentFilter().apply {
                    addAction(Intent.ACTION_PACKAGE_ADDED)
                    addAction(Intent.ACTION_PACKAGE_REMOVED)
                    addDataScheme("package")
                }
                requireContext().registerReceiver(installReceiver, intentFilter)
            }

            override fun onDestroy(owner: LifecycleOwner) {
                super.onDestroy(owner)
                requireContext().unregisterReceiver(installReceiver)
            }
        })
    }

    private inner class InstallReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            launch {
                logTagV(TAG, "收到广播: ${intent?.action}, 刷新页面")
                favAppViewModel.refresh()
            }
        }
    }
}