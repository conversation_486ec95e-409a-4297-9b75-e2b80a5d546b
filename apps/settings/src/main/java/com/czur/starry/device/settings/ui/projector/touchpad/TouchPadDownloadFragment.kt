package com.czur.starry.device.settings.ui.projector.touchpad

import android.content.Intent
import android.os.Bundle
import android.text.format.Formatter
import androidx.core.content.ContentProviderCompat.requireContext
import androidx.core.content.ContextCompat.startActivity
import androidx.fragment.app.viewModels
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.randomStr
import com.czur.starry.device.baselib.view.floating.common.SingleBtnCommonFloat
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.app.App
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentTouchPadDownloadBinding
import com.czur.starry.device.settings.ui.projector.touchpad.dfu.TouchPadUpdateDfuAty
import com.czur.starry.device.settings.ui.projector.touchpad.update.KEY_FIRMWARE_PATH
import com.drake.net.Get
import com.drake.net.Net
import com.drake.net.component.Progress
import com.drake.net.interfaces.ProgressListener
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * 下载页面
 */
class TouchPadDownloadFragment : BaseBindingMenuFragment<FragmentTouchPadDownloadBinding>() {
    companion object {
        private const val TAG = "TouchPadDownloadFragment"
    }

    private val touchVM: TouchControlVM by viewModels({ requireParentFragment() })

    private val fileDir: File by lazy {
        File(App.context.filesDir, "touchpad").apply {
            // 清空上次的版本
            deleteRecursively()
            mkdirs()
        }
    }

    private val info by lazy {
        touchVM.touchPadVersionCheckInfoLive.value!!
    }

    private var pageFinish: Boolean = false // 页面已经销毁

    private var downloadID: String? = null


    override fun FragmentTouchPadDownloadBinding.initBindingViews() {
        downloadProgress.max = info.fileSize
        updateProgress(0L)

        cancelBtn.setOnClickListener {
            downloadID?.let { id ->
                downloadID = null
                Net.cancelId(id).also { result ->
                    logTagD(TAG, "« 取消下载任务:$id result:${result}")
                }
            }
            back()
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        launch {
            startDownload()
        }
    }

    // 这个下载只能在当前页面(于洋)
    private suspend fun startDownload() = withContext(Dispatchers.IO) {
        val extension = info.packageUrl.substringAfterLast(".")
        val file = File(fileDir, "${info.md5}.${extension}")
        logTagD(TAG, "保存的固件名称:${file.name}")
        if (file.exists()) {
            file.delete()
        }

        try {
            val resultFile = Get<File>(info.packageUrl) {
                logTagD(TAG, "started")
                updateProgress(0L)
                downloadID = randomStr().also {
                    logTagV(TAG, "下载任务ID:$it")
                    setId(it)
                }
                setDownloadFileName(file.name)
                setDownloadDir(fileDir)
                setDownloadFileNameConflict(true)

                addDownloadListener(object : ProgressListener() {
                    override fun onProgress(p: Progress) {
                        val currentSize = p.currentByteCount
                        updateProgress(currentSize)
                    }

                })
            }.await()
            logTagD(TAG, "下载完成=${resultFile.name}")
            if (!pageFinish) {
                updateProgress(info.fileSize)
                back(false)
                startUploadAty(resultFile)
            }
        } catch (tr: Throwable) {
            logTagE(TAG, "下载失败", tr = tr)
            if (downloadID == null) {
                logTagV(TAG, "下载任务已经取消")
            } else {
                logTagD(TAG, "下载失败")
                launch {
                    showDownloadFailDialog()
                }
            }
        }
    }

    private fun showDownloadFailDialog() {
        SingleBtnCommonFloat(content = getString(R.string.str_touchpad_download_fail),
            confirmBtnText = getString(R.string.str_touchpad_download_fail_btn),
            onConfirmClick = {
                it.dismiss()
                back()
            }).show()
    }

    private fun updateProgress(completedSize: Long) {
        launch {
            if (pageFinish) return@launch  // 页面销毁了, 不更新UI
            val present = (completedSize * 100 / info.fileSize).toInt()
            binding.downloadNumTv.text = if (present < 10) {
                "0${present}"
            } else {
                present.toString()
            }

            binding.downloadProgress.progress = completedSize

            val size = getString(
                R.string.str_touchpad_download_info,
                Formatter.formatFileSize(requireContext(), completedSize),
                Formatter.formatFileSize(requireContext(), info.fileSize)
            )
            binding.downloadSizeTv.text = size
        }
    }

    private fun startUploadAty(binFile: File) {
        val targetAty = when (touchVM.touchPadHardwareVersion) {
            TouchPadHardwareVersion.UNKNOWN -> {
                logTagW(TAG, "未知的硬件版本, 无法升级")
                back(true)
                return
            }

            TouchPadHardwareVersion.V1 -> {
                logTagD(TAG, "V1版本的硬件, 使用旧的升级页面")
                TouchPadUpdateV1Aty::class.java
            }

            TouchPadHardwareVersion.V2 -> {
                logTagD(TAG, "V2版本的硬件, 使用新的升级页面")
                TouchPadUpdateDfuAty::class.java
            }
        }

        val intent = Intent(requireContext(), targetAty).apply {
            putExtra(KEY_FIRMWARE_PATH, binFile.absolutePath)
        }
        startActivity(intent)
    }

    private fun back(del: Boolean = true) {
        pageFinish = true
        if (del) {
            fileDir.deleteRecursively()
        }
        parentFragmentManager.popBackStack()
    }

    override fun onDestroy() {
        downloadID?.let {
            Net.cancelId(it)
        }
        super.onDestroy()
    }

}