package com.czur.starry.device.settings.ui.net.wifi

import android.view.View
import androidx.fragment.app.viewModels
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.SettingMainViewModel
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentWifiEnablerBinding
import com.czur.starry.device.settings.utils.getWifiScanResults
import com.czur.starry.device.settings.utils.isLocked
import com.czur.starry.device.settings.utils.isWifiSetPortal
import kotlinx.coroutines.delay

/**
 * Created by 陈丰尧 on 1/26/21
 * 静态加载
 */
class WifiEnableFragment : BaseBindingMenuFragment<FragmentWifiEnablerBinding>() {
    companion object {
        private const val TAG = "WifiEnableFragment"
        private var bssid: String? = null
    }
    private var fromVoiceAssistant = false
    private val mainViewModel: SettingMainViewModel by viewModels({ requireActivity() })
    // 共用一个ViewModel
    private val wifiViewModel: WifiViewModel by viewModels({ requireActivity() })
    private var userIntent: Boolean? = null // 用户的意图

    override fun FragmentWifiEnablerBinding.initBindingViews() {
        wifiViewModel.wifiEnable.observe(viewLifecycleOwner) {
            logTagD(TAG, "wifi 状态: $it")
            if (userIntent == it || userIntent == null) {
                wifiEnableSwitch.setSwitchOn(it)
            } else {
                logTagW(
                    TAG,
                    "用户点击开关,但是状态未改变,不做处理,当前状态: $it, 用户点击状态: $userIntent"
                )
            }
        }

        wifiEnableSwitch.setOnSwitchChangeListener { isOn, fromUser ->
            if (fromUser || fromVoiceAssistant) {
                userIntent = isOn
                wifiViewModel.setWifiEnable(isOn)
                if (!isOn) {
                    connecteditem.visibility = View.GONE
                    connectedItemBg.visibility = View.GONE
                }
            }
        }

        wifiViewModel.wifInfo.observe(viewLifecycleOwner) {
            if (it.ssid.contains("unknown") || (it.linkSpeed <= 0)) {
                connecteditem.gone()
                connectedItemBg.gone()
            } else {
                val scanResults = getWifiScanResults()
                val result = scanResults.find { scan ->
                    scan.BSSID == it.bssid
                }
                launch {
                    val isPortal = isWifiSetPortal().getOrDefault(false)
                    connecteditem.setWifiInfoConnectedSettings(
                        it.ssid.replace("\"", ""),
                        result?.isLocked() ?: true, it.rssi, isPortal
                    ) { wifiDetailsListener() }
                    connecteditem.changeTextColor(resources.getColor(R.color.white))
                    connecteditem.changeImgColorFilter(resources.getColor(R.color.white))
                    connecteditem.show()
                    connectedItemBg.show()
                    bssid = it.bssid
                }
            }
        }

        connecteditem.setOnClickListener {
            wifiViewModel.isConnectedSSID = true
            wifiViewModel.showDetail(bssid!!)
        }

        repeatCollectOnResume(mainViewModel.currentViewNavigateFlow) {
            logTagD(TAG, "currentViewNavigateFlow: $it")
            mainViewModel.onNavigateReset()
            if (it == getString(R.string.voice_reboot_wifi)) {
                fromVoiceAssistant = true
                delay(500)
                wifiEnableSwitch.setSwitchOn(false)
                delay(2000)
                wifiEnableSwitch.setSwitchOn(true)
                delay(500)
                fromVoiceAssistant = false
            }
        }

    }

    private fun wifiDetailsListener() {
        wifiViewModel.isConnectedSSID = true
        wifiViewModel.showDetail(bssid!!)
    }


}