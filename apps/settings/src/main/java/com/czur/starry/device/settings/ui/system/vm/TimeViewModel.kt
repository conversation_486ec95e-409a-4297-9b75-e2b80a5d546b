package com.czur.starry.device.settings.ui.system.vm

import android.app.Application
import android.content.Intent
import android.os.SystemClock
import android.provider.Settings
import androidx.lifecycle.AndroidViewModel
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.ONE_SECOND
import com.czur.starry.device.baselib.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.withContext
import java.time.LocalDateTime
import java.util.Calendar

/**
 * Created by 陈丰尧 on 2024/8/5
 */
private const val TAG = "TimeViewModel"

class TimeViewModel(application: Application) : AndroidViewModel(application) {
    private val _yearFlow = MutableStateFlow(0)
    val yearFlow = _yearFlow.asStateFlow()
    private val _monthFlow = MutableStateFlow(0)
    val monthFlow = _monthFlow.asStateFlow()
    private val _dayFlow = MutableStateFlow(0)
    val dayFlow = _dayFlow.asStateFlow()
    private val _hourFlow = MutableStateFlow(0)
    val hourFlow = _hourFlow.asStateFlow()
    private val _minuteFlow = MutableStateFlow(0)
    val minuteFlow = _minuteFlow.asStateFlow()
    private val _secondFlow = MutableStateFlow(0)
    val secondFlow = _secondFlow.asStateFlow()

    private val calendar = Calendar.getInstance()

    private var _editModeFlow = MutableStateFlow(false)
    val editModeFlow = _editModeFlow.asStateFlow()

    init {
        launch {
            parseCurrentTime(System.currentTimeMillis())
            while (isActive) {
                val time = System.currentTimeMillis()
                parseCurrentTime(time)
                val delayTime = ONE_SECOND - time % ONE_SECOND + 1
                delay(delayTime)
            }
        }
    }

    fun changeMode(edit: Boolean? = null) {
        val target = edit ?: !_editModeFlow.value
        _editModeFlow.value = target
    }

    /**
     * 验证时间合法性
     */
    suspend fun verifyLegality(
        year: Int,
        month: Int,
        day: Int,
        hour: Int,
        minute: Int,
        second: Int
    ): Boolean {
        return withContext(Dispatchers.Default) {
            // 检验时间合法性
            try { // 月份不需要-1
                LocalDateTime.of(year, month, day, hour, minute, second)
                true
            } catch (e: Exception) {
                logTagW(TAG, "设置时间不合法:$year - $month - $day - $hour - $minute - $second")
                false
            }
        }
    }

    suspend fun setTime(year: Int, month: Int, day: Int, hour: Int, minute: Int, second: Int) {
        withContext(Dispatchers.Default) {
            val closeSetTime = async {
                closeAutoSetTime()
            }

            val realYear = if (year > 2036) {
                2036    // 系统时间无法设置到2037年
            } else {
                year
            }
            logTagD(TAG, "year:$year, realYear:$realYear")

            calendar.set(realYear, month - 1, day, hour, minute, second)
            val timeInMillis = calendar.timeInMillis
            // 设置系统时间
            SystemClock.setCurrentTimeMillis(timeInMillis)
            logTagV(TAG, "设置时间成功:$realYear - $month - $day - $hour - $minute - $second")
            changeMode(false)
            closeSetTime.await()
            parseCurrentTime(System.currentTimeMillis())
        }
    }

    private suspend fun parseCurrentTime(currentTime: Long) {
        withContext(Dispatchers.Default) {
            calendar.timeInMillis = currentTime
            _yearFlow.value = calendar.get(Calendar.YEAR)
            _monthFlow.value = calendar.get(Calendar.MONTH) + 1
            _dayFlow.value = calendar.get(Calendar.DAY_OF_MONTH)
            _hourFlow.value = calendar.get(Calendar.HOUR_OF_DAY)
            _minuteFlow.value = calendar.get(Calendar.MINUTE)
            _secondFlow.value = calendar.get(Calendar.SECOND)
        }
    }

    private suspend fun closeAutoSetTime() {
        withContext(Dispatchers.Default) {
            // 关闭自动设置时间
            logTagV(TAG, "关闭自动设置时间")
            Settings.Global.putInt(globalAppCtx.contentResolver, Settings.Global.AUTO_TIME, 0)
        }
    }
}