package com.czur.starry.device.settings.ui.personalization.wallpaper.utils

import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.baselib.utils.prop.getStringSystemProp
import com.czur.starry.device.baselib.utils.prop.setBooleanSystemProp
import com.czur.starry.device.baselib.utils.prop.setStringSystemProp
import com.czur.starry.device.settings.ui.personalization.wallpaper.bean.FileEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File


//自定义标签
//assets下模板路径
const val RECENT_ASSETS = "recent"
const val CUSTOM_ASSETS = "custom"

const val KEY_WALLPAPER_OPEN = "persist.setting.wallpaper.open"
const val KEY_WALLPAPER_PATH = "persist.setting.wallpaper.path"


//删除指定图片信息
suspend fun removeDataLocal(fileEntity: FileEntity) = withContext(Dispatchers.IO) {
    val list = getValueToList(CUSTOM_ASSETS)
    val data = list.toMutableList()
    data.forEach {
        if (it.absPath == fileEntity.absPath) {
            list.remove(it)
        }
    }
    setValueToString(CUSTOM_ASSETS, list)
}

//添加最近使用
suspend fun saveRecentData(data: FileEntity) = withContext(Dispatchers.IO) {
    val list = getValueToList(RECENT_ASSETS)
    list.add(data)
    var entity: FileEntity? = null
    if (list.size > 1) {
        entity = list[0]
        list.removeAt(0)
    }
    setValueToString(RECENT_ASSETS, list)
    entity
}

//删除最近图片信息
suspend fun removeRecentData(data: FileEntity) = withContext(Dispatchers.IO) {
    val list = getValueToList(RECENT_ASSETS)
    val datas = list.toMutableList()
    datas.forEach {
        if (it.absPath == data.absPath) {
            list.remove(data)
        }
    }
    setValueToString(RECENT_ASSETS, list)
}


//添加自定义模板
suspend fun addDataLocal(data: FileEntity) = withContext(Dispatchers.IO) {
    val list = getValueToList(CUSTOM_ASSETS)
    list.add(data)
    setValueToString(CUSTOM_ASSETS, list)
}


suspend fun deleteLocalFile(path: String) = withContext(Dispatchers.IO) {
    try {
        val file = File(path)
        if (file.exists())
            file.delete()
    } catch (e: Exception) {
        e.printStackTrace()
    }
}


//是否设置了屏保开启
fun getWallpaperSystemProp(): Boolean {
    return getBooleanSystemProp(KEY_WALLPAPER_OPEN, false)
}

fun setWallpaperSystemProp(para: Boolean) {
    return setBooleanSystemProp(KEY_WALLPAPER_OPEN, para)
}

//设置屏保路径
fun setWallpaperPath(para: String) {
    return setStringSystemProp(KEY_WALLPAPER_PATH, para)
}

fun getWallpaperPath(): String {
    return getStringSystemProp(KEY_WALLPAPER_PATH, "")
}




