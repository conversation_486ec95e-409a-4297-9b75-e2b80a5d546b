package com.czur.starry.device.settings.ui.personalization.assistant

import android.os.Bundle
import com.czur.starry.device.baselib.utils.fw.proxy.AudioAIManagerProxy
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentAiAssistantBinding

/**
 * Created by 陈丰尧 on 2025/5/9
 */
class AIAssistantFragment : BaseBindingMenuFragment<FragmentAiAssistantBinding>() {
    private val audioAIManager: AudioAIManagerProxy by lazy {
        AudioAIManagerProxy()
    }

    override fun FragmentAiAssistantBinding.initBindingViews() {
        binding.totalSwitch.setOnSwitchChangeListener { isOn: Boolean, fromUser: Boolean ->
            if (fromUser) {
                audioAIManager.setInteractionStatus(isOn)
            }
        }

        binding.voiceWakeupSwitch.setOnSwitchChangeListener { isOn: Boolean, fromUser: Boolean ->
            if (fromUser) {
                audioAIManager.setInteractionStageStatus(isOn)
            }
        }
        binding.onlyTextSwitch.setOnSwitchChangeListener { isOn: Boolean, fromUser: Boolean ->
            if (fromUser) {
                audioAIManager.setTtsStatus(isOn)
            }

        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        val totalEnable = audioAIManager.getInteractionStatus()
        binding.totalSwitch.setSwitchOn(totalEnable)
        val enable = audioAIManager.getInteractionStageStatus()
        binding.voiceWakeupSwitch.setSwitchOn(enable)
        val ttsEnable = audioAIManager.getTtsStatus()
        binding.onlyTextSwitch.setSwitchOn(ttsEnable)
    }

}