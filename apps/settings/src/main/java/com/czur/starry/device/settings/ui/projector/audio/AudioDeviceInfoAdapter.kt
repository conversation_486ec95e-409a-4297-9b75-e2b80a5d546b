package com.czur.starry.device.settings.ui.projector.audio

import android.media.AudioDeviceInfo
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.czur.starry.device.baselib.base.BaseDifferAdapter
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.utils.displayName
import com.czur.uilib.bg.CZItemBgView

/**
 * Created by 陈丰尧 on 2025/4/7
 */
class AudioDeviceInfoAdapter : RecyclerView.Adapter<BaseVH>() {
    private var data: List<AudioDeviceInfo> = mutableListOf()
    var selItem = 0
        private set

    fun updateSelItem(newItemIndex: Int) {
        if (newItemIndex == selItem) {
            return
        }
        selItem = newItemIndex
        notifyDataSetChanged()
    }

    private fun bindViewHolder(
        holder: BaseVH,
        position: Int,
        itemData: AudioDeviceInfo
    ) {
        holder.setText(itemData.displayName, R.id.displayNameTv)
        holder.visible(selItem == position, R.id.selIv)
        val itemBg: CZItemBgView = holder.getView(R.id.itemBg)
        itemBg.isSelected = selItem == position
        if (selItem == position) {
            // 选中的item
            holder.setTextColorRes(R.color.white, R.id.displayNameTv)
        } else {
            // 未选中的item
            holder.setTextColorRes(R.color.text_common, R.id.displayNameTv)
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): BaseVH {
        return BaseVH(R.layout.item_audio_device_info, parent)
    }

    override fun onBindViewHolder(
        holder: BaseVH,
        position: Int
    ) {
        val itemData = data[position]
        bindViewHolder(holder, position, itemData)
    }

    override fun getItemCount(): Int {
        return data.size
    }

    fun getData(i: Int): AudioDeviceInfo {
        return data[i]
    }

    fun setData(infos: List<AudioDeviceInfo>) {
        data = infos
        notifyDataSetChanged()
    }
}