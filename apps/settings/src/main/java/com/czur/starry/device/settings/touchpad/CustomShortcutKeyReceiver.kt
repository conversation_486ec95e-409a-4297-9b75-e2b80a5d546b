package com.czur.starry.device.settings.touchpad

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.view.KeyEvent
import androidx.core.content.ContextCompat
import com.czur.czurutils.extension.platform.newTask
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.BootParam
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_FILE
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_NOTICE_SETTING
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_TRANSCRIPTION
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_VOICE_ASSISTANT
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.KEY_CODE_CZUR_BTB_FOCUS
import com.czur.starry.device.baselib.common.KEY_CODE_MIC
import com.czur.starry.device.baselib.common.KEY_CODE_SOURCE_CHANGE
import com.czur.starry.device.baselib.common.KEY_LAUNCHER_BOOT_COMPLETE
import com.czur.starry.device.baselib.common.StarryDevLocale.Mainland
import com.czur.starry.device.baselib.common.StarryDevLocale.Overseas
import com.czur.starry.device.baselib.common.hw.StudioSeries
import com.czur.starry.device.baselib.utils.AppUtil
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.SettingUtil.PersonalizationSetting.KEY_SHORTCUT_TYPE_SHORT_PRESS
import com.czur.starry.device.baselib.utils.keyboard.injectKey
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.manager.FocusManager
import com.czur.starry.device.settings.ui.personalization.shortcut.CustomShortcutEventType
import com.czur.starry.device.settings.utils.MenuUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2025/3/6
 * 自定义按键的广播
 */
private const val ACTION = "com.czur.starry.device.settings.touchpad.CustomShortcutKeyReceiver"

private const val KEY_EVENT_TYPE = "key_event_type"
private const val KEY_KEY_CODE = "key_code"

private const val TAG = "CustomShortcutKeyReceiver"

class CustomShortcutKeyReceiver : BroadcastReceiver(), CoroutineScope by MainScope() {
    private val appUtil by lazy { AppUtil() }

    fun register(context: Context) {
        logTagD(TAG, "注册自定义按键广播")
        val intentFilter = IntentFilter(ACTION)
        ContextCompat.registerReceiver(context, this, intentFilter, ContextCompat.RECEIVER_EXPORTED)
    }

    fun unregister(context: Context) {
        logTagD(TAG, "注销自定义按键广播")
        context.unregisterReceiver(this)
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        val bootComplete = getBooleanSystemProp(KEY_LAUNCHER_BOOT_COMPLETE, false)
        if (!bootComplete) {
            logTagW(TAG, "launcher未启动完成，不处理自定义按键")
            return
        }

        var keyType =
            intent?.getIntExtra(
                KEY_EVENT_TYPE,
                KEY_SHORTCUT_TYPE_SHORT_PRESS
            ) ?: KEY_SHORTCUT_TYPE_SHORT_PRESS
        if (keyType < 0) keyType = KEY_SHORTCUT_TYPE_SHORT_PRESS

        val keyCode = intent?.getIntExtra(KEY_KEY_CODE, -1) ?: -1
        logTagD(TAG, "接收到自定义按键广播，eventType=$keyType, code=$keyCode")
        if (keyCode < 0) {
            logTagW(TAG, "code小于0，不处理")
            return
        }

        when (keyCode) {
            KEY_CODE_CZUR_BTB_FOCUS -> {
                // 执行自定义按键的操作
                launch {
                    doCustomShortcutAction(keyType)
                }
            }

            KEY_CODE_SOURCE_CHANGE, KEY_CODE_MIC -> {
                // 执行语音助手或mic静音的操作
                launch {
                    doMicAction()
                }
            }
        }
    }

    /**
     * 执行mic的按键操作
     */
    private suspend fun doMicAction() = withContext(Dispatchers.Default) {
        when (Constants.starryHWInfo.salesLocale) {
            Mainland -> {
                // 国内执行启动语音助手
                val intent = Intent().apply {
                    `package` = "com.czur.starry.device.voiceassistant"
                    action = ACTION_BOOT_VOICE_ASSISTANT
                    putExtra("key_message", "wake_up")
                }
                globalAppCtx.startService(intent)
            }

            Overseas -> {
                // 海外执行mic静音
                injectKey(KeyEvent.KEYCODE_MUTE)
            }
        }
    }

    /**
     * 执行自定义按键的操作
     */
    private suspend fun doCustomShortcutAction(keyType: Int) = withContext(Dispatchers.Default) {
        // 获取用户的设置
        val userSetStr = SettingUtil.PersonalizationSetting.getCustomShortcutKey(keyType)
        logTagD(TAG, "userSetStr:${userSetStr}")
        val eventType = try {
            CustomShortcutEventType.valueOf(userSetStr.split(";")[0])
        } catch (tr: Throwable) {
            null
        }
        logTagD(TAG, "eventType:${eventType}")
        when (eventType) {
            CustomShortcutEventType.BOOT_THIRD_PARTY_APP -> {
                val pkgName = userSetStr.split(";")[1]
                // 启动应用
                bootAppByPkgName(pkgName, keyType)
            }

            CustomShortcutEventType.AUTOFOCUS -> {
                FocusManager.executeAutoFocus()
            }

            CustomShortcutEventType.MANUAL_FOCUS -> {
                FocusManager.executeManualFocus()
            }

            CustomShortcutEventType.SCREENSHOT -> {
                // 截屏
                injectKey(KeyEvent.KEYCODE_SYSRQ)
            }

            CustomShortcutEventType.AI_SUBTITLES,CustomShortcutEventType.SOUND_RECORD, CustomShortcutEventType.SCREEN_RECORD, CustomShortcutEventType.VIDEO_RECORD, CustomShortcutEventType.TAKE_PICTURES -> {
                // 通过小星调用:
                val intent = Intent().apply {
                    `package` = "com.czur.starry.device.voiceassistant"
                    action = ACTION_BOOT_VOICE_ASSISTANT
                    putExtra("key_message", eventType.name)
                }
                globalAppCtx.startService(intent)
            }

            CustomShortcutEventType.SPEAKER_MUTE -> {
                injectKey(KeyEvent.KEYCODE_VOLUME_MUTE)
            }

            CustomShortcutEventType.BOOT_FILE_APP -> {
                // 启动文件应用
                val intent = Intent().apply {
                    action = ACTION_BOOT_FILE
                }.newTask()
                globalAppCtx.startActivity(intent)
            }

            CustomShortcutEventType.BOOT_SETTING_APP -> {
                // 启动Setting
                val intent = Intent().apply {
                    action = ACTION_BOOT_NOTICE_SETTING
                }.newTask()
                globalAppCtx.startActivity(intent)
            }

            null -> {
                bootDefault(keyType)
            }
        }

    }


    private fun bootDefault(keyType: Int) {
        logTagD(TAG, "bootDefault")

        if (Constants.starryHWInfo.series == StudioSeries) {

            // 启动Setting
            val intent = Intent().apply {
                action = ACTION_BOOT_NOTICE_SETTING
                putExtra(
                    BootParam.BOOT_KEY_SETTING_PAGE_MENU_KEY,
                    MenuUtil.SUB_MENU_KEY_CUSTOM_SHORTCUT_KEYS
                )
            }.newTask()
            globalAppCtx.startActivity(intent)
        } else {
            logTagD(TAG, "固定启动对焦页面")
            if (keyType == KEY_SHORTCUT_TYPE_SHORT_PRESS) {
                logTagV(TAG, "自动对焦")
                FocusManager.executeAutoFocus()
            } else {
                logTagV(TAG, "手动对焦")
                FocusManager.executeManualFocus()
            }
        }
    }

    /**
     * 通过包名启动应用
     */
    private fun bootAppByPkgName(pkgName: String, keyType: Int) {
        // 启动应用
        val intent = appUtil.getBootIntent(pkgName)
        if (intent != null) {
            globalAppCtx.startActivity(intent)
        } else {
            logTagW(TAG, "启动应用失败，pkgName=$pkgName")
            bootDefault(keyType)
        }
    }
}