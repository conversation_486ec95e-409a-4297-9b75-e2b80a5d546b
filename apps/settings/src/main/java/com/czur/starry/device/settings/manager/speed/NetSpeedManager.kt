package com.czur.starry.device.settings.manager.speed

import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.utils.ONE_SECOND
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.map

/**
 * Created by 陈丰尧 on 1/25/21
 * 测试网速的工具
 */
class NetSpeedManager(private val testCount: Int, private val testInterval: Long) {
    companion object {
        const val TAG = "NetSpeedManager"
    }

    /**
     * 下载记录
     */
    private val downloadRecord = NetSpeedRecordUtil(NetSpeedType.DOWNLOAD, testCount, testInterval)
    private val uploadRecord = NetSpeedRecordUtil(NetSpeedType.UPLOAD, testCount, testInterval)

    /**
     * 下载的Flow
     */
    val downloadFlow = downloadRecord.sizeFlow.map {
        // 转换单位为Mb/s
        makeSpeed(it)
    }
    val uploadFlow = uploadRecord.sizeFlow.map {
        makeSpeed(it)
    }
    private var downloadJob: Job? = null    // 下载协程
    private var uploadJob: Job? = null       // 上传协程

    /**
     * 测试下载
     */
    suspend fun startTestDownload() {
        withContext(Dispatchers.IO) {
            // 开始下载
            downloadJob = launch {
                val downloadTask = DownloadTask()
                logTagD(TAG, "开始下载:${Thread.currentThread().name}")
                downloadTask.doTask()
            }
            downloadRecord.startRecord()
            // 停止下载
            stopTestDownload()
        }
    }


    private fun stopTestDownload() {
        downloadJob?.cancel()
    }

    /**
     * 将单位转换为Mb/s
     */
    private fun makeSpeed(byteSize: Long): Float =
        (byteSize * 8f / 1024 / 1024) / (testInterval / ONE_SECOND)

    /**
     * 测试上传
     */
    suspend fun testUpload() {
        withContext(Dispatchers.IO) {

            uploadJob = launch {
                logTagD(TAG, "开始上传")
                val uploadTask = UploadTask()
                uploadTask.doTask()
            }
            // 需要获取OSS的token 所以需要有一点延迟,再开始记录
            delay(500)
            // 记录协程

            uploadRecord.startRecord()
            stopTestUpload()
        }
    }

    private fun stopTestUpload() {
        uploadJob?.cancel()
    }

}