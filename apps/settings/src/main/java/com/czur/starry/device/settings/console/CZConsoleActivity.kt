package com.czur.starry.device.settings.console

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.PowerManager
import androidx.lifecycle.LifecycleOwner
import com.czur.czurutils.extension.platform.startService
import com.czur.starry.device.baselib.base.v2.aty.CZViewBindingAty
import com.czur.starry.device.baselib.utils.SettingUtil
import com.czur.starry.device.baselib.utils.fw.proxy.SystemManagerProxy
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.lifecycle.AutoRemoveLifecycleObserver
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.baselib.utils.prop.setBooleanSystemProp
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.toast
import com.czur.starry.device.settings.databinding.ActivityConsoleBinding
import com.czur.starry.device.settings.debug.EyeProtectionWindowService

/**
 * Created by 陈丰尧 on 2023/11/7
 *
 */
class CZConsoleActivity : CZViewBindingAty<ActivityConsoleBinding>() {

    override fun AtyParams.initAtyParams() {
        lifecycleObserver = object : AutoRemoveLifecycleObserver {
            override fun onStop(owner: LifecycleOwner) {
                super.onStop(owner)
                finish()    // 不可见时自动关闭
            }
        }
    }

    private val bootUtil = BootUtil()
    private val systemManager: SystemManagerProxy by lazy {
        SystemManagerProxy()
    }

    override fun ActivityConsoleBinding.initBindingViews() {
        rebootBtn.setOnDebounceClickListener {
            // 重启设备
            val pm = getSystemService(Context.POWER_SERVICE) as PowerManager
            pm.reboot("CZConsoleActivity")
        }

        showAllSwitch.setOnSwitchChangeListener { isOn, fromUser ->
            if (fromUser) {
                launch {
                    setBooleanSystemProp("persist.launcher.showAll", isOn)
                }
            }
        }

        removeAPKInstallLimitSwitch.setOnSwitchChangeListener { isOn, fromUser ->
            if (fromUser) {
                launch {
                    SettingUtil.SystemSetting.setRemoveApkInstallRestriction(isOn)
                }
            }
        }

        // 护眼模式
        eyeProtectionSwitch.setOnSwitchChangeListener { isOn, fromUser ->
            if (fromUser) {
                launch {
                    SettingUtil.SystemSetting.setEyeProtectionEnable(isOn)
                    if (isOn) {
                        startService<EyeProtectionWindowService>()
                    }
                }
            }
        }

        enableLangSwitch.setOnSwitchChangeListener { isOn, fromUser ->
            if (fromUser) {
                launch {
                    SettingUtil.SystemSetting.setEnableLanguageSwitch(isOn)
                }
            }
        }

        // 启动原生设置
        startSettingBtn.setOnClickListener {
            toast("启动原生设置!")
            bootUtil.bootAndroidSetting()
        }
        // 日志收集
        startLogCollectBtn.setOnClickListener {
            toast("启动日志收集!")
            bootUtil.bootDiagnosis()
        }
        // 厂测
        startDeviceTestBtn.setOnClickListener {
            toast("启动厂测!")
            bootUtil.bootDeviceTest()
        }
        // 老化
        startStressTestBtn.setOnClickListener {
            toast("启动老化!")
            bootUtil.bootStressTest()
        }

        openADBBtn.setOnClickListener {
            // 打开ADB
            launch {
                systemManager.enableADB(true)
            }
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        launch {
            val showAll = getBooleanSystemProp("persist.launcher.showAll", false)
            binding.showAllSwitch.setSwitchOn(showAll)
        }

        launch {
            val removeApkInstallLimit = SettingUtil.SystemSetting.isApkInstallRestrictionRemoved()
            binding.removeAPKInstallLimitSwitch.setSwitchOn(removeApkInstallLimit)
        }

        launch {
            val eyeProtectionRunning =
                SettingUtil.SystemSetting.isEyeProtectionEnable()
            binding.eyeProtectionSwitch.setSwitchOn(eyeProtectionRunning)
        }

        launch {
            val enableLangSwitch = SettingUtil.SystemSetting.isEnableLanguageSwitch()
            binding.enableLangSwitch.setSwitchOn(enableLangSwitch)
        }
    }
}