package com.czur.starry.device.settings.ui.net.ethernet

import android.annotation.SuppressLint
import android.app.Application
import android.net.EthernetManager
import android.net.InetAddresses
import android.net.IpConfiguration
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.basic.alsoTrue
import com.czur.starry.device.baselib.utils.basic.otherwise
import com.czur.starry.device.baselib.utils.basic.yes
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.ping
import com.czur.starry.device.settings.utils.WifiConfigUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.withContext
import java.net.Inet4Address
import java.net.Inet6Address
import java.net.InetAddress


/**
 * Created by 陈丰尧 on 2023/8/9
 */
private const val TAG = "EthernetViewModel"
private const val IP_EMPTY = "0.0.0.0"

@SuppressLint("NewApi")
class EthernetViewModel(application: Application) : AndroidViewModel(application) {
    enum class EthernetIPMode {
        DHCP, STATIC
    }

    /**
     * 有线网连接状态
     */
    enum class EthernetConnectState {
        CONNECTED,          // 已连接:     连接并能上网
        RECOGNIZING,        // 正在识别:    连接 正在检测上网能力
        NOT_RECOGNIZED,     // 未识别:     连接 无法上网
        DISCONNECTED        // 未连接:     未连接
    }

    /**
     * 是否有网
     */
    enum class HasNetwork {
        YES, NO, UNKNOWN
    }


    enum class EthernetState {
        ETHER_STATE_CONNECTED,
        ETHER_STATE_DISCONNECTED,
    }

    private var mIfaceName = ""
    private val ethManager =
        application.getSystemService(EthernetManager::class.java) as EthernetManager
    private val ethConnectFlow =
        MutableStateFlow(EthernetState.ETHER_STATE_DISCONNECTED)   // 是否连接
    private val hasNetworkFlow = MutableStateFlow(HasNetwork.UNKNOWN)// 是否有网

    private var isPinging = false   // 是否正在ping

    val connectStateFlow = combine(ethConnectFlow, hasNetworkFlow) { ethConnect, hasNetwork ->
        when (ethConnect) {
            EthernetState.ETHER_STATE_CONNECTED -> {
                when (hasNetwork) {
                    HasNetwork.YES -> EthernetConnectState.CONNECTED
                    HasNetwork.NO -> EthernetConnectState.NOT_RECOGNIZED
                    HasNetwork.UNKNOWN -> EthernetConnectState.RECOGNIZING
                }
            }

            EthernetState.ETHER_STATE_DISCONNECTED -> EthernetConnectState.DISCONNECTED
        }
    }

    val ipModeFlow = MutableStateFlow(EthernetIPMode.DHCP)
    val ipFlow = MutableStateFlow(IP_EMPTY)
    val subnetMaskFlow = MutableStateFlow(IP_EMPTY)
    val gatewayFlow = MutableStateFlow(IP_EMPTY)
    val dnsFlow = MutableStateFlow(IP_EMPTY)

    val saveBtnEnableFlow = MutableStateFlow(false)

    var customIP = ""
        set(value) {
            field = value
            updateSaveBtnEnable()
        }
    var customSubnetMask = ""
        set(value) {
            field = value
            updateSaveBtnEnable()
        }
    var customGateway = ""
        set(value) {
            field = value
            updateSaveBtnEnable()
        }
    var customDNS = ""
        set(value) {
            field = value
            updateSaveBtnEnable()
        }

    val saveBtnClick = MutableLiveData<Boolean>()

    init {
        launch {
            initEthernetInfo()
        }

        launch {
            ipModeFlow
                .collectLatest {
                    loadEthInfo(it)
                }
        }
    }


    /**
     * 更新有线网信息
     */
    fun updateEthConnectState(
        state: EthernetState = ethConnectFlow.value,
    ) {
        logTagV(TAG, "更新有线网络状态: ${state.name}")
        ethConnectFlow.value = state
        when (state) {
            EthernetState.ETHER_STATE_CONNECTED -> {
                logTagV(TAG, "有线网连接上了, 加载有线网信息")
                launch {
                    loadEthInfo()
                }
                if (hasNetworkFlow.value == HasNetwork.UNKNOWN) {
                    // 有线网连接上了, 但是还未检测是否有网, 需要检测
                    launch {
                        logTagD(TAG, "有线网连接上了, 但是还未检测是否有网, 需要检测")
                        checkHasNetWork()
                    }
                }
            }

            EthernetState.ETHER_STATE_DISCONNECTED -> {
                logTagV(TAG, "有线网断开, 重置有线网信息")
                if (ipModeFlow.value == EthernetIPMode.DHCP) {
                    //自动获取的时候断网重置
                    resetEthInfoToEmpty()
                }
            }
        }
    }

    /**
     * 更新是否能连接互联网
     * @param hasNetwork 是否能连接互联网
     */
    fun updateHasNetWork(hasNetwork: Boolean) {
        logTagD(TAG, "更新有线网是否有网: $hasNetwork")
        if (!hasNetwork && isPinging) {
            // 如果有网, 那么肯定是有网的, 不需要等待, 没网则有可能马上就有了
            // 所以只在没网的状态下, 如果正在ping, 则等待ping的结果
            logTagI(TAG, "正在ping, 不更新")
            return
        }

        hasNetworkFlow.value = if (hasNetwork) HasNetwork.YES else HasNetwork.NO
    }

    /**
     * 切换有线网ip获取模式
     * @param mode 模式: null表示切换到另一种模式
     */
    suspend fun changeIpMode(mode: EthernetIPMode? = null) = withContext(Dispatchers.Default) {
        logTagD(TAG, "切换有线网ip获取模式:${mode}")
        val target = mode ?: when (ipModeFlow.value) {
            EthernetIPMode.DHCP -> EthernetIPMode.STATIC
            EthernetIPMode.STATIC -> EthernetIPMode.DHCP
        }
        val result = when (target) {
            EthernetIPMode.DHCP -> {
                logTagD(TAG, "切换有线网ip获取模式:DHCP")
                val ipConfiguration = EthernetUtil.setDhcpMode() as IpConfiguration
                ethManager.setConfiguration(mIfaceName, ipConfiguration)
                true
            }

            EthernetIPMode.STATIC -> {
                logTagD(TAG, "切换有线网ip获取模式:STATIC")
                changeToStatic()
            }
        }
        if (result) {
            logTagD(TAG, "切换有线网ip获取模式成功")
            ipModeFlow.value = target
        } else {
            logTagW(TAG, "切换有线网ip获取模式失败")
        }
    }

    /**
     * 设置自定义的静态ip配置,
     * 使用 用户设置的信息
     */
    suspend fun setCustomStaticConfig(): Boolean {
        return changeToStatic(
            customIP.ifEmpty { ipFlow.value },
            customSubnetMask.ifEmpty { subnetMaskFlow.value },
            customGateway.ifEmpty { gatewayFlow.value },
            customDNS.ifEmpty { dnsFlow.value }
        ).alsoTrue {
            // 切换成功后, 清空自定义的配置
            logTagV(TAG, "切换用户指定配置成功, 重新加载有线网信息")
            clearCustomStaticConfig()
            loadEthInfo()
        }
    }

    private fun updateSaveBtnEnable() {
        saveBtnEnableFlow.value = when (ipModeFlow.value) {
            EthernetIPMode.DHCP -> false
            EthernetIPMode.STATIC -> {
                (customIP.isNotEmpty() && customIP != ipFlow.value)
                        || (customSubnetMask.isNotEmpty() && customSubnetMask != subnetMaskFlow.value)
                        || (customGateway.isNotEmpty() && customGateway != gatewayFlow.value)
                        || (customDNS.isNotEmpty() && customDNS != dnsFlow.value)
            }
        }
    }

    private fun resetEthInfoToEmpty() {
        logTagD(TAG, "resetEthInfoToEmpty")
        ipFlow.value = IP_EMPTY
        subnetMaskFlow.value = IP_EMPTY
        gatewayFlow.value = IP_EMPTY
        dnsFlow.value = IP_EMPTY
    }

    private suspend fun checkHasNetWork() {
        logTagD(TAG, "检测是否有网(ping)")
        isPinging = true
        ping(retryTimes = 2)
            .also {
                logTagD(TAG, "检测是否有网(ping)结束")
                isPinging = false
            }
            .yes {
                logTagD(TAG, "有线网有网(ping)")
                updateHasNetWork(true)
            }.otherwise {
                logTagD(TAG, "有线网无网((ping)")
                updateHasNetWork(false)
            }

    }


    private suspend fun initEthernetInfo() {
        val interfaces: Array<String> = ethManager.availableInterfaces
        mIfaceName = if (interfaces.isNotEmpty()) {
            interfaces[0] //"eth0";
        } else {
            logTagW(TAG, "没有可用的有线网接口, 使用默认值")
            "eth0"
        }
        // 1. ip获取模式
        ipModeFlow.value = loadIpMode().also {
            logTagD(TAG, "初始化有线网络信息, ip获取模式:${it}")
        }
    }

    private suspend fun loadIpMode(): EthernetIPMode {
        logTagD(TAG, "加载有线网ip获取模式")

        val ipConfigProxy = IpConfigurationProxy(ethManager.getConfiguration(mIfaceName))
        return when (ipConfigProxy.getIpAssignment()) {
            ipConfigProxy.getStaticInstance() -> EthernetIPMode.STATIC
            ipConfigProxy.getDhcpInstance() -> EthernetIPMode.DHCP
            else -> {
                logTagW(TAG, "不支持的有线网模式, 切换回DHCP")
                changeIpMode(EthernetIPMode.DHCP)
                EthernetIPMode.DHCP
            }
        }
    }


    private fun clearCustomStaticConfig() {
        customIP = ""
        customSubnetMask = ""
        customGateway = ""
        customDNS = ""
    }


    private suspend fun changeToStatic(
        ip: String = ipFlow.value,
        subnetMask: String = subnetMaskFlow.value,
        gateway: String = gatewayFlow.value,
        dns: String = dnsFlow.value
    ): Boolean = withContext(Dispatchers.Default) {
        /*
           * get ip address, netmask,dns ,gw etc.
           */

        logTagV(TAG, "ip:$ip")
        logTagV(TAG, "subnetMask:$subnetMask")
        logTagV(TAG, "gateway:$gateway")
        logTagV(TAG, "dns:$dns")

        val prefixLength: Int = WifiConfigUtil.maskStr2InetMask(subnetMask)
        logTagD(TAG, "prefixLength:$prefixLength")

        var inetAddr: InetAddress? = null
        var gatewayAddr: InetAddress? = null
        var dnsAddr: InetAddress? = null
        try {
            inetAddr = getIPv4Address(ip)!!
            gatewayAddr = getIPv4Address(gateway)!!
            dnsAddr = getIPv4Address(dns)!!
        } catch (e: Exception) {
            logTagE(TAG, "ip,mask or dnsAddr is wrong", tr = e)
        }

        if (null == inetAddr || inetAddr.address.toString()
                .isEmpty() || prefixLength == 0 || gatewayAddr.toString().isEmpty()
            || dnsAddr.toString().isEmpty()
        ) {
            logTagE("ip,mask or dnsAddr is wrong")
        }
        val mIpConfiguration = EthernetUtil.setStaticMode(
            inetAddr,
            prefixLength,
            gatewayAddr,
            dnsAddr
        ) as IpConfiguration
        ethManager.setConfiguration(mIfaceName, mIpConfiguration)
        true
    }


    private suspend fun loadEthInfo(ipMode: EthernetIPMode = ipModeFlow.value) {
        when (ipMode) {
            EthernetIPMode.DHCP -> getEthInfoFromDhcp()
            EthernetIPMode.STATIC -> getEthInfoFromStaticIp()
        }
    }

    private suspend fun getEthInfoFromDhcp() = withContext(Dispatchers.Default) {
        logTagD(TAG, "获取DHCP状态下的信息")
        var tempIpInfo: String
        //ip
        ipFlow.value =
            ethManager.getIpAddress(mIfaceName).takeUnless { it.isNullOrEmpty() } ?: IP_EMPTY

        //netmask
        subnetMaskFlow.value =
            ethManager.getNetmask(mIfaceName).takeUnless { it.isNullOrEmpty() } ?: IP_EMPTY

        //gateway
        gatewayFlow.value =
            ethManager.getGateway(mIfaceName).takeUnless { it.isNullOrEmpty() } ?: IP_EMPTY

        //dns
        val dnsServers = ethManager.getDns(mIfaceName).takeUnless { it.isNullOrEmpty() } ?: IP_EMPTY
        val dns1 = dnsServers.split(",")
            .takeIf { it.isNotEmpty() }
            ?.filterNot { it.isIPV6() }//过滤掉ipv6 dns
            ?.firstOrNull()
            ?: IP_EMPTY
        dnsFlow.value = dns1
    }

    private suspend fun getEthInfoFromStaticIp() = withContext(Dispatchers.Default) {
        logTagD(TAG, "获取静态IP状态下的信息")

        val staticIpConfiguration =
            ethManager.getConfiguration(mIfaceName).staticIpConfiguration
        if (staticIpConfiguration == null) {
            logTagW(TAG, "静态IP配置为空")
            ipFlow.value = IP_EMPTY
            subnetMaskFlow.value = IP_EMPTY
            gatewayFlow.value = IP_EMPTY
            dnsFlow.value = IP_EMPTY
            return@withContext
        }
        val ipAddress = staticIpConfiguration.ipAddress?.address?.hostAddress ?: IP_EMPTY
        val gateway = staticIpConfiguration.gateway?.hostAddress ?: IP_EMPTY
        val dnsServers = staticIpConfiguration.dnsServers
        val dns1 = dnsServers
            .filterNot { it is Inet6Address }//过滤掉ipv6 dns
            .map { it.hostAddress }
            .firstOrNull()
            ?: IP_EMPTY

        ipFlow.value = ipAddress
        subnetMaskFlow.value =
            prefixLengthToNetmask(staticIpConfiguration.ipAddress?.prefixLength ?: 0)
        gatewayFlow.value = gateway
        dnsFlow.value = dns1
    }

    private fun prefixLengthToNetmask(prefixLength: Int): String {
        val netmask = (-1 shl (32 - prefixLength)) // 左移操作来创建子网掩码
        val netmaskBytes = byteArrayOf(
            (netmask shr 24 and 0xFF).toByte(),
            (netmask shr 16 and 0xFF).toByte(),
            (netmask shr 8 and 0xFF).toByte(),
            (netmask and 0xFF).toByte(),
        )
        val inetAddress = InetAddress.getByAddress(netmaskBytes)
        return inetAddress.hostAddress ?: IP_EMPTY
    }

    private fun String.isIPV6(): Boolean {
        return try {
            val inetAddress = InetAddress.getByName(this)
            inetAddress is Inet6Address
        } catch (e: Exception) {
            false
        }
    }

    private fun getIPv4Address(text: String): Inet4Address? {
        return try {
            InetAddresses.parseNumericAddress(text) as Inet4Address
        } catch (e: IllegalArgumentException) {
            null
        } catch (e: ClassCastException) {
            null
        }
    }
}