package com.czur.starry.device.settings.ui.net.ethernet

import android.os.Bundle
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import com.czur.starry.device.baselib.base.v2.fragment.CZViewBindingFragment
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.settings.databinding.FragmentEthernetSettingStaticBinding

/**
 * Created by 陈丰尧 on 2023/8/8
 */
class EthernetSettingStaticFragment :
    CZViewBindingFragment<FragmentEthernetSettingStaticBinding>() {
    private val ethernetViewModel: EthernetViewModel by viewModels({ requireParentFragment() })

    override fun FragmentEthernetSettingStaticBinding.initBindingViews() {

        ipValueTv.setOnIPChangeListener {
            ethernetViewModel.customIP = it
        }
        subnetValueTv.setOnIPChangeListener {
            ethernetViewModel.customSubnetMask = it
        }
        gatewayValueTv.setOnIPChangeListener {
            ethernetViewModel.customGateway = it
        }
        dnsValueTv.setOnIPChangeListener {
            ethernetViewModel.customDNS = it
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)
        repeatCollectOnResume(ethernetViewModel.ipFlow) {
            binding.ipValueTv.setText(it)
            ethernetViewModel.customIP = it
        }
        repeatCollectOnResume(ethernetViewModel.subnetMaskFlow) {
            binding.subnetValueTv.setText(it)
            ethernetViewModel.customSubnetMask = it
        }
        repeatCollectOnResume(ethernetViewModel.gatewayFlow) {
            binding.gatewayValueTv.setText(it)
            ethernetViewModel.customGateway = it
        }
        repeatCollectOnResume(ethernetViewModel.dnsFlow) {
            binding.dnsValueTv.setText(it)
            ethernetViewModel.customDNS = it
        }
        ethernetViewModel.saveBtnClick.observe(this, Observer { status ->
            if (status) {
                binding.ipValueTv.clearFocus()
                binding.subnetValueTv.clearFocus()
                binding.gatewayValueTv.clearFocus()
                binding.dnsValueTv.clearFocus()
            }
        })

    }
}