package com.czur.starry.device.settings.ui.system.time

import android.view.MotionEvent
import android.view.ViewGroup
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.base.BaseDifferAdapter
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.startup.StartUpTimeZoneAdapter
import com.czur.starry.device.settings.widget.SettingItemBg
import com.czur.uilib.seek.CZRoundIndexTv

/**
 * Created by 陈丰尧 on 2023/6/20
 */
private const val TAG = "TimeZoneAdapter"

data class TimeZoneItem(
    val id: String,     // 时区id
    val name: String,   // 时区名称
    val pinyin: String, // 排序id
    val gmt: String,    // 时区GMT
    val offset: Int,    // 时区偏移
    val indexStr: String // 排序字符
)

class TimeZoneAdapter : BaseDifferAdapter<TimeZoneItem>() {
    private var selectId: String = ""
    var currentSelPos = -1
        private set(value) {
            field = value
            onSelPosChange?.invoke(value)
        }

    var onSelPosChange: ((Int) -> Unit)? = null

    fun updateSelectId(id: String) {
        if (selectId == id) {
            return
        }
        selectId = id
        if (currentSelPos >= 0) {
            notifyItemChanged(currentSelPos)
        }
        val data = getDataList()
        currentSelPos = data.indexOfFirst { it.id == id }
        notifyItemChanged(currentSelPos)
    }

    override fun setData(newData: List<TimeZoneItem>) {
        differ.submitList(newData) {
            currentSelPos = newData.indexOfFirst { it.id == selectId }
        }
    }

    override fun areItemsTheSame(oldItem: TimeZoneItem, newItem: TimeZoneItem): Boolean {
        return oldItem.id == newItem.id
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
        return BaseVH(R.layout.item_timezone, parent)
    }

    override fun bindViewHolder(holder: BaseVH, position: Int, itemData: TimeZoneItem) {
        holder.setText(itemData.name, R.id.timeZoneNameTv)
        holder.setText(itemData.gmt, R.id.timeZoneGMTTv)
        holder.setText(itemData.indexStr, R.id.timeZoneIndexTv)
        holder.visible(itemData.indexStr.isNotEmpty(), R.id.timeZoneIndexTv)
        holder.visible(itemData.id == selectId, R.id.timeZoneSelIv)
        holder.getView<SettingItemBg>(R.id.timeZoneBg).setHoverItemPosition(position, itemCount)
        if (itemData.id == selectId) {
            // 选中
            holder.getView<SettingItemBg>(R.id.timeZoneBg).isSelected = true
            holder.setTextColorRes(R.color.text_white, R.id.timeZoneNameTv)
            holder.setTextColorRes(R.color.text_white, R.id.timeZoneGMTTv)
            holder.getView<CZRoundIndexTv>(R.id.timeZoneIndexTv).updateColorRes(
                newBgColorRes = R.color.setting_round_index_tv_bg_sel,
                newTextColorRes = R.color.setting_round_index_tv_text_sel,
            )
        } else {
            holder.getView<SettingItemBg>(R.id.timeZoneBg).isSelected = false
            holder.setTextColorRes(R.color.text_common, R.id.timeZoneNameTv)
            holder.setTextColorRes(R.color.text_common, R.id.timeZoneGMTTv)
            holder.getView<CZRoundIndexTv>(R.id.timeZoneIndexTv).updateColorRes(
                newBgColorRes = R.color.setting_round_index_tv_bg,
                newTextColorRes = R.color.setting_round_index_tv_text,
            )
        }
    }

}