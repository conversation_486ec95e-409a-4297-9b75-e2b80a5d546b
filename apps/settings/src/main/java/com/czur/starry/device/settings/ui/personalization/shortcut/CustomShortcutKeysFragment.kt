package com.czur.starry.device.settings.ui.personalization.shortcut

import android.os.Bundle
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.czur.starry.device.baselib.utils.addItemDecorationDrawable
import com.czur.starry.device.baselib.utils.closeDefChangeAnimations
import com.czur.starry.device.baselib.utils.doOnItemClick
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.invisible
import com.czur.starry.device.baselib.utils.repeatCollectOnResume
import com.czur.starry.device.baselib.utils.repeatOnResume
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.adapter.CustomShortcutItem
import com.czur.starry.device.settings.adapter.CustomShortcutKeyAdapter
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentCustomShortcutKeysBinding
import com.czur.starry.device.settings.databinding.ItemShortcutChooseBinding

/**
 * Created by 陈丰尧 on 2025/3/5
 */
class CustomShortcutKeysFragment : BaseBindingMenuFragment<FragmentCustomShortcutKeysBinding>() {
    // 快捷键
    private val customShortcutViewModel: CustomShortcutViewModel by viewModels()
    private val customAdapter = CustomShortcutKeyAdapter()

    override fun FragmentCustomShortcutKeysBinding.initBindingViews() {
        // 初始化标题
        chooseSingleTapGroup.shortcutChooseTitleTv.setText(R.string.title_shortcut_keys_single_tap)
        chooseLongTapGroup.shortcutChooseTitleTv.setText(R.string.title_shortcut_keys_long_tap)

        chooseSingleTapGroup.clickView.setOnDebounceClickListener {
            customShortcutViewModel.setShortcutType(CustomShortcutViewModel.ShortcutType.SINGLE_TAP)
        }
        chooseLongTapGroup.clickView.setOnDebounceClickListener {
            customShortcutViewModel.setShortcutType(CustomShortcutViewModel.ShortcutType.LONG_TAP)
        }

        // 初始化RecyclerView
        chooseRv.apply {
            closeDefChangeAnimations()
            adapter = customAdapter
            layoutManager = GridLayoutManager(requireContext(), 3)
            addItemDecorationDrawable(
                RecyclerView.VERTICAL,
                R.drawable.shape_fav_app_divider_y
            )

            doOnItemClick { vh, view ->
                if (view.id == R.id.favAppActionIv) {
                    val pos = vh.bindingAdapterPosition
                    val item = customAdapter.getData(pos)
                    customShortcutViewModel.updateCustomShortcut(item)
                }
                false
            }
        }

        // 删除按钮
        chooseSingleTapGroup.delIconIv.setOnClickListener {
            customShortcutViewModel.updateCustomShortcut(null)
        }

        chooseLongTapGroup.delIconIv.setOnClickListener {
            customShortcutViewModel.updateCustomShortcut(null)
        }
    }

    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        repeatOnResume {
            customShortcutViewModel.getInstalledApps()
        }

        repeatCollectOnResume(customShortcutViewModel.shortcutTypeFlow) {
            binding.chooseSingleTapGroup.changeUISelect(it == CustomShortcutViewModel.ShortcutType.SINGLE_TAP)
            binding.chooseLongTapGroup.changeUISelect(it == CustomShortcutViewModel.ShortcutType.LONG_TAP)
        }

        repeatCollectOnResume(customShortcutViewModel.customShortcutItemsFlow) {
            customAdapter.setData(it)
        }

        // 点触
        repeatCollectOnResume(customShortcutViewModel.singleTapShortcutItemsFlow) {
            binding.chooseSingleTapGroup.updateItem(it)
        }
        // 长按
        repeatCollectOnResume(customShortcutViewModel.longTapShortcutItemsFlow){
            binding.chooseLongTapGroup.updateItem(it)
        }

        // 移除按钮可见性
        repeatCollectOnResume(customShortcutViewModel.singleTapRemoveIconVisibleFlow) {
            binding.chooseSingleTapGroup.delIconIv.gone(!it)
        }
        repeatCollectOnResume(customShortcutViewModel.longTapRemoveIconVisibleFlow) {
            binding.chooseLongTapGroup.delIconIv.gone(!it)
        }
    }

    private fun ItemShortcutChooseBinding.updateItem(item: CustomShortcutItem?) {
        emptyIv.gone(item != null)
        iconIv.gone(item == null)
        nameTv.gone(item == null)

        if (item != null) {
            iconIv.setImageDrawable(item.icon)
            nameTv.text = item.name
        } else {
            iconIv.setImageDrawable(null)
            nameTv.text = ""
        }
    }

    private fun ItemShortcutChooseBinding.changeUISelect(sel: Boolean) {
        borderView.invisible(!sel)
        val textColor = if (sel) R.color.bg_main_blue else R.color.text_common
        shortcutChooseTitleTv.setTextColor(requireContext().getColor(textColor))
        clickView.gone(sel)
    }
}