package com.czur.starry.device.settings.ui.personalization.wallpaper.vm

import android.app.Application
import android.os.Environment
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.baselib.utils.data.LiveDataDelegate
import com.czur.starry.device.baselib.utils.data.NullableLiveDataDelegate
import com.czur.starry.device.settings.ui.personalization.wallpaper.bean.CustomImageEntity
import com.czur.starry.device.settings.ui.personalization.wallpaper.bean.FileEntity
import com.czur.starry.device.settings.ui.personalization.wallpaper.`interface`.InnerServices
import com.czur.starry.device.settings.ui.personalization.wallpaper.utils.CUSTOM_ASSETS
import com.czur.starry.device.settings.ui.personalization.wallpaper.utils.RECENT_ASSETS
import com.czur.starry.device.settings.ui.personalization.wallpaper.utils.copyTask
import com.czur.starry.device.settings.ui.personalization.wallpaper.utils.getValueToList
import com.czur.starry.device.settings.ui.personalization.wallpaper.utils.getWallpaperName
import com.czur.starry.device.settings.ui.personalization.wallpaper.utils.getWallpaperSystemProp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 *  author : WangHao
 *  time   :2024/01/15
 */

private const val TAG = "WallpaperViewModel"

class WallpaperViewModel(application: Application) : AndroidViewModel(application) {
    companion object {
        //扫码上传二维码url拼接
        private const val WELCOME_SN = "sleepImage?sn="
        private const val IMAGE_TYPE_WALLPAPER = 2 //1：欢庆屏保，2：唤醒屏保
    }

    val wallpaperPath by lazy {
        Environment.getExternalStoragePublicDirectory(
            Environment.DIRECTORY_PICTURES
        )
    }
    val isShowRecent = DifferentLiveData(false)
    private var showRecent by LiveDataDelegate(isShowRecent)

    val unReceivedImageLive: LiveData<MutableList<CustomImageEntity>> = MutableLiveData()
    private var unReceivedImage by NullableLiveDataDelegate(unReceivedImageLive, null)

    //最近使用数据
    suspend fun getRecentData() = withContext(Dispatchers.IO) {
        val recentList = getValueToList(RECENT_ASSETS)
        showRecent = getWallpaperSystemProp() && recentList.size > 0

        recentList
    }

    //自定义数据
    suspend fun getCustomData() = withContext(Dispatchers.IO) {
        val list = getValueToList(CUSTOM_ASSETS)
        //adapter 数据
        val customList = mutableListOf(FileEntity())
        list.forEach {
            customList.add(it)
        }
        customList
    }


    //本地复制
    suspend fun copyToLocal(it: FileEntity) = withContext(Dispatchers.IO) {
        //复制图片
        val name = getWallpaperName(getCustomData())
        val cName =
            "$name.${it.name.substringAfterLast('.', "")}"
        val desc =
            FileEntity(
                absPath = "${wallpaperPath}/$cName",
                name = name,
                fileType = it.fileType
            )
        logTagD(TAG, "======absPath==${desc.absPath}")
        logTagD(TAG, "======copyToLocal==${desc.name}")
        doCopy(it, desc)
    }

    //复制
    private suspend fun doCopy(
        srcFile: FileEntity,
        targetFile: FileEntity,
    ) = withContext(Dispatchers.IO) {

        mkRootDir()
        val copyTask = copyTask(srcFile)
        val sumSizeMap = copyTask.computeSumSize(arrayListOf(srcFile))
        copyTask.doCopy(
            srcFile,
            targetFile,
            sumSizeMap[srcFile.absPath] ?: 0L,
        ) {
        }
        targetFile
    }

    fun mkRootDir() {
        if (!DisplayVM.rootDir.exists()) {
            DisplayVM.rootDir.mkdirs()
        }
    }


    /**
     * 查询未接收图片
     */
    suspend fun queryUnReceived() = withContext(Dispatchers.IO)
    {
        try {
            val entity =
                HttpManager.getService<InnerServices>(Constants.OTA_BASE_URL)
                    .getUnreceivedImage(Constants.SERIAL, IMAGE_TYPE_WALLPAPER)
            logTagV(TAG, "entity==${entity.code}")
            if (entity.isSuccess) {
                if (entity.bodyList.size > 0) {
                    unReceivedImage = entity.withCheck().bodyList
                }else {
                    unReceivedImage?.clear()
                }
                logTagV(TAG, "entity.withCheck().body==${entity.withCheck().bodyList}")
            } else {
                logTagD(TAG, "responseRecived失败")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }


    /**
     * 响应收到图片URL
     */
    suspend fun responseReceived(id: String) = withContext(Dispatchers.IO)
    {
        try {
            val entity =
                HttpManager.getService<InnerServices>(Constants.OTA_BASE_URL)
                    .responseImageReceived(id)
            logTagD(TAG, "entity==" + entity.code)
            if (entity.isSuccess) {
                logTagD(TAG, "responseRecived成功")
            } else {
                logTagD(TAG, "responseRecived失败")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    /**
     * 获取扫码上传url
     */
    suspend fun getUploadUrl() = withContext(Dispatchers.IO) {
        try {
            val entity =
                HttpManager.getService<InnerServices>(Constants.BASE_URL)
                    .getImageUploadUrl()
            logTagD(TAG, "entity==" + entity.code)
            if (entity.isSuccess) {
                logTagD(TAG, "getUploadUrl成功")
                //过滤末尾#符号后拼接地址
                val url = entity.body.toString().trimEnd('#') + WELCOME_SN
                url
            } else {
                logTagD(TAG, "getUploadUrl失败")
                null
            }

        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

}