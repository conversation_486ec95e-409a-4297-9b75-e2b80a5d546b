package com.czur.starry.device.settings.ui.projector.brightness

import android.app.Application
import android.content.ContentResolver
import android.provider.Settings
import androidx.lifecycle.AndroidViewModel
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.starry.device.baselib.utils.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext

/**
 * Created by 陈丰尧 on 2025/5/29
 */
private const val TAG = "ScreenBrightnessViewModel"

class ScreenBrightnessViewModel(application: Application) : AndroidViewModel(application) {

    companion object {
        const val BRIGHTNESS_MIN = 1
        const val BRIGHTNESS_MAX = 255
    }

    private val contentResolver: ContentResolver = application.contentResolver

    private val _brightnessFlow = MutableStateFlow(128)
    val brightnessFlow: StateFlow<Int> = _brightnessFlow.asStateFlow()

    private val _isLoadingFlow = MutableStateFlow(false)
    val isLoadingFlow: StateFlow<Boolean> = _isLoadingFlow.asStateFlow()

    init {
        loadCurrentBrightness()
    }

    /**
     * 加载当前亮度值
     */
    private fun loadCurrentBrightness() {
        launch {
            try {
                _isLoadingFlow.value = true
                val currentBrightness = getCurrentScreenBrightness()
                logTagD(TAG, "当前屏幕亮度: $currentBrightness")
                _brightnessFlow.value = currentBrightness
            } catch (e: Exception) {
                logTagE(TAG, "获取当前屏幕亮度失败", tr = e)
            } finally {
                _isLoadingFlow.value = false
            }
        }
    }

    /**
     * 设置屏幕亮度
     * @param brightness 亮度值，范围 [BRIGHTNESS_MIN, BRIGHTNESS_MAX]
     */
    fun setBrightness(brightness: Int) {
        val clampedBrightness = brightness.coerceIn(BRIGHTNESS_MIN, BRIGHTNESS_MAX)
        logTagD(TAG, "设置屏幕亮度: $clampedBrightness")

        launch {
            try {
                val success = setScreenBrightness(clampedBrightness)
                if (success) {
                    _brightnessFlow.value = clampedBrightness
                    logTagD(TAG, "屏幕亮度设置成功: $clampedBrightness")
                } else {
                    logTagE(TAG, "屏幕亮度设置失败: $clampedBrightness")
                }
            } catch (e: Exception) {
                logTagE(TAG, "设置屏幕亮度异常", tr = e)
            }
        }
    }

    /**
     * 获取当前屏幕亮度
     * @return 亮度值，范围 [BRIGHTNESS_MIN, BRIGHTNESS_MAX]
     */
    private suspend fun getCurrentScreenBrightness(): Int = withContext(Dispatchers.IO) {
        return@withContext try {
            Settings.System.getInt(
                contentResolver,
                Settings.System.SCREEN_BRIGHTNESS,
                128 // 默认值为128
            ).coerceIn(BRIGHTNESS_MIN, BRIGHTNESS_MAX)
        } catch (e: Exception) {
            logTagE(TAG, "读取屏幕亮度失败", tr = e)
            128
        }
    }

    /**
     * 设置屏幕亮度
     * @param brightness 亮度值，范围 [BRIGHTNESS_MIN, BRIGHTNESS_MAX]
     * @return 设置是否成功
     */
    private suspend fun setScreenBrightness(brightness: Int): Boolean = withContext(Dispatchers.IO) {
        return@withContext try {
            // 首先设置亮度模式为手动模式
            Settings.System.putInt(
                contentResolver,
                Settings.System.SCREEN_BRIGHTNESS_MODE,
                Settings.System.SCREEN_BRIGHTNESS_MODE_MANUAL
            )

            // 设置亮度值
            val result = Settings.System.putInt(
                contentResolver,
                Settings.System.SCREEN_BRIGHTNESS,
                brightness
            )

            logTagD(TAG, "设置屏幕亮度结果: $result, 亮度值: $brightness")
            result
        } catch (e: Exception) {
            logTagE(TAG, "设置屏幕亮度异常", tr = e)
            false
        }
    }
}