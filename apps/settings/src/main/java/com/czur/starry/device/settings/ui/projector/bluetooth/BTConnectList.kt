package com.czur.starry.device.settings.ui.projector.bluetooth

import android.annotation.SuppressLint
import android.bluetooth.BluetoothDevice
import com.czur.starry.device.bluetoothlib.util.BluetoothDeviceProxy
import java.lang.reflect.Method

/**
 * Created by 陈丰尧 on 2/1/21
 */
private val removeBondMethod: Method by lazy {
    val method = BluetoothDevice::class.java.getDeclaredMethod("removeBond")
    method.isAccessible = true
    method
}

/**
 * 删除配对的蓝牙设备
 * 参考 源码中 CacheBluetoothDevice
 */
@SuppressLint("MissingPermission")
fun BluetoothDevice.delBond(device: BluetoothDevice): Boolean {
    if (bondState == BluetoothDevice.BOND_BONDING){
        // 如果实在绑定中, 就取消绑定
        BluetoothDeviceProxy(device).cancelBondProcess()
    }

    return removeBondMethod.invoke(this) as <PERSON><PERSON><PERSON>
}