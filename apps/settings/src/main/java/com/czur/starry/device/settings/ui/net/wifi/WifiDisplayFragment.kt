package com.czur.starry.device.settings.ui.net.wifi

import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.fragment.app.viewModels
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.base.BaseBindingMenuFragment
import com.czur.starry.device.settings.databinding.FragmentWifiDisplayBinding

/**
 * Created by 陈丰尧 on 1/26/21
 */
class WifiDisplayFragment : BaseBindingMenuFragment<FragmentWifiDisplayBinding>() {
    companion object {
        private const val FRAGMENT_WIFI_LIST_TAG = "wifiList"
    }

    private val wifiViewModel: WifiViewModel by viewModels({ requireActivity() })

    override fun FragmentWifiDisplayBinding.initBindingViews() {
        wifiViewModel.wifiEnable.observe(viewLifecycleOwner) { wifiEnable ->
            val layoutParams = wifiEnableContent.layoutParams as ConstraintLayout.LayoutParams
            if (wifiEnable) {
                showWifiList()
            } else {
                hideWifiList()
            }
            // 设置约束到顶部: 135px
            layoutParams.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
            layoutParams.topMargin = 135
            wifiEnableContent.layoutParams = layoutParams
            wifiEnableContent.requestLayout()
        }
    }

    /**
     * 显示Wifi列表
     */
    private fun showWifiList() {
        //隐藏禁用图标
        binding.wifiDisableLayout.visibility = View.GONE
        val lastFragment = childFragmentManager.findFragmentByTag(FRAGMENT_WIFI_LIST_TAG)
        if (lastFragment == null) {
            val listFragment = WifiListFragment()
            childFragmentManager.beginTransaction()
                .add(R.id.wifiListContent, listFragment, FRAGMENT_WIFI_LIST_TAG)
                .commit()

            binding.wifiListContent.visibility = View.VISIBLE

        }
    }

    /**
     * 隐藏wifi列表页面
     */
    private fun hideWifiList() {
        // 删除Fragment
        val listFragment = childFragmentManager.findFragmentByTag(FRAGMENT_WIFI_LIST_TAG)
        listFragment?.let {
            childFragmentManager.beginTransaction()
                .remove(it)
                .commit()
        }
        binding.wifiListContent.visibility = View.INVISIBLE
        //显示禁用图标
        binding.wifiDisableLayout.visibility = View.VISIBLE
    }

}