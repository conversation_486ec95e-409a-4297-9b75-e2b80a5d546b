package com.czur.starry.device.settings.adapter

import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.baselib.base.CZURAtyManager
import com.czur.starry.device.baselib.widget.WifiItemView
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.model.WifiInfoEntity
import com.czur.starry.device.settings.ui.net.wifi.WifiViewModel
import com.czur.starry.device.settings.widget.SettingItemBg
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

class WifiListAdapter : RecyclerView.Adapter<BaseVH>() {
    private var wifiList: List<WifiInfoEntity> = listOf()
    private var wifiViewModel: WifiViewModel? = null
    private val itemColor: Int by lazy {
        CZURAtyManager.currentActivity().resources.getColor(R.color.bg_main_blue)
    }

    fun setWifiList(wifiList: List<WifiInfoEntity>) {
        this.wifiList = wifiList
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int) =
        BaseVH(R.layout.item_wifi_block, parent).apply {
            val wifiItemView = getView<WifiItemView>(R.id.wifiItem)
            val bgItem = getView<SettingItemBg>(R.id.wifiItemBg)
            wifiItemView.setOnItemHoverListener {
                bgItem.isHovered = it
            }
        }


    override fun onBindViewHolder(holder: BaseVH, position: Int) {
        val wifiInfo = wifiList[position]

        val bgItem = holder.getView<SettingItemBg>(R.id.wifiItemBg)
        bgItem.setHoverItemPosition(position, wifiList.size)
        val wifiItemView = holder.getView<WifiItemView>(R.id.wifiItem)
        wifiItemView.changeImgColorFilter(itemColor)

        wifiItemView.setWifiInfo(
            wifiInfo.ssid,
            wifiInfo.levelRaw,
            wifiInfo.locked,
            wifiInfo.save,
            wifiInfo.connecting,
            wifiInfo.isConnectedFailed
        )
        wifiItemView.setItemOnclick({ showJoinFragment(wifiInfo.bssid) })
        wifiItemView.setWifiInfoForSettings(
            wifiInfo.ssid,
            wifiInfo.save,
            wifiInfo.connecting,
            { showDetailFragement(wifiInfo.bssid) })
    }

    override fun getItemCount(): Int {
        return wifiList.size
    }

    fun getWifiEntity(pos: Int) = wifiList[pos]

    private fun showJoinFragment(bssid: String) {
        if (wifiViewModel!!.isOpenWifi(bssid)) {
            MainScope().launch {
                wifiViewModel!!.joinWifi(bssid = bssid)
            }
        } else {
            wifiViewModel!!.showJoin(bssid)
        }
    }

    fun showDetailFragement(bssid: String) {
        wifiViewModel!!.isConnectedSSID = false
        wifiViewModel!!.showDetail(bssid)
    }

    fun setwifiViewModel(wifiViewModel: WifiViewModel) {
        this.wifiViewModel = wifiViewModel
    }

    fun refreshUI() {
        notifyDataSetChanged()
    }


}