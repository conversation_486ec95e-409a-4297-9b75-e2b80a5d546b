package com.czur.starry.device.settings.ui.personalization.wallpaper.dialog

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.czur.starry.device.baselib.base.BaseDialog
import com.czur.starry.device.baselib.utils.view.findView
import com.czur.starry.device.baselib.widget.CommonButton
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.ui.personalization.wallpaper.utils.getBitmap

import java.io.File

/**
 * created by wangh 22.0728
 */

class LocalSelectDialog : BaseDialog() {


    var confirmClickListener: (() -> Unit)? = null
    var cancelClickListener: (() -> Unit)? = null
    var selectClickListener: (() -> Unit)? = null
    var confBtDark = false

    private val titleTv by findView<TextView>(R.id.titleTv)
    private val imageView by findView<ImageView>(R.id.im_view)
    private val normalDialogConfirmBtn by findView<CommonButton>(R.id.normalDialogConfirmBtn)
    private val normalDialogCancelBtn by findView<CommonButton>(R.id.normalDialogCancelBtn)
    private val layout by findView<LinearLayout>(R.id.layout_img)

    lateinit var title: String
    lateinit var image: Bitmap

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_local_select, container, false)

    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        titleTv.text = title
        imageView.setImageBitmap(image)
        if (confBtDark) {
            normalDialogConfirmBtn.isEnabled = false
        }

        normalDialogConfirmBtn.setOnClickListener {
            confirmClickListener?.invoke()
            dismiss()
        }
        normalDialogCancelBtn.setOnClickListener {
            cancelClickListener?.invoke()
            dismiss()
        }
        layout.setOnClickListener {
            selectClickListener?.invoke()
        }
    }

    class Builder {
        private var title = ""
        private var confirmClickListener: (() -> Unit)? = null
        private var cancelClickListener: (() -> Unit)? = null
        private var selectClickListener: (() -> Unit)? = null
        private var isMusic = false
        private var imageView: Bitmap? = null
        private var confBt = false


        fun setConfirmClickListener(listener: () -> Unit): Builder {
            confirmClickListener = listener
            return this
        }

        fun setSelectClickListener(listener: () -> Unit): Builder {
            selectClickListener = listener
            return this
        }

        fun setImageName(context: Context, name: String): Builder {
            title = if (name == "") {

                context.getString(R.string.wallpaper_tv_local_content)

            } else {
                name
            }

            return this
        }

        suspend fun setImageView(context: Context, path: String): Builder {
            val file = File(path)

            if (path.isNotEmpty() && file.exists()) {
                val bm = getBitmap(path, 400, 225)//BitmapFactory.decodeFile(path)
                imageView = bm
            } else {
                confBt = true
                imageView = BitmapFactory.decodeResource(
                    context.resources,
                    (R.drawable.ic_local_select)
                )
            }
            return this
        }


        fun build(): LocalSelectDialog {
            val dialog = LocalSelectDialog()
            dialog.title = title
            dialog.confirmClickListener = confirmClickListener
            dialog.cancelClickListener = cancelClickListener
            dialog.selectClickListener = selectClickListener
            dialog.image = imageView!!
            dialog.confBtDark = confBt
            return dialog
        }
    }
}