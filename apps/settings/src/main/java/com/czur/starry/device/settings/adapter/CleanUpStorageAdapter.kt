package com.czur.starry.device.settings.adapter

import android.view.ViewGroup
import androidx.recyclerview.widget.AsyncListDiffer
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import com.czur.starry.device.baselib.NotImpl
import com.czur.starry.device.baselib.base.BaseVH
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.toSizeStr
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.model.CleanUpStorageCategory
import com.czur.uilib.choose.CZCheckBox
import com.czur.uilib.choose.CZMultiStateCheckBox

/**
 * Created by 陈丰尧 on 2023/8/4
 */
private const val VIEW_TYPE_CATEGORY = 0    // 分类
private const val VIEW_TYPE_ITEM = 1        // 条目

const val CATEGORY_SEL_MODE_NONE = 0    // 未选中
const val CATEGORY_SEL_MODE_ALL = 1     // 全选中
const val CATEGORY_SEL_MODE_PART = 2    // 部分选中

data class CleanUpAdapterCategory(
    val category: CleanUpStorageCategory,   // 分类
    val sizeSum: Long,     // 分类大小
    val selMode: Int,   // 选中状态, 0: 未选中, 1: 选中, 2: 部分选中
    val itemList: List<CleanUpAdapterItem>,   // 条目列表
    val expand: Boolean,     // 是否展开
    val isLoading: Boolean,  // 是否正在加载
)

data class CleanUpAdapterItem(
    val clearKey: String,   // 清理的key 有可能是包名/文件路径
    val packageName: String,   // 清理的key 有可能是包名/文件路径
    val name: String,       // 显示名称
    val size: Long,         // 大小
    val category: CleanUpStorageCategory,   // 分类
    val isSelect: Boolean = false   // 是否选中
)

class CleanUpStorageAdapter : RecyclerView.Adapter<BaseVH>() {
    // 每一条的数据
    private var rawData = emptyList<CleanUpAdapterCategory>()
    var onExpandChangeListener: ((category: CleanUpStorageCategory, expand: Boolean) -> Unit)? =
        null

    // category整体选中状态切换
    var onCategorySelModeChangeListener: ((category: CleanUpAdapterCategory, select: Boolean) -> Unit)? =
        null

    // item选中状态切换
    var onItemSelChangeListener: ((item: CleanUpAdapterItem, select: Boolean) -> Unit)? = null

    private val itemCallback = object : DiffUtil.ItemCallback<Any>() {
        override fun areItemsTheSame(oldItem: Any, newItem: Any): Boolean {

            if (oldItem is CleanUpAdapterCategory && newItem is CleanUpAdapterCategory) {
                return oldItem.category == newItem.category
            } else if (oldItem is CleanUpAdapterItem && newItem is CleanUpAdapterItem) {
                return oldItem.clearKey == newItem.clearKey
            }
            return false
        }

        override fun areContentsTheSame(oldItem: Any, newItem: Any): Boolean {
            return if (oldItem is CleanUpAdapterCategory && newItem is CleanUpAdapterCategory) {
                oldItem.category == newItem.category
                        && oldItem.sizeSum == newItem.sizeSum
                        && oldItem.selMode == newItem.selMode
                        && oldItem.expand == newItem.expand
                        && oldItem.isLoading == newItem.isLoading
            } else if (oldItem is CleanUpAdapterItem && newItem is CleanUpAdapterItem) {
                oldItem.clearKey == newItem.clearKey
                        && oldItem.name == newItem.name
                        && oldItem.size == newItem.size
                        && oldItem.category == newItem.category
                        && oldItem.isSelect == newItem.isSelect
            } else {
                false
            }
        }
    }
    private val differ = AsyncListDiffer(this, itemCallback)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseVH {
        return when (viewType) {
            VIEW_TYPE_CATEGORY -> {
                BaseVH(R.layout.memory_clean_super, parent).apply {
                    itemView.setOnDebounceClickListener {
                        val category = tag as CleanUpAdapterCategory
                        if (category.itemList.isNotEmpty()) {
                            onExpandChangeListener?.invoke(category.category, !category.expand)
                        }
                    }

                    val checkBox = getView<CZMultiStateCheckBox>(R.id.categoryCb)
                    checkBox.setOnCheckedChangeListener { state, fromUser ->
                        if (fromUser) {
                            val category = tag as CleanUpAdapterCategory
                            val select = when (state) {
                                CZMultiStateCheckBox.CheckStatus.UNCHECKED -> false
                                CZMultiStateCheckBox.CheckStatus.CHECKED -> true
                                else -> false
                            }
                            onCategorySelModeChangeListener?.invoke(category, select)
                        }
                    }
                }
            }

            VIEW_TYPE_ITEM -> {
                BaseVH(R.layout.memory_clean_child, parent).apply {
                    val checkBox = getView<CZCheckBox>(R.id.cleanItemCb)
                    checkBox.setOnCheckedChangeListener { state, fromUser ->
                        if (fromUser) {
                            val item = tag as CleanUpAdapterItem
                            onItemSelChangeListener?.invoke(item, state)
                        }
                    }
                }
            }

            else -> NotImpl()
        }
    }

    override fun onBindViewHolder(holder: BaseVH, position: Int) {
        holder.tag = differ.currentList[position]
        when (holder.itemViewType) {
            VIEW_TYPE_CATEGORY -> {
                // 分类
                val category = differ.currentList[position] as CleanUpAdapterCategory
                holder.setText(category.category.nameRes, R.id.categoryNameTv)
                if (category.expand) {
                    holder.setImgResource(R.drawable.ic_clean_storage_group_expand, R.id.expendIv)
                } else {
                    holder.setImgResource(R.drawable.ic_clean_storage_group_shrink, R.id.expendIv)
                }
                holder.setText(category.sizeSum.toSizeStr(), R.id.categorySizeTv)
                holder.visible(category.isLoading, R.id.progressbar)
                    .visible(!category.isLoading, R.id.categorySizeTv)

                val checkBox = holder.getView<CZMultiStateCheckBox>(R.id.categoryCb)
                val checkMode = when (category.selMode) {
                    CATEGORY_SEL_MODE_NONE -> CZMultiStateCheckBox.CheckStatus.UNCHECKED
                    CATEGORY_SEL_MODE_ALL -> CZMultiStateCheckBox.CheckStatus.CHECKED
                    CATEGORY_SEL_MODE_PART -> CZMultiStateCheckBox.CheckStatus.HALF_CHECKED
                    else -> CZMultiStateCheckBox.CheckStatus.UNCHECKED
                }
                checkBox.setCheckState(checkMode)
            }

            VIEW_TYPE_ITEM -> {
                // 条目
                val item = differ.currentList[position] as CleanUpAdapterItem
                holder.setText(item.name, R.id.cleanItemNameTv)
                holder.setText(item.size.toSizeStr(), R.id.cleanItemSizeTv)
                val checkBox = holder.getView<CZCheckBox>(R.id.cleanItemCb)
                checkBox.setChecked(item.isSelect)
            }
        }
    }

    override fun getItemId(position: Int): Long {
        return if (getItemViewType(position) == VIEW_TYPE_CATEGORY) RecyclerView.NO_ID else position.toLong()
    }

    override fun getItemViewType(position: Int): Int {
        return if (differ.currentList[position] is CleanUpAdapterCategory) {
            VIEW_TYPE_CATEGORY
        } else {
            VIEW_TYPE_ITEM
        }
    }

    override fun getItemCount(): Int {
        return differ.currentList.size
    }

    fun setData(newData: List<CleanUpAdapterCategory>) {

        val dataList = mutableListOf<Any>()
        newData.forEach { category ->
            dataList.add(category)
            if (category.expand) {
                dataList.addAll(category.itemList)
            }
        }
        differ.submitList(dataList)
        rawData = newData
    }

    fun getSelectItem(): List<CleanUpAdapterItem> {
        val list = mutableListOf<CleanUpAdapterItem>()
        rawData.forEach {
            it.itemList.forEach { item ->
                if (item.isSelect) {
                    list.add(item)
                }
            }
        }
        return list
    }

}