package com.czur.starry.device.settings.model

import android.os.Parcelable
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.hw.Q1Series
import com.czur.starry.device.baselib.common.hw.Q2Series
import com.czur.starry.device.baselib.common.hw.StarryModel
import com.czur.starry.device.baselib.common.hw.StudioSeries
import com.czur.starry.device.settings.R
import com.czur.starry.device.settings.ui.personalization.wallpaper.bean.FileEntity
import kotlinx.parcelize.Parcelize

/**
 * Created by 陈丰尧 on 2024/7/23
 */

enum class BackdropType { FIXED, CUSTOM }

@Parcelize
data class BackdropItem(
    val name: String,
    val tag: BackdropTagEntity,
    val imgRes: Int
) : Parcelable

@Parcelize
data class BackdropListItem(
    val fileType: BackdropType,
    val fixedEntity: BackdropItem? = null,
    val customEntity: FileEntity? = null,
) : Parcelable

@Parcelize
sealed class BackdropTagEntity(
    val key: String,
    val name: Int
) : Parcelable

object BackdropTag {
    data object All : BackdropTagEntity("all", R.string.str_screen_backdrop_tag_all)

    // 抽象
    data object Abstract : BackdropTagEntity("abstract", R.string.str_screen_backdrop_tag_abstract)

    // 唯美
    data object Aestheticism :
        BackdropTagEntity("aestheticism", R.string.str_screen_backdrop_tag_aestheticism)

    // 艺术
    data object Art : BackdropTagEntity("art", R.string.str_screen_backdrop_tag_art)

    // 自然
    data object Natural : BackdropTagEntity("natural", R.string.str_screen_backdrop_tag_natural)

    // 自定义
    data object Custom : BackdropTagEntity("custom", R.string.str_screen_backdrop_tag_custom)

    val allBackdropTag by lazy(LazyThreadSafetyMode.NONE) {
        buildList {
            add(All)
            add(Natural)
            add(Aestheticism)
            add(Abstract)
            add(Art)
            when (val s = Constants.starryHWInfo.series) {
                is Q1Series -> when (s.model) {
                    StarryModel.Q1Model.Q1 -> {}
                    else -> add(Custom)
                }
                Q2Series -> when (s.model) {
                    StarryModel.Q2Model.Q2 -> {}
                    else -> add(Custom)
                }
                StudioSeries -> when (s.model) {
                    StarryModel.StudioModel.Studio -> {}
                    else -> add(Custom)
                }
            }

        }
    }

    /**
     * 获取背景图tag
     */
    fun getBackdropTag(key: String): BackdropTagEntity {
        return when (key) {
            Abstract.key -> Abstract
            Aestheticism.key -> Aestheticism
            Art.key -> Art
            Natural.key -> Natural
            Custom.key -> Custom
            else -> All
        }
    }
}