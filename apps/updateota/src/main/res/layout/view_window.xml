<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/view_widonw"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center">


    <RelativeLayout
        android:id="@+id/ly_textv"
        android:layout_width="500px"
        android:layout_height="360px"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/shape_corner"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="top"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="25px"
                android:includeFontPadding="false"
                android:text="@string/dialog_normal_title_tips"
                android:textColor="@color/white"
                android:textSize="36px"
                android:textStyle="bold" />


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_msg"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:layout_marginLeft="40px"
                android:layout_marginRight="40px"
                android:gravity="center"
                android:text="@string/updating_message_formate"
                android:textColor="@color/white"
                android:textSize="24px"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/btn_group_horizontal"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="30px"
            android:gravity="bottom|center"
            android:orientation="horizontal">

            <com.czur.starry.device.baselib.widget.CommonButton
                android:id="@+id/cancel_bt"
                android:layout_width="180px"
                android:layout_height="50px"
                app:baselib_theme="dark"
                android:text="@string/dialog_normal_cancel"
                android:textSize="20px"
                android:textStyle="bold" />

            <com.czur.starry.device.baselib.widget.CommonButton
                android:id="@+id/confirm_bt"
                android:layout_width="180px"
                android:layout_height="50px"
                android:layout_marginLeft="30px"
                android:text="@string/dialog_normal_confirm"
                android:textSize="20px"
                android:textStyle="bold"
                app:baselib_theme="white2" />
        </LinearLayout>

    </RelativeLayout>


</androidx.constraintlayout.widget.ConstraintLayout>