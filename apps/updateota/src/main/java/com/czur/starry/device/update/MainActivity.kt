package com.czur.starry.device.update

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity



class MainActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val bootIntent = Intent(this, UpdateService::class.java)
        this!!.startService(bootIntent)
        finish()
    }

}