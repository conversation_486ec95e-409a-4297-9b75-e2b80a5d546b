package com.czur.starry.device.update.task

import com.czur.czurutils.encryption.md5
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.common.Constants.PATH_SDCARD
import com.czur.starry.device.baselib.common.Constants.PATH_SDCARD_OTA_CAMERA
import com.czur.starry.device.otalib.OTAHandler
import com.czur.starry.device.otalib.OTAHandler.allMeetingStatus
import com.czur.starry.device.settings.model.FWVersionModel
import com.czur.starry.device.update.utils.CameraOTAUtil
import com.czur.starry.device.update.utils.MD5Utils
import kotlinx.coroutines.runBlocking
import java.io.File
import java.util.TimerTask
import java.util.concurrent.atomic.AtomicBoolean


class CameraOTATask : TimerTask() {
    companion object {
        const val TAG = "CameraOTATask"
        var isCameraOTARunning = AtomicBoolean(false)
        var fwVersion: FWVersionModel? = null
        var downloadResult = false
    }

    override fun run() {
        //正在运行，视频会议
        if (isCameraOTARunning.get() || allMeetingStatus) {
            return
        }

        runBlocking {
            fwVersion = CameraOTAUtil.checkVersionRequest()
        }

        if (fwVersion == null || fwVersion!!.update == 0) {
            return
        }
        logTagD(TAG, "=======fwVersion===" + fwVersion.toString())
        
        if (OTAHandler.newCameraVersion == fwVersion!!.version) {
            return
        }

        isCameraOTARunning.set(true)

        val packageUrl = fwVersion!!.packageUrl
        val md5 = fwVersion!!.md5
        if (fwVersion!!.version != null && packageUrl != null && md5 != null) {
            val lastFile = File(PATH_SDCARD_OTA_CAMERA)
            if (lastFile.exists()) {
                lastFile.delete()
            }

            logTagD(TAG, fwVersion.toString())
            val downloadFilePath = PATH_SDCARD + fwVersion!!.packageUrl.substringAfterLast("/")

            logTagD(TAG, downloadFilePath)
            logTagD(TAG, packageUrl)
            runBlocking {
                downloadResult = CameraOTAUtil.downloadOTAFile(
                    downloadFilePath,
                    fwVersion!!.packageUrl,
                    fwVersion!!.fileSize
                )
            }
            if (downloadResult) {
                logTagD(TAG, "OTA文件下载成功")
                val checkMD5Result = checkMD5(downloadFilePath, md5)
                if (checkMD5Result) {
                    logTagD(TAG, "OTA文件MD5相同")
                    File(downloadFilePath).renameTo(File(PATH_SDCARD_OTA_CAMERA))
                    OTAHandler.newCameraVersion = fwVersion!!.version
                } else {
                    logTagD(TAG, "OTA文件MD5不相同，删除文件")
                    File(downloadFilePath).delete()
                }
            } else {
                isCameraOTARunning.set(false)
                logTagD(TAG, "OTA文件下载失败")
            }
        } else {
            logTagD(TAG, "不需要升级")
        }
        logTagD(TAG, "下载流程结束")
        isCameraOTARunning.set(false)
    }

    private fun checkMD5(filePath: String, checkMD5: String): Boolean {
        val checkFile = File(filePath)
        if (!checkFile.exists()) {
            return false
        }
        val md5 = runBlocking { checkFile.md5() }
        return md5 == checkMD5
    }


}