package com.czur.starry.device.update

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.common.KEY_LAUNCHER_BOOT_COMPLETE
import com.czur.starry.device.baselib.common.KEY_STARTUP_COMPLETE
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.otalib.OTAHandler
import com.czur.starry.device.update.UpdateService.Companion.COMMAND_CHECK_USB_UPDATING


class UpdateReceiver : BroadcastReceiver() {
    private val TAG = "UpdateReceiver"

    companion object {
        private var isBootCompleted = false
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        val action = intent?.action
        logTagD(TAG, "action====$action")
        if (action == Intent.ACTION_BOOT_COMPLETED) {
            //开机启动
            if (getBooleanSystemProp(KEY_STARTUP_COMPLETE,false)) {
                isBootCompleted = true
            }

        } else if (isBootCompleted && (action == Intent.ACTION_MEDIA_MOUNTED)) {
            //usb 挂载
            if (getBooleanSystemProp(KEY_LAUNCHER_BOOT_COMPLETE, false)) {
                if (!OTAHandler.allMeetingStatus) {
                    val bootIntent = Intent(context, UpdateService::class.java)
                    bootIntent.apply {
                        putExtra("command", COMMAND_CHECK_USB_UPDATING)
                    }
                    context!!.startService(bootIntent)
                }
            } else {
                logTagW(TAG, "launcher not boot complete")
            }

        }/*else if(isBootCompleted && action == UsbManager.ACTION_USB_STATE){
            val extras = intent!!.extras
            val connected = extras!!.getBoolean(UsbManager.USB_CONNECTED)
            val configured = extras!!.getBoolean(UsbManager.USB_CONFIGURED)
            val mtpEnabled = extras!!.getBoolean(UsbManager.USB_FUNCTION_MTP)
            if ((!connected) && mtpEnabled && (!configured)) {
                val bootIntent = Intent(context, UpdateService::class.java)
                bootIntent.apply {
                    putExtra("command",COMMAND_CHECK_USB_UPDATING)
                }
                context!!.startService(bootIntent)
            }
        }*/
    }

}