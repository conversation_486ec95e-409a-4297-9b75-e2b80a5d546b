package com.czur.starry.device.update.utils

import android.os.Build
import android.os.Handler
import android.os.Looper
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.Constants.OTA_APP_ID
import com.czur.starry.device.baselib.network.HttpManager
import com.czur.starry.device.otalib.OTAHandler
import com.czur.starry.device.otalib.OTAHandler.rtmConnectedStatus
import com.czur.starry.device.settings.model.FWVersionModel
import com.czur.starry.device.update.UpdateApp.Companion.app
import com.czur.starry.device.update.UpdateService.Companion.rtmLoginListener
import com.czur.starry.device.update.`interface`.InnerServices
import com.czur.starry.device.update.model.VersionUpdateRequest
import com.czur.starry.device.update.utils.HandleUtils.startOTATask
import com.google.gson.Gson
import io.agora.rtm.*
import io.agora.rtm.RtmStatusCode.ConnectionState.*
import kotlinx.coroutines.*
import java.util.concurrent.atomic.AtomicBoolean

object RTMUtil {
    private const val TAG = "RTMUtil"

    // RTM 客户端实例
    private var mRtmClient: RtmClient? = null

    // RTM 频道实例
    private var mRtmChannel: RtmChannel? = null

    //RTM Channel name
    private val ChannelName = Constants.OTA_ONLINE_CHANNEL

    //RTM Peer name
    private var peer_id = Constants.OTA_CLIENT_ID
    private val gson by lazy { Gson() }
    var version: FWVersionModel? = null

    private val mHandler: Handler by lazy {
        Handler(Looper.getMainLooper())
    }

    //倒计时30s退出
    private const val DELAY_STOP_TIME = 30000L


    /*连接状态
     CONNECTION_STATE_DISCONNECTED（用户未连接）
     CONNECTION_STATE_CONNECTING（用户正在连接）
     CONNECTION_STATE_CONNECTED（用户已连接）
     CONNECTION_STATE_RECONNECTING（用户正在重连）
     CONNECTION_STATE_ABORTED（用户被踢出）
 */

    fun stopRTM() {
        MainScope().launch {
            mRtmClient?.logout(null)
            mRtmChannel?.leave(null)

            mRtmChannel?.release()
            mRtmClient?.release()
            //登陆失败或超时
            rtmLoginListener.emit(false)
        }
    }

    /**
     * 请求Token 登陆RTM
     */
    suspend fun requestToken() : String = withContext(Dispatchers.IO)
    {
        try {
            val entity =
                HttpManager.getService<InnerServices>(Constants.OTA_BASE_URL).requestToken()
            if (entity.isSuccess) {
                entity.body
            } else {
                logTagD(TAG, "requestToken 取token失败")
                "null"
            }
        } catch (e: Exception) {
            e.printStackTrace()
            "null"
        }

    }




    private var TimeRunnable = Runnable {
        logTagD(TAG, "===TimeRunnable==")
        stopRTM()
    }

    fun stopCountTime() {
        mHandler.removeCallbacks(TimeRunnable)
    }

    fun startCountTime() {
        mHandler.postDelayed(TimeRunnable, DELAY_STOP_TIME)
    }

    suspend fun initRTMClient() = withContext(Dispatchers.IO) {
        try {
            val appID = OTA_APP_ID

            if (mRtmClient != null) mRtmClient!!.release()

            mRtmClient = RtmClient.createInstance(app, appID, object : RtmClientListener {
                override fun onConnectionStateChanged(state: Int, reason: Int) {
                    logTagD(TAG, "Connection state changed to " + state + "Reason: " + reason + "\n")
                    when (state) {
                        CONNECTION_STATE_CONNECTED -> {
                            //链接
                            logTagD(TAG, "CONNECTION_STATE_CONNECTED")
                            stopCountTime()
                            sendPeerMessage()
                            rtmConnectedStatus = true
                            MainScope().launch {
                                rtmLoginListener.emit(true)
                            }
                        }
                        CONNECTION_STATE_RECONNECTING -> {
                            rtmConnectedStatus = false
                            logTagD(TAG, "CONNECTION_STATE_RECONNECTING")
                            startCountTime()
                        }
                        CONNECTION_STATE_DISCONNECTED -> {
                            rtmConnectedStatus = false
                            logTagD(TAG, "CONNECTION_STATE_DISCONNECTED")
                            stopRTM()
                        }
                        CONNECTION_STATE_ABORTED -> {
                            rtmConnectedStatus = false
                            //被踢出
                            logTagD(TAG, "CONNECTION_STATE_ABORTED")
                            stopRTM()

                        }
                    }


                }

                override fun onMessageReceived(rtmMessage: RtmMessage?, peerId: String?) {
                    logTagD(TAG, "onMessageReceived===" + rtmMessage?.text)
                    version = gson.fromJson(rtmMessage?.text, FWVersionModel::class.java)
                    if (version!!.update == 1) {
                        startOTATask(true)
                    }
                }

                override fun onImageMessageReceivedFromPeer(p0: RtmImageMessage?, p1: String?) {
                }

                override fun onFileMessageReceivedFromPeer(p0: RtmFileMessage?, p1: String?) {
                }

                override fun onMediaUploadingProgress(p0: RtmMediaOperationProgress?, p1: Long) {
                }

                override fun onMediaDownloadingProgress(p0: RtmMediaOperationProgress?, p1: Long) {
                }

                override fun onTokenExpired() {
                    logTagD(TAG, "====onTokenExpired==")
                    MainScope().launch {
                        val newToken = requestToken()
                        if (newToken != "null")
                            mRtmClient!!.renewToken(newToken, object : ResultCallback<Void> {
                                override fun onSuccess(p0: Void?) {
                                    logTagD(TAG, "renewToken====success")
                                }

                                override fun onFailure(p0: ErrorInfo?) {
                                    logTagD(TAG, "renewToken====onFailure")
                                }

                            })
                    }


                }

                override fun onPeersOnlineStatusChanged(p0: MutableMap<String, Int>?) {
                }

            })

        } catch (e: Exception) {
            logTagE(TAG, "RTM initialization failed!")
            e.printStackTrace()
//            throw RuntimeException("RTM initialization failed!")
        }
    }





    suspend fun loginRTM(token: String) = withContext(Dispatchers.IO) {
        logTagD(TAG, "====token=$token")

        mRtmClient!!.login(token, Constants.SERIAL, object : ResultCallback<Void> {
            override fun onSuccess(p0: Void?) {
                logTagD(TAG, "====login onSuccess=")
                //登录成功，发送点对点消息
//                sendPeerMessage()
                //加入频道
                joinChannel()

            }

            override fun onFailure(p0: ErrorInfo?) {
                logTagD(TAG, "====login failed=")
                MainScope().launch {
                    rtmLoginListener.emit(false)
                }
            }

        })
        //检测用户是否在线
        /* var var1 = HashSet<String>()
          var1.add(peer_id)
          mRtmClient!!.queryPeersOnlineStatus( var1, object :ResultCallback<Map<String, Boolean>>{
              override fun onSuccess(p0: Map<String, Boolean>?) {
                  logTagD(TAG,"onSuccess=p0===="+p0.toString())
              }

              override fun onFailure(p0: ErrorInfo?) {
                  logTagD(TAG,"onFailure==p0==="+p0.toString())
              }

          })*/

    }

    /**
     * 每次发送点对点消息，如有未上传的图片，会发送所有上传的图片。
     * version!!.versionId == 0
     * */
    fun sendPeerMessage() {
        if (null == mRtmClient) return
        OTAHandler.imageReceivedStatus = false
        val message = mRtmClient!!.createMessage()
        val content = VersionUpdateRequest(Constants.FIRMWARE_NAME)
        message.text = gson.toJson(content)
        logTagD(TAG, "====message.text success=" + message.text)
        val option = SendMessageOptions().apply {
            enableOfflineMessaging = true
        }
        if (mRtmClient != null)
            mRtmClient!!.sendMessageToPeer(peer_id, message, option, object : ResultCallback<Void> {
                override fun onSuccess(p0: Void?) {
                    logTagD(TAG, "====sendMessageToPeer success=")
                }

                override fun onFailure(p0: ErrorInfo?) {
                    logTagD(TAG, "====sendMessageToPeer failed=" + p0.toString())
                }

            })
    }

    fun joinChannel() {
        // 创建频道监听器
        val mRtmChannelListener = object : RtmChannelListener {
            override fun onMemberCountUpdated(p0: Int) {
                TODO("Not yet implemented")
            }

            override fun onAttributesUpdated(p0: MutableList<RtmChannelAttribute>?) {
                TODO("Not yet implemented")
            }

            override fun onMessageReceived(message: RtmMessage?, fromMember: RtmChannelMember?) {
                logTagD(TAG, "===JoinChannel==onMessageReceived====" + message!!.text)
            }

            override fun onImageMessageReceived(p0: RtmImageMessage?, p1: RtmChannelMember?) {
                TODO("Not yet implemented")
            }

            override fun onFileMessageReceived(p0: RtmFileMessage?, p1: RtmChannelMember?) {
                TODO("Not yet implemented")
            }

            override fun onMemberJoined(p0: RtmChannelMember?) {
                TODO("Not yet implemented")
            }

            override fun onMemberLeft(p0: RtmChannelMember?) {
                TODO("Not yet implemented")
            }

        }
        try {
            // 创建 RTM 频道
            if (mRtmChannel != null) mRtmChannel!!.release()
            mRtmChannel = mRtmClient!!.createChannel(ChannelName, mRtmChannelListener)
            // 加入 RTM 频道
            mRtmChannel!!.join(object : ResultCallback<Void> {
                override fun onSuccess(responseInfo: Void?) {
                    logTagD(TAG, "join Channel success")
                }

                override fun onFailure(errorInfo: ErrorInfo?) {
                    logTagD(TAG, "failed to join the channel")
                }

            })
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}