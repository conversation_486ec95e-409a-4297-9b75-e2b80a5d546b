package com.czur.starry.device.update.ui

import android.app.Dialog
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.ActivityInfo
import android.os.Bundle
import android.os.storage.StorageManager
import android.os.storage.VolumeInfo
import android.view.Gravity
import android.view.ViewGroup
import android.view.WindowManager
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.utils.prop.setBooleanSystemProp
import com.czur.starry.device.baselib.utils.view.getVolumes
import com.czur.starry.device.baselib.widget.CommonButton
import com.czur.starry.device.otalib.OTAHandler
import com.czur.starry.device.otalib.OTAHandler.newVersionStatus
import com.czur.starry.device.update.R
import com.czur.starry.device.update.utils.RKRecoverySystem
import kotlinx.coroutines.*
import java.io.File
import java.util.concurrent.atomic.AtomicBoolean

/**
 * created by wangh on 23.0322
 */

private const val TAG = "UpdateDialog"

class UpdateDialog : Dialog {
    private val KEY_FORCE_UPGRATE = "vendor.czur.force.update"
    private var mImageFilePath = ""
    private var context: Context
    private var onCancelClick: ((dialog: UpdateDialog) -> Unit)? = null
    private var isLive = AtomicBoolean(false)
    var job: Job? = null

    private val cancelBt: CommonButton by lazy { findViewById<CommonButton>(R.id.cancel_bt)!! }
    private val confirmBt: CommonButton by lazy { findViewById<CommonButton>(R.id.confirm_bt)!! }

    constructor(
        context: Context,
        path: String,
        theme: Int,
        onCancelClick: ((dialog: UpdateDialog) -> Unit)? = null
    ) : super(context, theme) {
        mImageFilePath = path
        this.context = context
        this.onCancelClick = onCancelClick
        val dialogWindow = window
        val lp = dialogWindow!!.attributes
        lp.width = ViewGroup.LayoutParams.WRAP_CONTENT
        lp.height = ViewGroup.LayoutParams.WRAP_CONTENT
        lp.screenOrientation = ActivityInfo.SCREEN_ORIENTATION_NOSENSOR
        dialogWindow!!.attributes = lp
        dialogWindow.setType(WindowManager.LayoutParams.TYPE_KEYGUARD_DIALOG);
        dialogWindow!!.setGravity(Gravity.CENTER)
        dialogWindow.setDimAmount(0.5f)

        this.setContentView(R.layout.view_window)

    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window?.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        if (OTAHandler.forceVersionStatus) {
            setBooleanSystemProp(KEY_FORCE_UPGRATE, true)
            cancelBt.isEnabled = false
        }


        cancelBt.setOnClickListener {
            onCancelClick?.invoke(this)
        }
        confirmBt.setOnClickListener {
            dismiss()
            VerifyDialog(context, mImageFilePath).show()
            logTagD(TAG, "=mImageFilePath==$mImageFilePath=" )
        }

        startUSBReceiver()

        job = MainScope().launch(Dispatchers.IO) {
            isLive.set(true)
            usbListener()
        }
    }

    override fun onStop() {
        super.onStop()
        stopUsbJob()
        window?.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }

    override fun onBackPressed() {
        logTagV(TAG, "onBackPressed")
        if (OTAHandler.forceVersionStatus) return
        onCancelClick?.invoke(this)
        super.onBackPressed()
    }

    fun startUSBReceiver() {
        val filter = IntentFilter()
        filter.addAction(Intent.ACTION_MEDIA_UNMOUNTED)
        filter.addDataScheme("file")
        context.registerReceiver(mReceiver, filter)
    }

    private fun stopUsbJob() {
        dismiss()
        job?.cancel()
        isLive.set(false)
        context.unregisterReceiver(mReceiver)
    }

    private val mReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            logTagW("mReceiver.onReceive() : 'action' =${intent.action}")
            if (intent.action === Intent.ACTION_MEDIA_UNMOUNTED) {
                val path = intent.data!!.path?.replace("storage/", "")
                logTagW(
                    TAG,
                    "mReceiver.onReceive() : original mount point : $path; image file path : ${mImageFilePath}"
                )
                if (mImageFilePath != null && mImageFilePath!!.contains(path!!)) {
                    logTagW(
                        TAG,
                        "mReceiver.onReceive() : Media that img file live in is unmounted, to finish this activity."
                    )
                    dismiss()
                }
            }
        }
    }

    private suspend fun usbListener() = withContext(Dispatchers.IO) {
        val storageManager =
            context?.getSystemService(Context.STORAGE_SERVICE) as StorageManager
        while (isLive.get()) {
            delay(2000)
            val mList = mutableListOf<Pair<String, String>>()
            var volumes: MutableList<VolumeInfo>? = null
            volumes = storageManager.getVolumes()
            volumes!!.forEach {
                if (it.type == VolumeInfo.TYPE_PUBLIC) {
                    val disk = it.getDisk()
                    if (disk != null && disk.isUsb) {
                        val str = it.path
                        if (str != null) {
                            val path = str.replace("/", "").replace("storage", "")
                            if (doTestReadAndWrite(it.path)) {
                                var label = disk.label
                                val usbEle = Pair<String, String>(path, label)
                                mList.add(usbEle)
                            }
                        }

                    }
                }
            }
        }

    }


    private fun doTestReadAndWrite(USB_PATH: String): Boolean {
        val directoryName = "$USB_PATH/testota"
        val directory = File(directoryName)
        try {

            if (!directory.isDirectory) {
                if (!directory.mkdirs()) {
                    return false
                } else {
                    directory.delete()
                    return true
                }
            }

        } catch (e: Exception) {
            e.printStackTrace()
            return false
        }
        return false
    }
}