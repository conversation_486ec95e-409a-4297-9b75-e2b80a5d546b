package com.czur.starry.device.update.utils

import android.content.Context
import android.os.RecoverySystem
import android.os.storage.StorageManager
import android.os.storage.VolumeInfo
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagE
import com.czur.starry.device.baselib.utils.prop.getStringSystemProp
import com.czur.starry.device.baselib.utils.view.getVolumes
import com.czur.starry.device.update.UpdateApp.Companion.app
import com.czur.starry.device.update.UpdateService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileReader
import java.io.FileWriter
import java.io.IOException
import java.security.GeneralSecurityException

class RKRecoverySystem {

    companion object {
        private const val TAG = "RKRecoverySystem"
        private val RECOVERY_DIR = File("/cache/recovery")
        private val UPDATE_FLAG_FILE = File(RECOVERY_DIR, "last_flag")
        private const val OTA_PACKAGE_FILE = "update.zip"
        private const val RK_IMAGE_FILE = "update.img"

        @Throws(IOException::class)
        fun installPackage(context: Context?, packageFile: File) {

            val canonicalPath = packageFile.canonicalPath
            writeFlagCommand(canonicalPath)

            logTagD(TAG, "===canonicalPath=$canonicalPath=")
            val newPath = if (canonicalPath.startsWith("/storage/emulated/0")) {
                canonicalPath.replace("/storage/emulated/0", "/data/media/0")
            } else {
                if (canonicalPath.startsWith("/storage")) canonicalPath.replace(
                    "/storage",
                    "/mnt/media_rw"
                ) else canonicalPath
            }
            logTagD(TAG,  "installPackage update $canonicalPath to $newPath")
            RecoverySystem.installPackage(context, File(newPath))
        }

        /**
         * 校验ota包是否与设备匹配
         */
        suspend fun doesOtaPackageMatchProduct(str: String) = withContext(Dispatchers.IO) {
            var newPath = str
            if (newPath.startsWith("/data/media")) {
                newPath = newPath.replace("/data/media", "/storage/emulated")
            } else if (newPath.startsWith("/mnt/media_rw")) {
                newPath = newPath.replace("/mnt/media_rw", "/storage")
            }
            try {
                RecoverySystem.verifyPackage(File(newPath), null, null)
                true
            } catch (e: GeneralSecurityException) {
                logTagE(TAG, "doesImageMatchProduct(): verifyPackage failed!", tr = e)
                false
            } catch (e2: Exception) {
                logTagE(TAG, "doesImageMatchProduct(): verifyPackage failed!", tr = e2)
                false
            }
        }


        @Throws(IOException::class)
        fun writeFlagCommand(path: String) {
            RECOVERY_DIR.mkdirs()
            UPDATE_FLAG_FILE.delete()
            val writer = FileWriter(UPDATE_FLAG_FILE)
            try {
                writer.write("updating\$path=$path")
            }catch (e: Exception) {
                e.printStackTrace()
            } finally {
                writer.close()
            }
        }


        fun readFlagCommand(): String? {
            if (UPDATE_FLAG_FILE.exists()) {
                logTagD(TAG, "UPDATE_FLAG_FILE is exists")
                val buf = CharArray(128)
                var readCount = 0
                try {
                    val reader = FileReader(UPDATE_FLAG_FILE)
                    readCount = reader.read(buf, 0, buf.size)
                    logTagD(TAG, "readCount = " + readCount + " buf.length = " + buf.size)
                } catch (e: IOException) {
                    logTagE(TAG, "can not read /cache/recovery/last_flag!")
                } finally {
                    UPDATE_FLAG_FILE.delete()
                }
                val sBuilder = StringBuilder()
                for (i in 0 until readCount) {
                    sBuilder.append(buf[i])
                }
                return sBuilder.toString()
            } else {
                return null
            }

        }


        fun deletePackage(path: String) {
            logTagD(TAG, "try to deletePackage...")
            if (path.startsWith("@")) {
                var fileName = "/storage/emulated/0/update.zip"
                logTagD(TAG, "ota was maped, so try to delete path = $path")
                var fOta = File(fileName)
                if (fOta.exists()) {
                    fOta.delete()
                    logTagD(TAG, "delete complete! path=$fileName")
                } else {
                    fileName = "/data/media/0/update.img"
                    fOta = File(fileName)
                    if (fOta.exists()) {
                        fOta.delete()
                        logTagD(TAG, "delete complete! path=$fileName")
                    } else {
                        logTagD(TAG, "path = $fileName, file not exists!")
                    }
                }
            }
            val f = File(path)
            if (f.exists()) {
                f.delete()
                logTagD(TAG, "delete complete! path=$path")
            } else {
                logTagD(TAG, "path=$path ,file not exists!")
            }
        }

        fun getMultiUserState(): Boolean {

            var multiUser: String = getStringSystemProp("ro.factory.hasUMS","")
            if (multiUser.isNotEmpty()) {
                return multiUser != "true"
            }
            multiUser = getStringSystemProp("ro.factory.storage_policy","")
            return if (multiUser.isNotEmpty()) {
                multiUser == "1"
            } else false
        }


        suspend fun getValidFirmwareImageFile(searchPaths: Array<String>): Array<String>? =
            withContext(Dispatchers.IO) {

                for (dirPath in searchPaths) {
                    val filePath = dirPath + OTA_PACKAGE_FILE
                    logTagD(TAG, "getValidFirmwareImageFile() : --Target image file path : $filePath")
                    if (File(filePath).exists()) {
                        logTagD(TAG, "==File(filePath).exists()==$filePath")
                        return@withContext arrayOf(filePath)
                    }
                }

                //find rk image
                for (dirPath in searchPaths) {
                    val filePath = dirPath + RK_IMAGE_FILE
                    if (File(filePath).exists()) {
                        return@withContext arrayOf(filePath)
                    }
                }
                if (UpdateService.IS_SUPPORT_USB_UPDATE) {
                    //find usb device update package
                    return@withContext findFromSdOrUsb()
                } else {
                    return@withContext null
                }
            }


        suspend fun findFromSdOrUsb(): Array<String>? = withContext(Dispatchers.IO) {
            val mStorageManager =
                app.getSystemService(Context.STORAGE_SERVICE) as StorageManager
            val volumes: List<VolumeInfo> = mStorageManager.getVolumes()
            for (volume in volumes) {
                if (!volume.isMountedReadable) continue
                val disk = volume.getDisk() ?: continue
                logTagE(TAG, " --- volume.internalPath = " + volume.internalPath)
                var path = volume.internalPath
                if (path.startsWith("/mnt/media_rw")) {
                    path = path.replace("/mnt/media_rw", "/storage")
                }
                val otaPath = "$path/$OTA_PACKAGE_FILE"
                val imgPath = "$path/$RK_IMAGE_FILE"
                val flag = if (File(otaPath).exists()) {
                    0
                } else if (File(imgPath).exists()) {
                    1
                } else {
                    continue
                }
                if (disk.isSd) {
                    if (volume.fsType == "ntfs" || volume.fsType == "exfat") {
                        logTagE(TAG, "sd fstype is " + volume.fsType)
                        continue
                    }
                } else if (disk.isUsb) {
                    if (volume.fsType == "ntfs" || volume.fsType == "exfat") {
                        logTagE(TAG, "usb fstype is " + volume.fsType)
                        continue
                    }
                }
                if (flag == 0) {
                    logTagE(TAG, "find package from $otaPath")
                    return@withContext arrayOf(otaPath)
                } else {
                    logTagE(TAG, "find package from $imgPath")
                    return@withContext arrayOf(imgPath)
                }
            }
            return@withContext null
        }
    }
}