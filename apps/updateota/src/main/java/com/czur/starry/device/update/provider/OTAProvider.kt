package com.czur.starry.device.update.provider

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStore
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logV
import com.czur.starry.device.baselib.handler.SPContentHandler
import com.czur.starry.device.baselib.handler.SPContentProvider
import com.czur.starry.device.baselib.handler.createDefCorruptionHandler
import com.czur.starry.device.baselib.utils.prop.getBooleanSystemProp
import com.czur.starry.device.baselib.utils.prop.setBooleanSystemProp
import com.czur.starry.device.otalib.OTAHandler
import com.czur.starry.device.otalib.OTAHandler.KEY_ALL_MEETING_STATUES

class OTAProvider : SPContentProvider() {
    override val Context.providerDataStore: DataStore<Preferences> by preferencesDataStore(
        name = "otaprovider",
        corruptionHandler = createDefCorruptionHandler("otaprovider")
    )
    override val targetHandler: SPContentHandler by lazy { OTAHandler }

    companion object {
        private const val PROP_KEY_ALL_MEETING_STATUES = "system.meeting.allMeetingStatus"
    }

    override fun onSubQuery(key: String, defValue: String): String? {
        if (key == KEY_ALL_MEETING_STATUES) {
            logV("读取当前是否进行会议或录制")
            return getBooleanSystemProp(PROP_KEY_ALL_MEETING_STATUES, false).toString()
        }
        return super.onSubQuery(key, defValue)
    }

    override fun onSubUpdate(key: String, value: String): Boolean {
        if (key == KEY_ALL_MEETING_STATUES) {
            logV("设置当前是否进行会议或录制")
            setBooleanSystemProp(PROP_KEY_ALL_MEETING_STATUES, value.toBoolean())
            return true
        }
        return super.onSubUpdate(key, value)
    }

    override fun doOnDataStoreError(tr: Throwable) {
        logTagE("OTAProvider", "DataStore异常", tr = tr)
        val file = context!!.filesDir
        file.deleteRecursively()
    }
}