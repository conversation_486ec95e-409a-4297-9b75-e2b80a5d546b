

@Suppress("DSL_SCOPE_VIOLATION") // TODO: Remove once KTIJ-19369 is fixed
plugins {
    alias(libs.plugins.application)
    alias(libs.plugins.kotlinAndroid)
    id("kotlin-parcelize")
    alias(libs.plugins.ksp)
    alias(libs.plugins.navigationSafeargs)
}

private val pkgName = "com.czur.starry.device.file"
private val apkName = "CZFileManager"
android.buildFeatures.buildConfig = true
android {
    namespace = pkgName
    compileSdk = libs.versions.compileSdkVersion.get().toInt()

    defaultConfig {
        applicationId = namespace
        minSdk = libs.versions.minSdkVersion.get().toInt()
        targetSdk = libs.versions.targetSdkVersion.get().toInt()

        versionCode = libs.versions.appVersionCode.get().toInt()
        versionName = libs.versions.appVersionName.get()

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        renderscriptSupportModeEnabled = true

        setFlavorDimensions(listOf("constantEnv"))
    }

    // 签名
    signingConfigs {
        create("keyStore") {
            storeFile = file("${rootDir}/signature/starry.jks")
            storePassword = rootProject.ext["storePassword"].toString()
            keyAlias = rootProject.ext["keyAlias"].toString()
            keyPassword = rootProject.ext["keyPassword"].toString()
        }
    }

    buildTypes {
        val signConfig = signingConfigs.getByName("keyStore")
        debug {
            signingConfig = signConfig
            isMinifyEnabled = false
        }

        create("unsigned") {
            signingConfig = signConfig
            isDebuggable = false
            isMinifyEnabled = false
        }

    }
    productFlavors {
        create("envProduct") {
            dimension = "constantEnv"
            buildConfigField("Integer", "CONSTANT_ENV", "0")
        }
        create("envTest") {
            dimension = "constantEnv"
            buildConfigField("Integer", "CONSTANT_ENV", "1")
            isDefault = true
        }
    }

    externalNativeBuild {
        this.cmake {
            path = file("CMakeLists.txt")
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }

    buildFeatures {
        aidl = true
        viewBinding = true
    }

    packaging {
        // netty在引入的时候需要添加这个, 否则编译会报错
        resources.excludes.add("META-INF/INDEX.LIST")
        resources.excludes.add("META-INF/io.netty.versions.properties")
        resources.excludes.add("META-INF/DEPENDENCIES")
        jniLibs {
            useLegacyPackaging = true
        }
    }

    android.applicationVariants.all {
        outputs.all {
            if (this is com.android.build.gradle
                .internal.api.ApkVariantOutputImpl
            ) {
                this.outputFileName = "${apkName}.apk"
            }
        }
    }
}

dependencies {

    // 测试依赖
    testImplementation(libs.junit)
    androidTestImplementation(libs.bundles.androidTest)

    implementation(project(":baselib"))
    implementation(project(":base:filelib"))
    implementation(project(":base:tempOSS"))
    implementation(project(":thirdlibs:photoview"))
    implementation(project(":aars:renderscriptToolkit"))
    implementation(project(":base:UILib"))
    implementation(project(":base:eShareLib"))
    implementation(project(":base:otalib"))
    // xml解析库
    implementation(libs.fastxml.jackson)
    // media3
    implementation(libs.bundles.media3)
    // 生成pdf
    implementation(libs.itextpdf)
    // markdown
    implementation(libs.commonmark)

    implementation(libs.netty)
    // 数据库相关
    implementation(libs.bundles.room)
    annotationProcessor(libs.room.compiler)
    ksp(libs.room.compiler)

    // 导航库
    implementation(libs.bundles.navigation)
}