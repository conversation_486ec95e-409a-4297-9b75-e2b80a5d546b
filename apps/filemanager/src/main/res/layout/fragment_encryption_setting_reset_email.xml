<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_main"
    tools:ignore="PxUsage">

    <TextView
        android:id="@+id/titleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/title_encryption_setting_reset_email"
        android:textColor="@color/text_content_title"
        android:textSize="44px"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/emailTv"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/label_encryption_setting_verify_email_original"
        android:textColor="@color/text_common"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/emailTv"
        app:layout_constraintRight_toRightOf="@id/emailNewLabelTv"
        app:layout_constraintTop_toTopOf="@id/emailTv" />

    <TextView
        android:id="@+id/emailTv"
        android:layout_width="wrap_content"
        android:layout_height="80px"
        android:layout_marginTop="70px"
        android:gravity="center_vertical"
        android:maxWidth="600px"
        android:paddingHorizontal="20px"
        android:textColor="@color/text_common"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/verificationCodeOriginalEt"
        app:layout_constraintLeft_toLeftOf="@id/verificationCodeOriginalEt"
        app:layout_constraintTop_toBottomOf="@id/titleTv"
        tools:text="<EMAIL>" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_verification_code"
        android:textColor="@color/text_common"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/verificationCodeOriginalEt"
        app:layout_constraintRight_toRightOf="@id/emailNewLabelTv"
        app:layout_constraintTop_toTopOf="@id/verificationCodeOriginalEt" />

    <EditText
        android:id="@+id/verificationCodeOriginalEt"
        style="@style/encryption_input_et"
        android:layout_width="420px"
        android:layout_marginTop="50px"
        android:inputType="number"
        android:maxLength="6"
        app:layout_constraintBottom_toTopOf="@id/emailNewEt"
        app:layout_constraintLeft_toLeftOf="@id/emailNewEt"
        app:layout_constraintTop_toBottomOf="@id/emailTv" />

    <TextView
        android:id="@+id/getCodeOriginalTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_get_verification_code"
        android:textColor="@color/text_common"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/verificationCodeOriginalEt"
        app:layout_constraintLeft_toRightOf="@id/verificationCodeOriginalEt"
        app:layout_constraintRight_toRightOf="@id/emailNewEt"
        app:layout_constraintTop_toTopOf="@id/verificationCodeOriginalEt" />

    <ProgressBar
        android:id="@+id/progressBarOriginal"
        android:layout_width="80px"
        android:layout_height="80px"
        android:indeterminateTint="#5879FC"
        android:indeterminateTintMode="src_atop"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/verificationCodeOriginalEt"
        app:layout_constraintLeft_toRightOf="@id/verificationCodeOriginalEt"
        app:layout_constraintRight_toRightOf="@id/emailNewEt"
        app:layout_constraintTop_toTopOf="@id/verificationCodeOriginalEt" />

    <TextView
        android:id="@+id/countdownOriginalTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_common"
        android:textSize="30px"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/verificationCodeOriginalEt"
        app:layout_constraintLeft_toRightOf="@id/verificationCodeOriginalEt"
        app:layout_constraintRight_toRightOf="@id/emailNewEt"
        app:layout_constraintTop_toTopOf="@id/verificationCodeOriginalEt" />

    <TextView
        android:id="@+id/emailNewLabelTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginRight="40px"
        android:text="@string/label_encryption_setting_verify_email_new"
        android:textColor="@color/text_common"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/emailNewEt"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/emailNewEt"
        app:layout_constraintTop_toTopOf="@id/emailNewEt" />

    <EditText
        android:id="@+id/emailNewEt"
        style="@style/encryption_input_et"
        android:layout_width="600px"
        android:layout_marginTop="70px"
        android:inputType="textEmailAddress"
        android:lines="1"
        app:layout_constraintBottom_toTopOf="@id/verificationCodeNewEt"
        app:layout_constraintLeft_toRightOf="@id/emailNewLabelTv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/verificationCodeOriginalEt" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_verification_code"
        android:textColor="@color/text_common"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/verificationCodeNewEt"
        app:layout_constraintRight_toRightOf="@id/emailNewLabelTv"
        app:layout_constraintTop_toTopOf="@id/verificationCodeNewEt" />

    <EditText
        android:id="@+id/verificationCodeNewEt"
        style="@style/encryption_input_et"
        android:layout_width="420px"
        android:layout_marginTop="50px"
        android:inputType="number"
        android:maxLength="6"
        app:layout_constraintBottom_toTopOf="@id/finishBtn"
        app:layout_constraintLeft_toLeftOf="@id/emailNewEt"
        app:layout_constraintTop_toBottomOf="@id/emailNewEt" />

    <TextView
        android:id="@+id/getCodeNewTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/str_get_verification_code"
        android:textColor="@color/text_common"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/verificationCodeNewEt"
        app:layout_constraintLeft_toRightOf="@id/verificationCodeNewEt"
        app:layout_constraintRight_toRightOf="@id/emailNewEt"
        app:layout_constraintTop_toTopOf="@id/verificationCodeNewEt" />

    <ProgressBar
        android:id="@+id/progressBarNew"
        android:layout_width="80px"
        android:layout_height="80px"
        android:indeterminateTint="#5879FC"
        android:indeterminateTintMode="src_atop"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/verificationCodeNewEt"
        app:layout_constraintLeft_toRightOf="@id/verificationCodeNewEt"
        app:layout_constraintRight_toRightOf="@id/emailNewEt"
        app:layout_constraintTop_toTopOf="@id/verificationCodeNewEt" />

    <TextView
        android:id="@+id/countdownNewTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/text_common"
        android:textSize="30px"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/verificationCodeNewEt"
        app:layout_constraintLeft_toRightOf="@id/verificationCodeNewEt"
        app:layout_constraintRight_toRightOf="@id/emailNewEt"
        app:layout_constraintTop_toTopOf="@id/verificationCodeNewEt" />

    <com.czur.uilib.btn.CZButton
        android:id="@+id/finishBtn"
        android:layout_width="300px"
        android:layout_height="80px"
        android:layout_marginTop="70px"
        android:enabled="false"
        android:text="@string/str_encryption_pwd_next_step"
        android:textSize="30px"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/verificationCodeNewEt" />

</androidx.constraintlayout.widget.ConstraintLayout>