<animated-vector
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt">
    <aapt:attr name="android:drawable">
        <vector
            android:name="vector"
            android:width="43px"
            android:height="50px"
            android:viewportWidth="43"
            android:viewportHeight="50">
            <group
                android:name="group"
                android:pivotX="22"
                android:pivotY="25">
                <path
                    android:name="path_1"
                    android:pathData="M 40.089 20.987 C 31.281 15.325 15.868 6.517 7.06 1.17 C 3.6 -1.032 0.454 -0.088 0.14 3.686 C -0.175 14.382 0.14 35.143 0.14 46.152 C 0.454 50.242 3.914 50.871 7.06 48.984 C 15.868 43.636 30.967 34.514 39.775 29.166 C 40.044 28.902 46.546 25.369 40.089 20.987 Z"
                    android:fillColor="#ffffff"
                    android:strokeWidth="1"/>
                <clip-path
                    android:name="path"
                    android:pathData="M 11 0 C 13.761 0 16 2.239 16 5 L 16 45 C 16 47.761 13.761 50 11 50 C 8.239 50 6 47.761 6 45 L 6 5 C 6 2.239 8.239 0 11 0 Z M 32 0 C 34.761 0 37 2.239 37 5 L 37 45 C 37 47.761 34.761 50 32 50 C 29.239 50 27 47.761 27 45 L 27 5 C 27 2.239 29.239 0 32 0 Z"/>
            </group>
        </vector>
    </aapt:attr>
    <target android:name="path_1">
        <aapt:attr name="android:animation">
            <objectAnimator
                android:propertyName="pathData"
                android:duration="400"
                android:valueFrom="M 7.06 1.17 C 15.868 6.517 31.281 15.325 40.089 20.987 C 40.089 20.987 40.089 20.987 40.089 20.987 C 46.546 25.369 40.044 28.902 39.775 29.166 C 30.967 34.514 15.868 43.636 7.06 48.984 C 3.914 50.871 0.454 50.242 0.14 46.152 C 0.14 35.143 -0.175 14.382 0.14 3.686 C 0.297 1.799 1.162 0.62 2.42 0.187 C 3.679 -0.245 5.33 0.069 7.06 1.17 M 32 25 L 32 25 C 32 25 32 25 32 25 L 32 25 C 32 25 32 25 32 25 C 32 25 32 25 32 25 L 32 25 C 32 25 32 25 32 25"
                android:valueTo="M 11 0 C 12.38 0 13.63 0.56 14.535 1.465 C 15.44 2.369 16 3.619 16 5 C 16 18.333 16 31.667 16 45 C 16 47.761 13.761 50 11 50 C 8.239 50 6 47.761 6 45 C 6 31.667 6 18.333 6 5 C 6 2.239 8.239 0 11 0 C 11 0 11 0 11 0 M 32 0 L 32 0 C 29.239 0 27 2.239 27 5 L 27 45 C 27 47.761 29.239 50 32 50 C 34.761 50 37 47.761 37 45 L 37 5 C 37 2.239 34.761 0 32 0"
                android:valueType="pathType"
                android:interpolator="@android:anim/accelerate_decelerate_interpolator"/>
        </aapt:attr>
    </target>
    <target android:name="group">
        <aapt:attr name="android:animation">
            <set>
                <objectAnimator
                    android:propertyName="rotation"
                    android:duration="400"
                    android:valueFrom="0"
                    android:valueTo="360"
                    android:valueType="floatType"
                    android:interpolator="@android:anim/accelerate_decelerate_interpolator"/>
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="200"
                    android:valueFrom="1"
                    android:valueTo="0.2"
                    android:valueType="floatType"
                    android:interpolator="@android:anim/accelerate_interpolator"/>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="200"
                    android:valueFrom="1"
                    android:valueTo="0.2"
                    android:valueType="floatType"
                    android:interpolator="@android:anim/accelerate_interpolator"/>
                <objectAnimator
                    android:propertyName="scaleX"
                    android:startOffset="200"
                    android:duration="200"
                    android:valueFrom="0.2"
                    android:valueTo="1"
                    android:valueType="floatType"
                    android:interpolator="@android:anim/decelerate_interpolator"/>
                <objectAnimator
                    android:propertyName="scaleY"
                    android:startOffset="200"
                    android:duration="200"
                    android:valueFrom="0.2"
                    android:valueTo="1"
                    android:valueType="floatType"
                    android:interpolator="@android:anim/decelerate_interpolator"/>
            </set>
        </aapt:attr>
    </target>
</animated-vector>
