package com.czur.starry.device.file.server.component.servlet.get

import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.file.manager.LocalEncryptionManager
import com.czur.starry.device.file.server.common.FileShareConstant
import com.czur.starry.device.file.server.common.FileShareConstant.ONE_CHUNK_SIZE
import com.czur.starry.device.file.server.component.anno.Servlet
import com.czur.starry.device.file.server.component.servlet.HttpServlet
import com.czur.starry.device.file.server.download.DownloadHelper
import com.czur.starry.device.file.server.upload.getAsBase64
import com.czur.starry.device.file.server.upload.getLong
import com.czur.starry.device.file.server.util.CZHttpRequest
import com.czur.starry.device.file.server.util.CZHttpResponse
import com.czur.starry.device.file.server.util.needCheckPwd
import java.io.File
import java.io.FileNotFoundException
import kotlin.math.ceil

/**
 * Created by 陈丰尧 on 2024/12/17
 */
private const val TAG = "DownloadFile"
private const val PARAM_KEY_PWD = "pwd"

@Servlet(path = FileShareConstant.GET_DOWNLOAD_FILE)
class DownloadFileServlet : HttpServlet() {
    override suspend fun onConnect(request: CZHttpRequest, response: CZHttpResponse) {
        val filePath = request.rawHeaders.getAsBase64(FileShareConstant.FILE_PATH)
        val chunkNumber = request.rawHeaders.getLong(FileShareConstant.CHUNK_NUMBER, -1)

        val file = File(filePath)
        if (!file.exists() || !file.isFile) {
            logTagI(TAG, "file is not exist filePath=$filePath")
            response.mkMsg(FileShareConstant.FILE_NOT_EXIST, "file is not exist")
            return
        }
        if (chunkNumber < 0) {
            logTagI(TAG, "chunkNumber error chunkNumber=$chunkNumber")
            response.mkMsg(FileShareConstant.BYTE_START_ERROR, "chunkNumber error")
            return
        }

        if (needCheckPwd(filePath)) {
            // 需要检查密码
            val pwd = request.getPathParam(PARAM_KEY_PWD) ?: ""
            if (!LocalEncryptionManager.checkPwd(pwd)) {
                logTagI(TAG, "密码错误 pwd=$pwd , filePath=$filePath")
                response.mkMsg(FileShareConstant.PWD_ERROR, "password error")
                return
            }
        }

        logTagV(TAG, "开始下载: $filePath")

        val totalChunks = ceil(file.length().toFloat() / ONE_CHUNK_SIZE).toInt()
        val fileChunkRes = DownloadHelper.getFileChunk(file, chunkNumber)
        if (fileChunkRes.isFailure) {
            logTagI(TAG, "读取文件失败")
            // 读取文件失败
            val exception = fileChunkRes.exceptionOrNull() ?: Exception("Unknow")
            if (exception is FileNotFoundException) {
                logTagI(TAG, "文件不存在")
                response.mkMsg(FileShareConstant.FILE_NOT_EXIST, "file is not exist")
            } else {
                logTagW(TAG, "未知错误", tr = exception)
                response.mkMsg(
                    FileShareConstant.UNKNOWN_ERROR,
                    exception.message ?: "unknown error"
                )
            }
            return
        }
        val chunk = fileChunkRes.getOrNull()
        if (chunk == null) {
            logTagI(TAG, "未知错误, chunk is null")
            response.mkMsg(FileShareConstant.UNKNOWN_ERROR, "unknown error")
            return
        }

        logTagI(TAG, "下载成功")
        // 请求成功
        response.header[FileShareConstant.CHUNK_TOTAL_NUMBER] = totalChunks.toString()
        response.header[FileShareConstant.CHUNK_CURRENT_SIZE] = chunk.chunkSize.toString()
        response.header[FileShareConstant.FILE_SIZE] = file.length().toString()
        response.header[FileShareConstant.FILE_MD5] = chunk.blockMd5
        response.content = chunk.byteBuffer

    }
}