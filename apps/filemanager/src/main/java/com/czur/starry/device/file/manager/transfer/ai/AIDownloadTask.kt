package com.czur.starry.device.file.manager.transfer.ai

import android.content.Context
import android.os.SystemClock
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagI
import com.czur.czurutils.log.logTagV
import com.czur.czurutils.log.logTagW
import com.czur.starry.device.baselib.network.download.startDownload
import com.czur.starry.device.baselib.utils.getString
import com.czur.starry.device.file.R
import com.czur.starry.device.file.manager.DownloadFileAccess
import com.czur.starry.device.file.manager.LocalFileAccess
import com.czur.starry.device.file.server.entity.AITransFileEntity
import com.itextpdf.io.font.PdfEncodings
import com.itextpdf.kernel.font.PdfFontFactory
import com.itextpdf.kernel.pdf.PdfDocument
import com.itextpdf.kernel.pdf.PdfWriter
import com.itextpdf.layout.Document
import com.itextpdf.layout.element.Paragraph
import com.itextpdf.layout.element.Text
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.isActive
import kotlinx.coroutines.withContext
import java.io.File

/**
 * Created by 陈丰尧 on 2025/4/18
 */
class AIDownloadTask(private val entityInList: AITransFileEntity, val entity: AITransFileEntity) {
    companion object {
        const val MAX_PROGRESS_VALUE = 3    // 任务的总进度
        private const val TAG = "AIDownloadTask"
    }

    private var progress = 0

    /**
     * 开始下载
     * 分为三个阶段:
     * 1. 下载asr
     * 2. 下载summary
     * 3. 生成PDF文档
     * 每个阶段完成后,都会回调进度 + 1
     */
    suspend fun start(progressCb: (progress: Int) -> Unit) {
        withContext(Dispatchers.IO) {
            progress = 0
            // 1. 下载asr
            val asrResult = downloadTarget(entity.asrUrl)
            if (asrResult.isFailure) {
                if (entity.asrUrl.isNullOrEmpty()|| entity.asrUrl == "null") {
                    progress = 0
                } else {
                    progress = -1
                    callbackProgress(progressCb)
                    return@withContext
                }
                logTagW(
                    TAG,
                    " 下载asr失败: ${entity.name} ${asrResult.exceptionOrNull().toString()}"
                )
            } else {
                progress += 1
            }

            val asrFile = asrResult.getOrNull()

            logTagV(TAG, "asr下载完成")

            callbackProgress(progressCb)

            if (!isActive) {
                logTagI(TAG, " 取消下载: ${entity.name}")
                asrFile?.delete()
                return@withContext
            }

            // 2. 下载summary
            val summaryResult = downloadTarget(entity.summaryUrl)
            val summaryFile = summaryResult.getOrNull()
            if (summaryResult.isFailure) {
                if (entity.summaryUrl.isNullOrEmpty()|| entity.summaryUrl == "null") {
                    progress = 0
                } else {
                    progress = -1
                    asrFile?.delete()
                    callbackProgress(progressCb)
                    return@withContext
                }
                logTagW(
                    TAG,
                    " 下载summary失败: ${entity.name} ${asrResult.exceptionOrNull().toString()}"
                )
            } else {
                progress += 1
            }
            logTagV(TAG, "summary下载完成")

            callbackProgress(progressCb)

            if (!isActive) {
                logTagI(TAG, " 取消下载: ${entity.name}")
                asrFile?.delete()
                summaryFile?.delete()
                return@withContext
            }

            // 3. 生成PDF文档
            val absPath = DownloadFileAccess.getRootEntity().absPath
            logTagD(TAG, "下载完毕 ${entity.name}")
            val pdfFile = File(
                "${absPath}${File.separator}${entity.name}.pdf"
            )
            val filePath = determineFileName(pdfFile.path)
            convertTxtFilesToPdf(
                listOf(summaryFile, asrFile),
                filePath
            )
            asrFile?.delete()
            summaryFile?.delete()

            entityInList.localAbsPath = filePath
            entityInList.hasDownloaded = true

            logTagD(TAG, "PDF生成完成: $filePath")
            progress += 1
            callbackProgress(progressCb)
            logTagD(TAG, "PDF生成完成end: $filePath")
        }
    }

    /**
     * 回调进度,会回调到主线程中
     */
    private suspend fun callbackProgress(progressCb: (progress: Int) -> Unit) {
        withContext(Dispatchers.Main) {
            progressCb.invoke(progress)
            progress = 0
        }
    }

    /**
     * 下载目标文件 用于下载asr和summary文件
     */
    private suspend fun downloadTarget(url: String?): Result<File?> {
        if (url.isNullOrEmpty() || url == "null") return Result.failure(Exception("没有下载地址"))
        val targetFile = getTmpFile()
        val downloadRes = startDownload(url, targetFile)
        if (!downloadRes) {
            logTagW(TAG, " 下载asr失败: ${entity.name} - url:${url}")
            return Result.failure(Exception("下载失败"))
        }
        return Result.success(targetFile)
    }


    /**
     * 将两个txt文件转换为PDF
     */
    private fun convertTxtFilesToPdf(
        txtFiles: List<File?>,
        outputPdfFilePath: String
    ) {
        val inputTextFiles = txtFiles.filterNotNull().toMutableList()
        if (inputTextFiles.size == 2) {
            val asrFile = inputTextFiles[0]
            val summaryFile = inputTextFiles[1]
            val asrFileLength = asrFile.length()
            if (summaryFile.length() == asrFileLength && asrFileLength < 50) {
                logTagI(TAG, "会议纪要太短, 输出一句即可")
                inputTextFiles.removeAt(0)
            }
        }


        logTagD(TAG, "convertTxtFilesToPdf 开始生成PDF: $outputPdfFilePath")
        try {
            val outputFile = File(outputPdfFilePath)
            if (outputFile.parentFile?.exists() == false) {
                outputFile.parentFile?.mkdirs()
            }

            val writer = PdfWriter(outputFile)
            val pdf = PdfDocument(writer)
            val document = Document(pdf)

            val context: Context = globalAppCtx
            val fontPath = "fonts/NotoSansSC-Regular.ttf"
            val fontBytes = readFontFromAssets(context, fontPath)
            val font = PdfFontFactory.createFont(
                fontBytes,
                PdfEncodings.IDENTITY_H,
                PdfFontFactory.EmbeddingStrategy.PREFER_EMBEDDED
            )

            var hasContent = false

            fun parseSpanStyle(text: String): Pair<String, String?> {
                val pattern = "<span style=\"color:([^\"]+)\">([^<]+)</span>".toRegex()
                val matchResult = pattern.find(text)
                return if (matchResult != null) {
                    Pair(matchResult.groupValues[2], matchResult.groupValues[1])
                } else {
                    Pair(text, null)
                }
            }

            fun hexColorToDeviceColor(hexColor: String): com.itextpdf.kernel.colors.Color {
                try {
                    val colorStr = hexColor.removePrefix("#")
                    val r = colorStr.substring(0, 2).toInt(16) / 255f
                    val g = colorStr.substring(2, 4).toInt(16) / 255f
                    val b = colorStr.substring(4, 6).toInt(16) / 255f
                    return com.itextpdf.kernel.colors.DeviceRgb(r, g, b)
                } catch (e: Exception) {
                    logTagD(TAG, "颜色转换失败: $hexColor, ${e.message}")
                    return com.itextpdf.kernel.colors.DeviceRgb(0f, 0f, 0f)
                }
            }

            fun createStyledText(
                text: String,
                isBold: Boolean = false,
                color: String? = null
            ): Text {
                val styledText = Text(text).setFont(font)
                if (isBold) {
                    styledText.setBold()
                }
                if (color != null) {
                    styledText.setFontColor(hexColorToDeviceColor(color))
                }
                return styledText
            }

            fun processMarkdownText(text: String, index: Int): Paragraph {

                val paragraph = Paragraph().setFont(font)
                    .setFontSize(12f)
                    .setMarginBottom(8f)

                // 处理加粗文本
                val boldPattern = "\\*\\*([^*]+?)\\*\\*(.*)".toRegex()
                val boldMatch = boldPattern.find(text)

                if (boldMatch != null) {
                    // 加粗部分
                    val boldText = boldMatch.groupValues[1]
                    // 剩余部分
                    val remainingText = boldMatch.groupValues[2]

                    // 添加加粗文本
                    paragraph.add(createStyledText(boldText, true))
                    // 添加剩余文本
                    if (remainingText.isNotEmpty()) {
                        paragraph.add(createStyledText(remainingText))
                    }
                } else {
                    // 处理带颜色的文本
                    val (spanText, colorValue) = parseSpanStyle(text)
                    paragraph.add(createStyledText(spanText, false, colorValue))
                }

                return paragraph
            }

            for ((index, txtFile) in inputTextFiles.withIndex()) {
                if (txtFile.exists() == true && txtFile.length() > 0) {
                    try {
                        val content = txtFile.readText(Charsets.UTF_8)

                        content.split("\n").forEach { line ->
                            if (line.isNotEmpty()) {
                                when {
                                    line.startsWith("<span style") && line.contains(">智能纪要</span>") && index == 1
                                            || line.startsWith("<span style") && line.contains(">会议时间:") && index == 1
                                            || line.startsWith("**会议主题:**") && index == 1
                                            || line.contains("[点击查看]") -> {
                                        // 跳过这一行,暂时不添加到文件中

                                    }

                                    line.startsWith("## ") -> {
                                        document.add(
                                            Paragraph(line.substring(3))
                                                .setFont(font)
                                                .setBold()
                                                .setFontSize(18f)
                                                .setMarginTop(15f)
                                                .setMarginBottom(10f)
                                        )
                                    }

                                    line.startsWith("### ") -> {
                                        document.add(
                                            Paragraph(line.substring(4))
                                                .setFont(font)
                                                .setBold()
                                                .setFontSize(16f)
                                                .setMarginTop(12f)
                                                .setMarginBottom(8f)
                                        )
                                    }

                                    line.startsWith("• ") -> {
                                        document.add(
                                            processMarkdownText("  • ${line.substring(2)}", index)
                                        )
                                    }


                                    else -> {
                                        document.add(processMarkdownText(line, index))
                                    }
                                }
                                hasContent = true
                            }
                        }

                        document.add(Paragraph("\n"))
                    } catch (e: Exception) {
                        logTagD(TAG, "处理文件 ${txtFile.name} 时出错: ${e.message}")
                        e.printStackTrace()
                    }
                }
            }

            if (!hasContent) {
                document.add(
                    Paragraph(getString(R.string.ai_has_no_record))
                        .setFont(font)
                        .setFontSize(12f)
                )
            }

            document.close()
            logTagD(TAG, "PDF生成完成: $outputPdfFilePath")
        } catch (e: Exception) {
            logTagD(TAG, "PDF生成异常: ${e.message}", tr = e)
        }
    }

    /**
     * 判断文件名是否重复,如果重复,则在文件名后加上(01),(02)等
     */
    fun determineFileName(path: String): String {
        var file = File(path)
        var newPath = path;
        var partIndex = 1
        val fileName = file.name.substringBeforeLast(".")
        val fileExtension = file.extension
        val name = fileName.substringBeforeLast("(")

        while (file.exists()) {
            newPath =
                "${file.parent}/$name(${partIndex.toString().padStart(2, '0')}).$fileExtension"
            file = File(newPath)
            partIndex++
        }

        return newPath
    }

    /**
     * 获取临时文件,用于临时存放下载的asr和summary文件
     */
    private fun getTmpFile(): File {
        val fileName = SystemClock.elapsedRealtime()
        return File(globalAppCtx.cacheDir, "${fileName}.tmp")
    }

    /**
     * 从 assets 读取字体文件并转为字节数组
     */
    private fun readFontFromAssets(context: Context, fontPath: String): ByteArray {
        // 从 assets 读取字体文件并转为字节数组
        return context.assets.open(fontPath).use { inputStream ->
            inputStream.readBytes()
        }
    }
}