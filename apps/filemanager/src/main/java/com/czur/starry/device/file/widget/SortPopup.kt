package com.czur.starry.device.file.widget

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.czur.starry.device.file.R
import com.czur.starry.device.file.manager.FileAccess
import com.wanglu.lib.BasePopup
import com.wanglu.lib.WPopParams
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Created by 陈丰尧 on 2021/9/8
 */
private val mainScope = MainScope()
private const val SEL_CHANGE_SHOW_TIME = 120L

/**
 * 构建一个排序
 */
fun createSortPopup(
    context: Context,
    onPopupItemClick: (sortType: FileAccess.SortType) -> Unit,
    defSortType: FileAccess.SortType = FileAccess.SortType.TYPE_TIME_DESC,
): BasePopup {
    val sortPopup = BasePopup(
        WPopParams(
            R.layout.popup_sort,
            context,
            false,
            cancelable = true,
            width = ViewGroup.LayoutParams.WRAP_CONTENT,
            height = ViewGroup.LayoutParams.WRAP_CONTENT,
            isShowShadow = true
        )
    )
    sortPopup.defaultMargin = 0
    val popItems = mapOf(
        FileAccess.SortType.TYPE_TIME_ASC to sortPopup.getContentView()
            .findViewById(R.id.sortItemTimeAsc) as LinearLayout,
        FileAccess.SortType.TYPE_TIME_DESC to sortPopup.getContentView()
            .findViewById(R.id.sortItemTimeDesc) as LinearLayout,
        FileAccess.SortType.TYPE_NAME_ASC to sortPopup.getContentView()
            .findViewById(R.id.sortItemNameAsc) as LinearLayout,
        FileAccess.SortType.TYPE_NAME_DESC to sortPopup.getContentView()
            .findViewById(R.id.sortItemNameDesc) as LinearLayout,
    )

    fun changeSelect(targetType: FileAccess.SortType) {
        var index = 0
        popItems.forEach { (type, item) ->
            val isNotSelected = type != targetType
            item.colorTv(isNotSelected)
            item.hideIv(isNotSelected)
            item.let {
                if (isNotSelected) {
                    it.background = context.getDrawable(R.drawable.bg_pop_sort)
                } else {
                    if (index == 0) {
                        it.background = context.getDrawable(R.drawable.bg_pop_sort_selected_top)
                    } else if (index == popItems.size - 1) {
                        it.background = context.getDrawable(R.drawable.bg_pop_sort_selected_bottom)
                    } else {
                        it.background = context.getDrawable(R.drawable.bg_pop_sort_selected)
                    }
                }


            }
            index++
        }
    }

    popItems.forEach { (type, item) ->
        changeSelect(defSortType)
        item.setOnClickListener {
            // 选中的显示
            changeSelect(type)
            // 回调
            onPopupItemClick(type)
            mainScope.launch {
                delay(SEL_CHANGE_SHOW_TIME)
                sortPopup.dismiss()
            }
        }
    }


    return sortPopup
}

private fun LinearLayout.hideIv(isNotSelected: Boolean = true) {
    val imageView = getChildAt(1)
    if (imageView is ImageView) {
        imageView.visibility = if (isNotSelected) View.INVISIBLE else View.VISIBLE
    }

}

private fun LinearLayout.colorTv(isNotSelected: Boolean = true) {
    val textView = getChildAt(0)
    if (textView is TextView) {
        if (isNotSelected) {
            textView.setTextColor(context.getColor(R.color.text_common))
        } else {
            textView.setTextColor(context.getColor(R.color.white))
        }
    }
}

