package com.czur.starry.device.file.view.czurshare

import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.widget.doOnTextChanged
import androidx.fragment.app.viewModels
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.czur.czurutils.log.logTagD
import com.czur.czurutils.log.logTagV
import com.czur.starry.device.baselib.base.v2.fragment.floating.CZVBFloatingFragment
import com.czur.starry.device.baselib.common.Constants
import com.czur.starry.device.baselib.common.VersionIndustry
import com.czur.starry.device.baselib.utils.DifferentLiveData
import com.czur.starry.device.baselib.utils.addCharsFilter
import com.czur.starry.device.baselib.utils.doOnLoseFocus
import com.czur.starry.device.baselib.utils.gone
import com.czur.starry.device.baselib.utils.invisible
import com.czur.starry.device.baselib.utils.keyboard.keyboardHide
import com.czur.starry.device.baselib.utils.keyboard.keyboardShow
import com.czur.starry.device.baselib.utils.launch
import com.czur.starry.device.baselib.utils.lifecycle.AutoRemoveLifecycleObserver
import com.czur.starry.device.baselib.utils.setOnActionDoneListener
import com.czur.starry.device.baselib.utils.setOnDebounceClickListener
import com.czur.starry.device.baselib.utils.show
import com.czur.starry.device.baselib.view.floating.common.DoubleBtnCommonFloat
import com.czur.starry.device.baselib.widget.showTextLength
import com.czur.starry.device.file.R
import com.czur.starry.device.file.databinding.FloatCzurShareBinding
import com.czur.starry.device.file.filelib.FileHandlerLive
import com.czur.starry.device.file.filelib.FileHandlerLive.fileMeetingEnable
import com.czur.starry.device.file.filelib.FileHandlerLive.fileShareCodeEnable
import com.czur.starry.device.file.filelib.FileHandlerLive.fileShareCodeStatus
import com.czur.starry.device.file.filelib.FileHandlerLive.fileShareTimeStatus
import com.czur.starry.device.file.manager.FileAccess
import com.czur.starry.device.file.server.ServerService
import com.czur.starry.device.file.view.czurshare.vm.FileShareViwModel
import com.czur.starry.device.file.view.dialog.FileShareQRCodeFloat
import com.czur.starry.device.file.widget.createShareSortPopup
import com.wanglu.lib.BasePopup
import com.wanglu.lib.WPopupDirection

/**
 *  author : WangHao
 *  time   :2023/10/18
 */

private const val TAG = "FileShareFloat"

class FileShareFloat : CZVBFloatingFragment<FloatCzurShareBinding>() {

    private val shareVM: FileShareViwModel by viewModels({ requireActivity() })
    private val inNameEditModeLive = DifferentLiveData(false)
    private val inCodeEditModeLive = DifferentLiveData(false)
    private var currentTimeStatus = fileShareTimeStatus

    private var sortPopup: BasePopup? = null

    private var qrCodeFloat: FileShareQRCodeFloat? = null

    override fun FloatingFragmentParams.initFloatingParams() {
        this.viewLifecycleObserver = object : AutoRemoveLifecycleObserver {
            override fun onStop(owner: LifecycleOwner) {
                super.onStop(owner)
                qrCodeFloat?.dismiss()
            }
        }
        this.floatingRepeatMode = FloatingRepeatMode.Single(TAG)
    }


    override fun FloatCzurShareBinding.initBindingViews() {
        launch {
            shareVM.loadDeviceName()
        }

        if (Constants.versionIndustry == VersionIndustry.DEVICE_INDUSTRY_ARMY_BUILD) {
            logTagV(TAG, "军工版本,修改提示文言")
            shareHintTv.setText(R.string.str_file_share_hint2_army)
            deviceNamePsTv.setText(R.string.str_device_name_ps_army)
        }

        // 关闭
        closeFloatIv.setOnClickListener {
            dismiss()
        }

        //删除time pop
        timeCodeGroup.setOnClickListener {
            val currentSortTime = getPopItemsList().keys.toList()[currentTimeStatus]
            sortPopup = createShareSortPopup(requireContext(), ::onSortItemClick, currentSortTime)
            exitEditMode()
            // 显示排序Popup
            sortPopup?.showAtDirectionByViewAlignRight(timeSortIv, WPopupDirection.BOTTOM, 50)
            sortPopup?.dismissListener = ::onDismissSortPopup
            timeSortIv.setImageResource(R.drawable.file_time_sort_on)
        }

        //监听是否文件传输中
        enableShareSwitch.setOnTouchListener { v, event ->
            exitEditMode()
            if (event.action == MotionEvent.ACTION_DOWN && enableShareSwitch.value() && ServerService.isShareWorking.get()) {
                showDoubleDialog()
                true
            } else {
                false
            }
        }
        //允许传输文件
        enableShareSwitch.setOnSwitchChange { isOn, fromClick ->
            if (fromClick) {
                exitEditMode()
                launch {
                    FileHandlerLive.fileShareEnable = isOn
                }
                FileHandlerLive.fileShareEnable = isOn
                if (isOn) {
                    if (enableCodeSwitch.value()) {
                        showSubCodeView()
                    } else {
                        hideSubCodeView()
                    }
                } else {
                    hideSubCodeView()
                }
            }
        }

        //校验码
        enableCodeSwitch.setOnSwitchChange { isOn, fromClick ->
            if (fromClick) {
                exitEditMode()
                fileShareCodeEnable = isOn
                if (isOn) {
                    changeCodeEditMode(false)
                    showSubCodeView()
                } else {
                    hideSubCodeView()
                }
            }
        }

        enableMeetingSwitch.setOnSwitchChange { isOn, fromClick ->
            launch {
                if (fromClick) {
                    fileMeetingEnable = isOn
                }
            }
        }

        /**
         * 名称编辑
         */
        deviceNameEditIv.setOnClickListener {
            changeNameEditMode(true)
        }
        // 确定按钮是否可点击
        deviceNameEt.doOnTextChanged { text, _, _, _ ->
            // 没有输入内容时,不可点击
            changeEnableAndAlpha(editNameConfirmIv, !text.isNullOrBlank())
        }
        deviceNameEt.doOnLoseFocus {
            deviceNameEt.keyboardHide()
        }
        deviceNameEt.showTextLength = SHARE_DEVICE_NAME_MAX_LENGTH // 最多输入19个字符(Miracast限制)
        deviceNameEt.addCharsFilter(deviceNameBlockChars)
        deviceNameEt.setOnActionDoneListener {
            if (editNameConfirmIv.isEnabled) {
                editNameConfirmIv.performClick()
            } else {
                deviceNameEt.keyboardHide()
            }
            true
        }
        editNameConfirmIv.setOnClickListener {
            launch {
                val newName = deviceNameEt.text.toString()
                shareVM.updateDeviceName(newName)
                changeNameEditMode(false)

            }

        }
        // 取消编辑
        editNameCancelIv.setOnClickListener {
            changeNameEditMode(false)
        }


        /**
         * 验证码编辑
         */
        deviceEncodeEditIv.setOnClickListener {
            changeCodeEditMode(true)
        }
        /**
         * 验证码刷新
         */
        deviceEncodeRefreshIv.setOnClickListener {
            launch {
                shareVM.getDeviceCode()
            }
        }

        // 确定按钮是否可点击
        deviceCodeEt.doOnTextChanged { text, _, _, _ ->
            // 没有输入内容时,不可点击
            changeEnableAndAlpha(
                editCodeConfirmIv,
                !text.isNullOrBlank() && text.length == SHARE_DEVICE_CODE_MAX_LENGTH
            )
        }
        deviceCodeEt.doOnLoseFocus {
            deviceCodeEt.keyboardHide()
        }
        deviceCodeEt.showTextLength = SHARE_DEVICE_CODE_MAX_LENGTH // 最多输入19个字符(Miracast限制)
        deviceCodeEt.setOnActionDoneListener {
            if (editCodeConfirmIv.isEnabled) {
                editCodeConfirmIv.performClick()
            } else {
                deviceCodeEt.keyboardHide()
            }
            true
        }
        editCodeConfirmIv.setOnClickListener {
            val newCode = deviceCodeEt.text.toString()
            fileShareCodeStatus = newCode
            changeCodeEditMode(false)
        }
        // 取消编辑
        editCodeCancelIv.setOnClickListener {
            changeCodeEditMode(false)
        }

        shareAppEncodeIv.setOnDebounceClickListener {
            logTagV(TAG, "显示二维码悬浮窗")
            qrCodeFloat = FileShareQRCodeFloat().apply {
                setOnDismissListener {
                    qrCodeFloat = null
                }
                show()
            }
        }

    }


    override fun initData(savedInstanceState: Bundle?) {
        super.initData(savedInstanceState)

        viewLifecycleOwner.lifecycle.addObserver(object : DefaultLifecycleObserver {
            override fun onStop(owner: LifecycleOwner) {
                super.onStop(owner)
                // 页面不可见时, 取消掉编辑模式
                inNameEditModeLive.value = false
                inCodeEditModeLive.value = false
            }
        })

        // DeviceName
        shareVM.deviceNameLive.observe(this) {
            binding.deviceNameTv.text = it
        }
        // CodeName
        shareVM.deviceCodeLive.observe(this) {
            binding.deviceCodeTv.text = it
        }

        val etParent = binding.deviceNameEt.parent as ViewGroup
        val etLayoutParam = binding.deviceNameEt.layoutParams
        // 编辑模式
        inNameEditModeLive.observe(viewLifecycleOwner) { inEditMode ->
            if (inEditMode) {
                etParent.addView(binding.deviceNameEt, etLayoutParam)
                binding.deviceNameTv.invisible()
                binding.editGroup.show()
                binding.deviceNameEt.setText(shareVM.deviceName)
                binding.deviceNameEt.requestFocus()
                binding.deviceNameEt.keyboardShow()
                binding.deviceNameEt.setSelection(binding.deviceNameEt.text.length)
            } else {
                binding.deviceNameTv.show()
                binding.editGroup.gone()
                // 这里隐藏的的时候, 如果使用触控板的键盘, 还是会看见选词提示窗
                // 所以之间将这个EditText从父布局中移除
                etParent.removeView(binding.deviceNameEt)
            }
        }

        val etCodeParent = binding.deviceCodeEt.parent as ViewGroup
        val etCodeLayoutParam = binding.deviceCodeEt.layoutParams
        inCodeEditModeLive.observe(viewLifecycleOwner) { inEditMode ->
            if (inEditMode) {
                etCodeParent.addView(binding.deviceCodeEt, etCodeLayoutParam)
                binding.deviceCodeTv.invisible()
                binding.deviceEncodeRefreshIv.invisible()
                binding.editCodeGroup.show()
                binding.deviceCodeEt.setText(shareVM.deviceCode)
                binding.deviceCodeEt.requestFocus()
                binding.deviceCodeEt.keyboardShow()
                binding.deviceCodeEt.setSelection(binding.deviceCodeEt.text.length)
            } else {
                binding.deviceCodeTv.show()
                binding.deviceEncodeRefreshIv.show()
                binding.editCodeGroup.gone()
                // 这里隐藏的的时候, 如果使用触控板的键盘, 还是会看见选词提示窗
                // 所以之间将这个EditText从父布局中移除
                etCodeParent.removeView(binding.deviceCodeEt)
            }
        }

        FileHandlerLive.fileShareEnableLive.observe(viewLifecycleOwner) {
            binding.enableShareSwitch.setSwitchOn(it, true)
            logTagD(TAG, "===switchFileLive=$it=")
            if (it) {
                showCodeView()
            } else {
                hideCodeView()
                hideSubCodeView()
            }
        }

        FileHandlerLive.fileShareCodeEnableLive.observe(viewLifecycleOwner) {
            binding.enableCodeSwitch.setSwitchOn(it, true)
            logTagD(TAG, "===fileShareCodeEnableLive=$it=")
            if (it && FileHandlerLive.fileShareEnable) {
                showSubCodeView()
            } else {
                hideSubCodeView()
            }
        }


        FileHandlerLive.fileShareMeetingEnableLive.observe(viewLifecycleOwner) {
            binding.enableMeetingSwitch.setSwitchOn(it)
        }

        binding.timeSortTv.text = getPopItemsList().values.toList()[currentTimeStatus]

        // 二维码
        shareVM.qrCodeImgLive.observe(this) {
            if (it != null) {
                binding.shareAppEncodeIv.setImageBitmap(it)
            }
        }
    }

    /**
     * 当sortTypeItem被点击
     */
    private fun onSortItemClick(sortTime: FileAccess.SortTime) {
        if (fileShareTimeStatus > getPopItemsList().keys.indexOf(sortTime)) {
            showDeleteTipDialog(sortTime)
        } else {
            binding.timeSortTv.text = getPopItemsList()[sortTime]
            fileShareTimeStatus = getPopItemsList().keys.indexOf(sortTime)
            currentTimeStatus = getPopItemsList().keys.indexOf(sortTime)
        }

    }


    private fun onDismissSortPopup() {
        binding.timeSortIv.setImageResource(R.drawable.file_time_sort_off)
        sortPopup?.dismiss()
    }

    private fun changeNameEditMode(editMode: Boolean) {
        if (editMode) {
            changeCodeEditMode(false)
        }
        inNameEditModeLive.value = editMode
    }

    private fun changeCodeEditMode(editMode: Boolean) {
        if (editMode) {
            changeNameEditMode(false)
        }
        inCodeEditModeLive.value = editMode
    }

    // 自动传输校验码全部退出编辑模式
    private fun exitEditMode() {
        inNameEditModeLive.value = false
        inCodeEditModeLive.value = false
    }

    //设置deviceName 居中或置顶
    private fun setLayoutParamsMiddle(middle: Boolean) {
        val nameLayout = binding.deviceNameLabelTv.layoutParams as ConstraintLayout.LayoutParams
        if (middle) {

            nameLayout.topMargin = 400
        } else {
            nameLayout.topMargin = 240
        }
        binding.deviceNameLabelTv.layoutParams = nameLayout
    }

    private fun showDoubleDialog() {
        DoubleBtnCommonFloat(content = getString(R.string.close_file_share_tip2)) { doublecommonFloat, position ->
            if (position == 0) {
                binding.enableShareSwitch.setSwitchOn(true)
                doublecommonFloat.dismiss()
                FileHandlerLive.fileShareEnable = true
            } else {
                FileHandlerLive.fileShareEnable = false
                hideSubCodeView()
                doublecommonFloat.dismiss()
            }
        }.show()
    }

    private fun showDeleteTipDialog(sortTime: FileAccess.SortTime) {
        DoubleBtnCommonFloat(content = getString(R.string.str_auto_delete_tip)) { doubleFloat, position ->
            if (position == 0) {
                doubleFloat.dismiss()
            } else {
                binding.timeSortTv.text = getPopItemsList()[sortTime]
                fileShareTimeStatus = getPopItemsList().keys.indexOf(sortTime)
                currentTimeStatus = getPopItemsList().keys.indexOf(sortTime)
                doubleFloat.dismiss()
            }
        }.show()
    }

    private fun showCodeView() {
        binding.enableCodeSwitch.show()
        binding.enableCodeTv.show()
        binding.enableMeetingSwitch.show()
        binding.localMeetingTitleTv.show()
    }

    private fun hideCodeView() {
        binding.enableCodeSwitch.gone()
        binding.enableCodeTv.gone()
        binding.enableMeetingSwitch.gone()
        binding.localMeetingTitleTv.gone()
    }

    private fun showSubCodeView() {
        binding.enCodeGroup.show()
        setLayoutParamsMiddle(false)
    }

    private fun hideSubCodeView() {
        binding.enCodeGroup.gone()
        setLayoutParamsMiddle(true)
    }

    private fun changeEnableAndAlpha(view: View, enable: Boolean) {
        view.isEnabled = enable
        view.alpha = if (enable) 1f else 0.5f
    }
}