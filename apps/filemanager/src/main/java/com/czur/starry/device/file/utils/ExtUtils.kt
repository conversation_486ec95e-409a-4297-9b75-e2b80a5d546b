package com.czur.starry.device.file.utils


import com.czur.starry.device.file.base.FileApp

/**
 * Created by 陈丰尧 on 12/31/20
 * 一些扩展工具类
 */

/**
 * 根据ID获取对应的字符串
 */
fun Int.getStrRes(): String = FileApp.getApp().resources.getString(this)




/**
 * 转化成ArrayList
 */
fun <T> Iterable<T>.toArrayList(): ArrayList<T> {
    if (this is ArrayList){
        return this
    }
    if (this is Collection){
        return ArrayList(this)
    }

    val result = ArrayList<T>()
    this.forEach{
        result.add(it)
    }
    return result
}