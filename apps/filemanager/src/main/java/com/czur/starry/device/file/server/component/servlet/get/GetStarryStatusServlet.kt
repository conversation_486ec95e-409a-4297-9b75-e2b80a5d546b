package com.czur.starry.device.file.server.component.servlet.get

import com.czur.starry.device.file.filelib.FileHandlerLive
import com.czur.starry.device.file.server.ServerService
import com.czur.starry.device.file.server.common.FileShareConstant
import com.czur.starry.device.file.server.component.anno.Servlet
import com.czur.starry.device.file.server.component.servlet.HttpServlet
import com.czur.starry.device.file.server.entity.StarryStatusEntity
import com.czur.starry.device.file.server.upload.UploadHelper
import com.czur.starry.device.file.server.util.CZHttpRequest
import com.czur.starry.device.file.server.util.CZHttpResponse

/**
 * Created by 陈丰尧 on 2025/1/7
 */
@Servlet(path = FileShareConstant.GET_STARRY_STATUS)
class GetStarryStatusServlet : HttpServlet() {

    override suspend fun onConnect(request: CZHttpRequest, response: CZHttpResponse) {

        val serverEnable = ServerService.enableShareFile    // 服务器是否关闭
        val enableTransMeeting = serverEnable && FileHandlerLive.fileMeetingEnable // 是否允许传输会议文件
        val cpuHigh = ServerService.isCpuHigh.get()         // CPU负载是否过高
        val diskSpaceNotEnough = UploadHelper.isNotEnough() // 磁盘空间是否不足
        response.content = StarryStatusEntity(
            serverEnable,
            enableTransMeeting,
            cpuHigh,
            diskSpaceNotEnough
        )
    }
}