package com.czur.starry.device.file.manager

import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.file.bean.MainTabKey
import com.czur.starry.device.file.filelib.FileHandlerLive
import com.czur.starry.device.file.service.FileWatcherService.Companion.aiTransState
import com.czur.starry.device.file.service.FileWatcherService.Companion.czurShareState
import com.czur.starry.device.file.service.FileWatcherService.Companion.downloadState
import com.czur.starry.device.file.service.FileWatcherService.Companion.localState
import com.czur.starry.device.file.service.FileWatcherService.Companion.meetingState
import com.czur.starry.device.file.service.FileWatcherService.Companion.remindDataStore
import com.czur.starry.device.file.service.FileWatcherService.Companion.screenShortState
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext

/**
 * created by wangh on 23.0519
 */


object UnReadFileManager {
    private const val TAG = "UnReadFileManager"
    private const val KEY_UNREAD_FILE = "unRead_file_key"

    private val gson by lazy { Gson() }

    data class UnReadFileEntity(
        var name: String = "", // 文件名
        var pathType: PathType, //路径分类
        var createTime: Long //创建时间
    )

    enum class PathType {
        TYPE_LOCAL_FILE,
        TYPE_SCREEN_SHORT_FILE,
        TYPE_DOWNLOAD_FILE,
        TYPE_SHARE_FILE,
        TYPE_MEETING_FILE,
        TYPE_AI_TRANS_FILE
    }


    /**
     * 添加新增未读文件
     */
    suspend fun addUnreadFile(pathType: PathType, fileEntity: UnReadFileEntity) {
        withContext(Dispatchers.IO) {
            val list = getAllFileName() as MutableList
            //同类型只保留最新一个
            var result = list.filter { it.pathType != pathType }.toMutableList()
            result.add(fileEntity)
            val value = gson.toJson(result)
            setDSValue(value)
            updateLiveState(pathType, true)
        }
    }


    /**
     * 获取未读文件
     */
    suspend fun getUnreadFile(
        pathType: PathType
    ) = withContext(Dispatchers.IO) {
        logTagD(TAG, "=====pathType=$pathType")
        var unList = getAllFileName() as MutableList
        when (pathType) {
            PathType.TYPE_LOCAL_FILE -> {
                FileHandlerLive.unReadFileLocalStatus = getValueFromList(unList, pathType)
                localState = FileHandlerLive.unReadFileLocalStatus
            }

            PathType.TYPE_DOWNLOAD_FILE -> {
                FileHandlerLive.unReadFileDownloadStatus = getValueFromList(unList, pathType)
                downloadState = FileHandlerLive.unReadFileDownloadStatus
            }

            PathType.TYPE_SHARE_FILE -> {
                FileHandlerLive.unReadFileShareStatus = getValueFromList(unList, pathType)
                czurShareState = FileHandlerLive.unReadFileShareStatus
            }

            PathType.TYPE_SCREEN_SHORT_FILE -> {
                FileHandlerLive.unReadFileScreenStatus = getValueFromList(unList, pathType)
                screenShortState = FileHandlerLive.unReadFileScreenStatus
            }

            PathType.TYPE_MEETING_FILE -> {
                FileHandlerLive.unReadFileMeetingStatus = getValueFromList(unList, pathType)
                meetingState = FileHandlerLive.unReadFileMeetingStatus
            }

            PathType.TYPE_AI_TRANS_FILE -> {
                FileHandlerLive.unReadFileAITransStatus = getValueFromList(unList, pathType)
                aiTransState = FileHandlerLive.unReadFileAITransStatus
            }
        }

    }

    private fun updateLiveState(pathType: PathType, isUnread: Boolean = false) {
        when (pathType) {
            PathType.TYPE_LOCAL_FILE -> {
                if (localState != isUnread) {
                    FileHandlerLive.unReadFileLocalStatus = isUnread
                    localState = isUnread
                }
            }

            PathType.TYPE_DOWNLOAD_FILE -> {
                if (downloadState != isUnread) {
                    FileHandlerLive.unReadFileDownloadStatus = isUnread
                    downloadState = isUnread
                }
            }

            PathType.TYPE_SHARE_FILE -> {
                if (czurShareState != isUnread) {
                    FileHandlerLive.unReadFileShareStatus = isUnread
                    czurShareState = isUnread
                }
            }

            PathType.TYPE_SCREEN_SHORT_FILE -> {
                if (screenShortState != isUnread) {
                    FileHandlerLive.unReadFileScreenStatus = isUnread
                    screenShortState = isUnread
                }

            }

            PathType.TYPE_MEETING_FILE -> {
                if (meetingState != isUnread) {
                    FileHandlerLive.unReadFileMeetingStatus = isUnread
                    meetingState = isUnread
                }
            }

            PathType.TYPE_AI_TRANS_FILE -> {
                if (aiTransState != isUnread) {
                    FileHandlerLive.unReadFileAITransStatus = isUnread
                    aiTransState = isUnread
                }
            }
        }
    }


    /**
     * 删除全部未读文件（文件已读）
     */
    private suspend fun clearUnreadFile(pathType: PathType) {
        withContext(Dispatchers.IO) {
            val list = getAllFileName() as MutableList
            val result = list.filter {
                it.pathType != pathType
            } as MutableList
            if (result.size == 0) {
                logTagD(TAG, "clearData=====")
                clearData()
                updateLiveState(pathType)
            } else {
                if (result == list) return@withContext
                val value = gson.toJson(result)
                setDSValue(value)
                updateLiveState(pathType)
            }

        }
    }

    /**
     * 读取最近一条未读文件名称
     */
    suspend fun getLastFileName() =
        withContext(Dispatchers.IO) {
            var unList = getAllFileName() as MutableList
            if (unList.isNotEmpty()) {
                unList.last().name
            } else {
                null
            }
        }

    /**
     * 读取所有未读文件
     */
    private suspend fun getAllFileName() = withContext(Dispatchers.IO) {
        val jsonStr = getDSValue("")
        if (jsonStr!!.isNotEmpty()) {
            val list = gson.fromJson<List<UnReadFileEntity>>(
                jsonStr,
                object : TypeToken<List<UnReadFileEntity>>() {}.type
            )
            list
        } else {
            mutableListOf()
        }
    }

    /**
     * 是否有该类型未读文件
     */
    private suspend fun getValueFromList(list: MutableList<UnReadFileEntity>, pathType: PathType) =
        withContext(Dispatchers.IO) {
            if (list.size > 0) {
                val result = list.filter {
                    it.pathType == pathType
                }
                result.isNotEmpty()
            } else {
                false
            }
        }


    private suspend fun getDSValue(defValue: String = ""): String {
        val dsKey = stringPreferencesKey(KEY_UNREAD_FILE)
        return remindDataStore.data.map {
            it[dsKey] ?: defValue
        }.first()
    }


    private suspend fun setDSValue(value: String = "") {
        withContext(Dispatchers.IO) {
            val stringKey = stringPreferencesKey(KEY_UNREAD_FILE)
            remindDataStore.edit { preferences ->
                preferences[stringKey] = value
            }
        }
    }

    /**
     * 清空数据
     */
    fun clearData() = runBlocking {
        remindDataStore.edit { it.clear() }
    }

    /**
     * 刷新红点未读
     */
    suspend fun refreshUnRead(showTypeKey: Int) = withContext(Dispatchers.IO) {
        logTagD(TAG, "==showTypeKey==$showTypeKey")
        when (showTypeKey) {
            MainTabKey.SHOW_TYPE_LOCAL -> clearUnreadFile(PathType.TYPE_LOCAL_FILE)
            MainTabKey.SHOW_TYPE_LOCAL_DOWNLOAD -> clearUnreadFile(PathType.TYPE_DOWNLOAD_FILE)
            MainTabKey.SHOW_TYPE_LOCAL_CZUR_SHARE -> clearUnreadFile(PathType.TYPE_SHARE_FILE)
            MainTabKey.SHOW_TYPE_LOCAL_SCREEN_SHOT -> clearUnreadFile(PathType.TYPE_SCREEN_SHORT_FILE)
            MainTabKey.SHOW_TYPE_LOCAL_MEETING -> clearUnreadFile(PathType.TYPE_MEETING_FILE)
            MainTabKey.SHOW_TYPE_NET_TRANS_RECORD -> clearUnreadFile(PathType.TYPE_AI_TRANS_FILE)
        }
    }

}