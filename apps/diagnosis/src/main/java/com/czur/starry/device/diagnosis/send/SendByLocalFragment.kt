package com.czur.starry.device.diagnosis.send

import android.widget.ProgressBar
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import com.czur.czurutils.log.logTagD
import com.czur.starry.device.baselib.utils.*
import com.czur.starry.device.baselib.view.floating.KeyBackFloatFragment
import com.czur.starry.device.diagnosis.R
import java.io.File

/**
 * Created by 陈丰尧 on 2022/8/1
 */
class SendByLocalFragment(
    private val zipFile: File
) : KeyBackFloatFragment(bgDark = true) {

    override fun getLayoutId(): Int = R.layout.fragment_send_by_local

    companion object {
        private const val TAG = "SendByLocalFragment"
    }

    private val serverInfoMap = mutableMapOf<String, LocalServerInfoEntity>()
    private val adapter = LocalServerAdapter()

    private val socketUtil by lazy {
        SocketUtil(lifecycleScope)
    }

    private val uploadUtil = UploadUtil()

    private val pcList: RecyclerView by lazy { requireView().findViewById(R.id.pcList) as RecyclerView }
    private val progressBar: ProgressBar by lazy { requireView().findViewById(R.id.progressBar) as ProgressBar }

    override fun initView() {
        super.initView()
        pcList.adapter = adapter

        pcList.doOnItemClick { vh, view ->
            val pos = vh.bindingAdapterPosition
            val entity = adapter.getData(pos)
            socketUtil.stopSearch()
            upload(entity)
            true
        }
    }

    /**
     * 传输文件
     */
    private fun upload(serverInfo: LocalServerInfoEntity) {
        launch {
            progressBar.show()
            val result = uploadUtil.transLogFile(zipFile, serverInfo)
            if (result) {
                toastSuccess()
                dismiss()
            } else {
                toast("发送失败, 重试或使用adb")
            }

            progressBar.gone()
        }
    }


    override fun initData() {
        super.initData()
        socketUtil.searchLocalServer {
            serverInfoMap[it.id] = it
            val dataList = serverInfoMap.values.toList()
            logTagD(TAG, "adapter:${dataList.size}")
            adapter.setData(dataList)
        }
    }

    override fun onDestroyView() {
        socketUtil.stopSearch()
        super.onDestroyView()
    }

}