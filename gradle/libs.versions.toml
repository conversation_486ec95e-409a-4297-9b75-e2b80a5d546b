[versions]
agp = "8.10.0"
compileSdkVersion = "35"
minSdkVersion = "31"
targetSdkVersion = "34"

appVersionCode = "5030033"
appVersionName = "V5.03.00.33"

kotlin = "2.1.10"    # https://github.com/JetBrains/kotlin
coreKtx = "1.16.0"      # https://developer.android.com/jetpack/androidx/releases/core?hl=zh-cn
coroutinesAndroid = "1.10.2"  # https://github.com/Kotlin/kotlinx.coroutines
kspVersion = "2.1.10-1.0.30"    # https://github.com/google/ksp 更新Kotlin版本去这个网址找对应ksp
appcompat = "1.7.0" # https://developer.android.com/jetpack/androidx/releases/appcompat?hl=zh-cn
activity = "1.10.1"     # https://developer.android.google.cn/jetpack/androidx/releases/activity?hl=en
fragment = "1.8.6"
navVersion = "2.8.9"    # https://developer.android.google.cn/jetpack/androidx/releases/navigation?hl=en
constraintlayout = "2.2.1"

lifecycleVersion = "2.8.7"
roomVersion = "2.6.1" # https://developer.android.com/jetpack/androidx/releases/room?hl=zh_cn
cameraX = "1.4.0"
emoji2 = "1.5.0"
media3 = "1.6.1"    # https://developer.android.google.cn/jetpack/androidx/releases/media3?hl=en

protoc-javalite = "4.26.1"

net-downloadVersion = "3.7.0" # 网络请求库版本: https://github.com/liangjingkanji/Net/?tab=readme-ov-file

fasterxml-jackson = "2.17.1" # xml解析库
dfuAndroid = "2.7.0" # 蓝牙DFU升级库版本: https://github.com/NordicSemiconductor/Android-DFU-Library
guavaAndroid = "33.3.1-android" # https://github.com/google/guava
kotlinxSerializationJson = "1.8.1" # https://github.com/Kotlin/kotlinx.serialization
annotationJvm = "1.9.1"
czurUtil = "1.8.4"

[libraries]
tinypinyin-core = "com.github.promeg:tinypinyin:2.0.3" #TinyPinyin核心包，约80KB
tinypinyin-lexicons-android-cncity = "com.github.promeg:tinypinyin-lexicons-android-cncity:2.0.3" # 可选，适用于Android的中国地区词典

# LifeCycle
lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycleVersion" }
lifecycle-viewmodel-ktx = { module = "androidx.lifecycle:lifecycle-viewmodel-ktx", version.ref = "lifecycleVersion" }
lifecycle-livedata-ktx = { module = "androidx.lifecycle:lifecycle-livedata-ktx", version.ref = "lifecycleVersion" }
lifecycle-service = { module = "androidx.lifecycle:lifecycle-service", version.ref = "lifecycleVersion" }

# kotlinToolLib
kotlin-stdlib = { module = "org.jetbrains.kotlin:kotlin-stdlib", version.ref = "kotlin" }
kotlin-reflect = { module = "org.jetbrains.kotlin:kotlin-reflect", version.ref = "kotlin" }

# 协程
kotlinx-coroutines-android = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-android", version.ref = "coroutinesAndroid" }

# 核心库
core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
activity = { module = "androidx.activity:activity", version.ref = "activity" }
activity-ktx = { group = "androidx.activity", name = "activity-ktx", version.ref = "activity" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
fragment-ktx = { module = "androidx.fragment:fragment-ktx", version.ref = "fragment" }
constraintlayout = { module = "androidx.constraintlayout:constraintlayout", version.ref = "constraintlayout" }

# UI库
flexbox = "com.google.android.flexbox:flexbox:3.0.0"
nooberBackground = "com.github.JavaNoober.BackgroundLibrary:libraryx:1.7.6"
viewpager2 = "androidx.viewpager2:viewpager2:1.1.0"
agentWeb = "io.github.justson:agentweb-core:v5.1.1-androidx" # webview封装库，优点：解决了webview一些bug，api调用也更方便，支持进度条 https://github.com/Justson/AgentWeb
paging = "androidx.paging:paging-runtime:3.3.4" # 分页
material = "com.google.android.material:material:1.12.0"
navigation-fragment-ktx = { module = "androidx.navigation:navigation-fragment-ktx", version.ref = "navVersion" }
navigation-ui-ktx = { module = "androidx.navigation:navigation-ui-ktx", version.ref = "navVersion" }
recyclerview = "androidx.recyclerview:recyclerview:1.4.0" # https://developer.android.com/jetpack/androidx/releases/recyclerview?hl=zh_cn
palette = "androidx.palette:palette-ktx:1.0.0" # https://developer.android.com/develop/ui/views/graphics/palette-colors

emoji2 = { module = "androidx.emoji2:emoji2", version.ref = "emoji2" }
emoji2-bundle = { module = "androidx.emoji2:emoji2-bundled", version.ref = "emoji2" }

#pdf
itextpdf = "com.itextpdf:itext7-core:7.2.5"
# markdown
commonmark = "org.commonmark:commonmark:0.19.0"

# 网络相关库
okhttp = "com.squareup.okhttp3:okhttp:4.12.0"
glide = "com.github.bumptech.glide:glide:4.16.0"
netty = "io.netty:netty-all:4.1.118.Final"
oss = "com.aliyun.dpa:oss-android-sdk:2.9.21"
net-download = { module = "com.github.liangjingkanji:Net", version.ref = "net-downloadVersion" }

# Utils
czurUtil = { group = "com.gitee.czur_dl", name = "czurutils", version.ref = "czurUtil" }
gson = "com.google.code.gson:gson:2.11.0"
kotlinx-serialization-json = { group = "org.jetbrains.kotlinx", name = "kotlinx-serialization-json", version.ref = "kotlinxSerializationJson" }
commons-codec = "commons-codec:commons-codec:1.17.0" # 用于编码MD5 base64等
guava = { module = "com.google.guava:guava", version.ref = "guavaAndroid" }
markwon = "io.noties.markwon:core:4.6.2" # markdown依赖

# 数据存储相关
datastore = "androidx.datastore:datastore-preferences:1.1.3"
disklrucache = "com.jakewharton:disklrucache:2.0.2" # 磁盘缓存
room-runtime = { module = "androidx.room:room-runtime", version.ref = "roomVersion" }
room-ktx = { module = "androidx.room:room-ktx", version.ref = "roomVersion" }
room-compiler = { module = "androidx.room:room-compiler", version.ref = "roomVersion" }

# 测试相关
junit = "junit:junit:4.13.2"
android-junit = "androidx.test.ext:junit:1.2.1"
espresso-core = "androidx.test.espresso:espresso-core:3.6.1"

# camera
camera-core = { module = "androidx.camera:camera-core", version.ref = "cameraX" }
camera-camera2 = { module = "androidx.camera:camera-camera2", version.ref = "cameraX" }
camera-lifecycle = { module = "androidx.camera:camera-lifecycle", version.ref = "cameraX" }
camera-view = { module = "androidx.camera:camera-view", version.ref = "cameraX" }

# 蓝牙相关
dfu = { module = "no.nordicsemi.android:dfu", version.ref = "dfuAndroid" }
fastble = "com.github.Jasonchenlijian:FastBle:2.4.0" # 蓝牙库 用来进入DFU模式

google-protobuf-javaLite = { module = "com.google.protobuf:protobuf-javalite", version.ref = "protoc-javalite" }
fastxml-jackson = { module = "com.fasterxml.jackson.module:jackson-module-kotlin", version.ref = "fasterxml-jackson" }

# media3
media3-ui = { group = "androidx.media3", name = "media3-ui", version.ref = "media3" }
media3-exoplayer = { group = "androidx.media3", name = "media3-exoplayer", version.ref = "media3" }
media3-common = { group = "androidx.media3", name = "media3-common", version.ref = "media3" }
media3-exoplayer-dash = { group = "androidx.media3", name = "media3-exoplayer-dash", version.ref = "media3" }
media3-exoplayer-hls = { group = "androidx.media3", name = "media3-exoplayer-hls", version.ref = "media3" }
media3-exoplayer-rtsp = { group = "androidx.media3", name = "media3-exoplayer-rtsp", version.ref = "media3" }
media3-session = { group = "androidx.media3", name = "media3-session", version.ref = "media3" }
media3-decoder = { group = "androidx.media3", name = "media3-decoder", version.ref = "media3" }
media3-container = { group = "androidx.media3", name = "media3-container", version.ref = "media3" }
media3-datasource = { group = "androidx.media3", name = "media3-datasource", version.ref = "media3" }
media3-transformer = { group = "androidx.media3", name = "media3-transformer", version.ref = "media3" }
media3-effect = { group = "androidx.media3", name = "media3-effect", version.ref = "media3" }
media3-muxer = { group = "androidx.media3", name = "media3-muxer", version.ref = "media3" }
androidx-annotation-jvm = { group = "androidx.annotation", name = "annotation-jvm", version.ref = "annotationJvm" }
[bundles]
# 拼音库
tinypinyin = ["tinypinyin-core", "tinypinyin-lexicons-android-cncity"]
lifecycle = ["lifecycle-runtime-ktx", "lifecycle-viewmodel-ktx", "lifecycle-livedata-ktx", "lifecycle-service"]
androidCore = ["core-ktx", "activity", "activity-ktx", "androidx-appcompat", "fragment-ktx", "constraintlayout", "emoji2", "emoji2-bundle"]
kotlinStdLib = ["kotlin-stdlib", "kotlin-reflect"]
toolKit = ["czurUtil", "gson", "commons-codec", "guava", "kotlinx-serialization-json", ]
androidTest = ["android-junit", "espresso-core"]
room = ["room-runtime", "room-ktx"]
navigation = ["navigation-fragment-ktx", "navigation-ui-ktx"]
cameraX = ["camera-core", "camera-camera2", "camera-lifecycle", "camera-view"]
dfu = ["dfu"]
media3 = ["media3-ui", "media3-exoplayer", "media3-common", "media3-exoplayer-hls", "media3-exoplayer-rtsp", "media3-decoder", "media3-container", "media3-datasource", "media3-session", "media3-exoplayer-dash", "media3-transformer", "media3-effect", "media3-muxer"]

[plugins]
application = { id = "com.android.application", version.ref = "agp" }
library = { id = "com.android.library", version.ref = "agp" }
kotlinAndroid = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlinNoarg = { id = "org.jetbrains.kotlin.plugin.noarg", version.ref = "kotlin" }
navigationSafeargs = { id = 'androidx.navigation.safeargs', version.ref = "navVersion" }
ksp = { id = "com.google.devtools.ksp", version.ref = "kspVersion" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
