package com.czur.starry.device.baselib.utils;

import androidx.annotation.NonNull;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.Observer;

import kotlinx.coroutines.flow.MutableStateFlow;

/**
 * Created by 陈丰尧 on 2022/4/27
 */
public class FlowMediatorLiveData<T> extends MediatorLiveData<T> {
    private MutableStateFlow<Long> flow;

    public FlowMediatorLiveData(MutableStateFlow<Long> updateFlow) {
        this.flow = updateFlow;
    }

    public void addSource(LiveData<?>... sources) {
        for (LiveData<?> source : sources) {
            Observer onChanged = s -> flow.setValue(System.currentTimeMillis());
            addSource(source, onChanged);
        }
    }

}
