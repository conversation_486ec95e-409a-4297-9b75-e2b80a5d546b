package com.czur.starry.device.baselib.utils.fw.proxy

import android.audioai.AudioAiServiceManager
import android.content.Intent
import com.czur.czurutils.global.globalAppCtx
import com.czur.czurutils.log.logTagE
import com.czur.czurutils.log.logTagI
import com.czur.starry.device.baselib.common.BootParam.ACTION_BOOT_VOICE_ASSISTANT

/**
 * Created by 陈丰尧 on 2025/5/9
 */
private const val TAG = "AudioAIManagerProxy"

class AudioAIManagerProxy {
    private val audioAiServiceManager: AudioAiServiceManager by lazy(LazyThreadSafetyMode.NONE) {
        (globalAppCtx.getSystemService("audioai") as AudioAiServiceManager)
    }

    /**
     * 获取当前的语音助手状态
     */
    fun getInteractionStageStatus(): Boolean {
        return try {
            audioAiServiceManager.interactionStageStatus != 0
        } catch (tr: Throwable) {
            logTagE(TAG, "getInteractionStageStatus error", tr = tr)
            false
        }
    }

    /**
     * 设置语音助手状态
     */
    fun setInteractionStageStatus(status: Boolean) {
        try {
            logTagI(TAG, "setInteractionStageStatus $status")
            audioAiServiceManager.interactionKwsStatus(if (status) 1 else 0)
        } catch (tr: Throwable) {
            logTagE(TAG, "setInteractionStageStatus error", tr = tr)
        }
    }

    /**
     * 获取当前的语音助手总开关状态
     */
    fun getInteractionStatus(): Boolean {
        return try {
            audioAiServiceManager.interactionStatus != 0
        } catch (tr: Throwable) {
            logTagE(TAG, "getInteractionStatus error", tr = tr)
            false
        }
    }

    /**
     * 设置语音助手总开关
     */
    fun setInteractionStatus(status: Boolean) {
        try {
            logTagI(TAG, "setInteractionStatus $status")
            audioAiServiceManager.interactionStatus(if (status) 1 else 0)
            noticeAIServiceEvent()
        } catch (tr: Throwable) {
            logTagE(TAG, "setInteractionStatus error", tr = tr)
        }
    }

    // 通知小星处理事件
    private fun noticeAIServiceEvent() {
        val intent = Intent().apply {
            `package` = "com.czur.starry.device.voiceassistant"
            action = ACTION_BOOT_VOICE_ASSISTANT
            putExtra("key_message", "wake_up_switch")
        }
        globalAppCtx.startService(intent)
    }

    /**
     * 获取Tts播报状态
     */
    fun getTtsStatus(): Boolean {
        return try {
            audioAiServiceManager.ttsStatus != 1
        } catch (tr: Throwable) {
            logTagE(TAG, "getTtsStatus error", tr = tr)
            false
        }
    }

    /**
     * 设置Tts播报状态
     */
    fun setTtsStatus(status: Boolean) {
        try {
            logTagI(TAG, "setTtsStatus $status")
            audioAiServiceManager.ttsStatus = if (status) 0 else 1
        } catch (tr: Throwable) {
            logTagE(TAG, "setTtsStatus error", tr = tr)
        }
    }
}