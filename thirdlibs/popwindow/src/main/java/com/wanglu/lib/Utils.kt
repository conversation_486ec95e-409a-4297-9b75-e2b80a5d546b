package com.wanglu.lib

import android.content.Context
import android.util.DisplayMetrics
import android.view.WindowManager


internal object Utils {

    /**
     * 获取屏幕大小 [0] 宽 [1] 高
     */
    fun getScreenSize(context: Context): IntArray {
        val wm = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val outMetrics = DisplayMetrics()
        wm.defaultDisplay.getMetrics(outMetrics)
        return intArrayOf(outMetrics.widthPixels, outMetrics.heightPixels)
    }


    /**
     * 获取窗口的大小
     */
    fun getWindowSize(activity: Context): IntArray {
        val result = IntArray(2)
        val screenSize = getScreenSize(activity)
        result[0] = screenSize[0]
//        result[1] = if (checkDeviceHasNavigationBar(activity)) screenSize[1] - getNavigationBarHeight(activity) else screenSize[1]
        result[1] = screenSize[1]
        return result
    }



    fun dp2px(context: Context, dipValue: Int): Int {
        // 在Starry上, 使用PX作为单位
//        return TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP,
//                dipValue.toFloat(), context.resources.displayMetrics).toInt()
        return dipValue
    }

}